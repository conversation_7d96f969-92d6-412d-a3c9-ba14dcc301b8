package com.mpolicy.settlement.core.client;

import com.mpolicy.common.result.Result;
import com.mpolicy.settlement.core.common.protocol.CheckOrgListIsMateInput;
import com.mpolicy.settlement.core.common.protocol.CheckOrgListIsMateOut;
import com.mpolicy.settlement.core.common.protocol.ProductOrgListOut;
import com.mpolicy.settlement.core.common.protocol.ProductOrgListVo;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 结算系统- 保司对账单feign服务
 *
 * <AUTHOR>
 * @since 2023/05/20
 */
@FeignClient(name = "settlement-center-core", url = "${ims.feign.settlement-center-core:}",
    configuration = SettlementCoreFeignConfiguration.class, fallbackFactory = SettlementProtocolFallback.class)
@Configuration
public interface SettlementProtocolClient {

    /**
     * 校验代理的机构信息和险种编码是否可以正常匹配
     *
     * @param orgCode       代理人机构编码
     * @param effectiveDate 协议生效时间 yyyy-MM-dd
     * @param productCode   小鲸险种编码
     * @return
     */
    @GetMapping("/settlement/protocol/checkOrgCodeAndProductCodeIsMate")
    Result checkOrgCodeAndProductCodeIsMate(
        @RequestParam(value = "orgCode") @ApiParam(name = "orgCode", value = "机构编码",
            example = "F001") String orgCode,
        @RequestParam(value = "effectiveDate") @ApiParam(name = "effectiveDate", value = "生效时间",
            example = "F001") String effectiveDate,
        @RequestParam(value = "productCode") @ApiParam(name = "productCode", value = "险种编码",
            example = "zhangsan") String productCode);

    @PostMapping("/settlement/protocol/checkOrgListIsMate")
    Result<List<CheckOrgListIsMateOut>> checkOrgListIsMate(@RequestBody(required = false) List<CheckOrgListIsMateInput> inputList);

    /**
     * 根据险种编码 + 时间 获取适用保司信息
     *
     * @param params 请求参数
     * @return
     */
    @PostMapping("/settlement/protocol/findProductOrgList")
    Result<List<ProductOrgListOut>> findProductOrgList(@RequestBody @Valid ProductOrgListVo params);

}
