package com.mpolicy.settlement.core.client;

import com.alibaba.fastjson.JSON;
import com.mpolicy.common.result.Result;
import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.settlement.core.common.autocost.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 结算系统- 保司对账单feign服务
 *
 * <AUTHOR>
 * @since 2023/05/20
 */
@FeignClient(name = "settlement-center-core",
        url = "${ims.feign.settlement-center-core:}",
        configuration = SettlementCoreFeignConfiguration.class,
        fallbackFactory = SettlementAutoCostFallback.class)
@Configuration
public interface SettlementAutoCostClient {


    /**
     * 文件导入记录查询
     * @param query
     * @return
     */
    @ApiOperation(value = "文件导入记录查询", notes = "文件导入记录查询")
    @PostMapping("/settlement/auto_cost/import_record/pageSettlementCostImportRecord")
    Result<PageUtils<SettlementCostImportRecord>> pageSettlementCostImportRecord(@RequestBody @ApiParam(name = "query", value = "文件导入记录查询", required = true) SettlementCostImportRecordQuery query);
    /**
     * 文件导入操作记录查询
     * @param query
     * @return
     */
    @ApiOperation(value = "文件导入操作记录查询", notes = "文件导入操作记录查询")
    @PostMapping("/settlement/auto_cost/import_record/pageSettlementCostImportOperationRecord")
    Result<PageUtils<SettlementCostImportOperationRecord>> pageSettlementCostImportOperationRecord(@RequestBody @ApiParam(name = "query", value = "文件导入记录查询", required = true) SettlementCostImportOperationRecordQuery query);

    @ApiOperation(value = "结算文件记录删除", notes = "结算文件记录删除")
    @PostMapping("/settlement/auto_cost/import_record/deleteImportRecord")
    Result<String> deleteImportRecord(@RequestBody @ApiParam(name = "input", value = "结算文件记录删除", required = true) SettlementCostImportRecordDelete input);

    @ApiOperation(value = "结算文件导入异常明细列表", notes = "结算文件导入异常明细列表")
    @GetMapping("/settlement/auto_cost/import_record/listImportErrorDataByApplyCode")
    Result<List<SettlementCostImportErrorData>> listImportErrorDataByApplyCode(@RequestParam(value = "applyCode") @ApiParam(name = "applyCode", value = "结算文件导入申请编码", example = "F001") String applyCode);
    /**
     * 动态科目申请
     *
     * @param apply 动态科目文件申请信息
     * @return 动态科目申请编码
     * <AUTHOR>
     * @since 2024/1/29
     */
    @PostMapping("/settlement/auto_cost/dynamic_subject/upload")
    Result<String> uploadSubjectDataDynamicApply(@RequestBody @ApiParam(name = "apply", value = "动态科目文件申请信息") SubjectDataDynamicApply apply);

    /**
     * 装载动态科目数据信息
     *
     * @param applyCode 动态科目申请编码
     * @param userName  操作员
     * @return 处理结果
     * <AUTHOR>
     * @since 2024/1/29
     */
    @PostMapping("/settlement/auto_cost/dynamic_subject/load")
    Result<String> loadDynamicSubject(@RequestParam(value = "applyCode") @ApiParam(name = "applyCode", value = "动态科目申请编码", example = "F001") String applyCode,
                                      @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "张三") String userName);

    /**
     * 方案计算确认
     *
     * @param programmeCode 结算方案编码
     * @param costSettlementCycle 结算方案编码
     * @param confirmUser 确认操作人
     * @return com.mpolicy.common.result.Result<java.lang.String>
     */
    @PostMapping("/settlement/auto_cost/calculate/confirmCostProgramme")
    Result<String> confirmCostProgramme(@RequestParam(value = "programmeCode") @ApiParam(name = "programmeCode", value = "结算方案编码", example = "F001") String programmeCode,
                                               @RequestParam(value = "costSettlementCycle") @ApiParam(name = "costSettlementCycle", value = "结算周期", example = "202309") String costSettlementCycle,
                                               @RequestParam(value = "confirmUser") @ApiParam(name = "confirmUser", value = "确认操作人", example = "202309") String confirmUser);


    /**
     * 结算文件导入申请
     *
     * @param apply 结算文件导入申请信息
     * @return 申请编码
     * <AUTHOR>
     * @since 2024/9/29
     */
    @PostMapping("/settlement/auto_cost/history_long_cost/upload")
    Result<String> uploadHistoryLongCostApply(@RequestBody @ApiParam(name = "apply", value = "动态科目文件申请信息") SettlementHistoryCostUploadApply apply);

    /**
     * 装载结算文件导入数据信息
     *
     * @param applyCode 结算文件导入申请编码
     * @param userName  操作员
     * @return 处理结果
     * <AUTHOR>
     * @since 2024/9/29
     */
    @PostMapping("/settlement/auto_cost/history_long_cost/loadCost")
    Result<String> loadCost(@RequestParam(value = "applyCode") @ApiParam(name = "applyCode", value = "结算文件导入申请编码", example = "F001") String applyCode,
                                      @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "张三") String userName);

    @ApiOperation(value = "根据动态科目名称模糊查询科目定义信息")
    @GetMapping("/settlement/auto_cost/dynamic_subject/listSettlementDynamicSubject")
    Result<List<SettlementDynamicSubjectDefinition>> listSettlementDynamicSubject(@RequestParam(value = "dynamicSubjectName") @ApiParam(name = "dynamicSubjectName", value = "动态科目名称模糊信息", example = "F001") String dynamicSubjectName);

    @ApiOperation(value = "存储动态科目定义信息")
    @PostMapping("/settlement/auto_cost/dynamic_subject/saveSettlementDynamicSubjectDefinition")
    Result<String> saveSettlementDynamicSubjectDefinition(@RequestBody SettlementDynamicSubjectDefinition definition);


    @ApiOperation(value = "主任寻人规则不匹配日志记录", notes = "主任寻人规则不匹配日志记录")
    @PostMapping("/settlement/auto_cost/branchDirector/notMatchLog")
    Result<PageUtils<SettlementDirectorNotMatchRetDto>> pageNotMatchLog(@RequestBody @ApiParam(name = "query", value = "文件导入记录查询", required = true) SettlementDirectorNotMatchLogQuery query);

    @ApiOperation(value = "结算生服对接人导出信息", notes = "结算生服对接人导出信息")
    @PostMapping("/settlement/auto_cost/pco/listSettlementPcoInfo")
    Result<List<SettlementPcoInfoExpertDto>> listSettlementPcoInfo(@RequestParam(value = "cycle") @ApiParam(name = "cycle", value = "导出周期", required = true) String cycle);

}
