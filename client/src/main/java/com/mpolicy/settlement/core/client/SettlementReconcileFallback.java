package com.mpolicy.settlement.core.client;

import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.settlement.core.common.commission.dto.IncomeProfitInfoParamsDto;
import com.mpolicy.settlement.core.common.commission.dto.IncomeRateOut;
import com.mpolicy.settlement.core.common.commission.dto.ValidateCostConfigParam;
import com.mpolicy.settlement.core.common.reconcile.*;
import com.mpolicy.settlement.core.common.reconcile.diff.DiffBacklogInput;
import com.mpolicy.settlement.core.common.reconcile.diff.ReconcileAmountAccuracyInput;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 结算系统-保司对账单 Fallback
 *
 * <AUTHOR>
 * @since 2023/05/20
 */
@Component
@Slf4j
public class SettlementReconcileFallback implements FallbackFactory<SettlementReconcileClient> {

    @Override
    public SettlementReconcileClient create(Throwable cause) {
        return new SettlementReconcileClient() {

            @Override
            public Result<String> uploadReconcileFile(String reconcileCode, String reconcileTemplateCode, String fileCode, String userName) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]", Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<String> retryUploadReconcileFile(String reconcileCode, String reconcileTemplateCode, String sourceFileCode, String fileCode, String userName) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]", Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<String> removeReconcileFile(String reconcileCode, String reconcileFileCode, String userName) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]", Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<String> startReconcile(String reconcileCode, String userName) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]", Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<String> reconcileAmountAccuracy(ReconcileAmountAccuracyInput reconcileAmountAccuracyInput) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]", Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<String> retryStartReconcile(String reconcileCode, String userName) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]", Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<String> finishReconcile(String reconcileCode, String userName) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]", Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<String> diffBacklog(DiffBacklogInput diffBacklogInput) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]", Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<String> closeReconcile(String reconcileCode, String userName) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]", Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<String> reconcileRefreshPolicy(String reconcileCode, String userName) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]", Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<String> handleSettlementPolicyInfo(HandleSettlementPolicyInfoVo params) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]", Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<String> refreshSettlementPolicyInfoCommission(RefreshSettlementPolicyInfoCommissionVo params) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]", Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<String> refreshSettlementEventJob(RefreshSettlementEventJobVo params) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]", Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<String> batchRefreshRate(String reconcileCode) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]", Thread.currentThread().getStackTrace()[1].getMethodName(), cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<String> forceCreateReconcile(CreateReconcileVo params) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]请求参数={}", Thread.currentThread().getStackTrace()[1].getMethodName(), params, cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<String> reconcileRegulatorySubmit(String reconcileCode) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]请求参数={}", Thread.currentThread().getStackTrace()[1].getMethodName(), reconcileCode, cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            /**
             *  查询保单是否存在完成对账的记录
             * @param policyNo 保单号
             * @return true 已完成 false 未完成
             */
            @Override
            public Result<Boolean> isCompletedReconcileRecord(String policyNo) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]请求参数={}", Thread.currentThread().getStackTrace()[1].getMethodName(), policyNo, cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<List<BatchIsCompletedReconcileRecord>> batchIsCompletedReconcileRecord(List<String> policyNoList) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]请求参数={}", Thread.currentThread().getStackTrace()[1].getMethodName(), policyNoList, cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<List<BatchIsCompletedReconcileRecordPreservation>> batchIsCompletedReconcileRecordPreservation(List<String> preservationCodeList) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]请求参数={}", Thread.currentThread().getStackTrace()[1].getMethodName(), preservationCodeList, cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<String> handleSettlementEventJob(List<String> codeList) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]请求参数={}", Thread.currentThread().getStackTrace()[1].getMethodName(), codeList, cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<String> rectificationOne(RectificationOneVo vo) {
                log.warn("调用[结算系统-保司对账单服务-{}]出现异常]请求参数={}", Thread.currentThread().getStackTrace()[1].getMethodName(), vo, cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-保司对账单]异常"), cause);
            }

            @Override
            public Result<List<IncomeRateOut>> queryIncomeReconcileRate(IncomeProfitInfoParamsDto profitInfoParamsDto) {
                log.warn("调用[结算系统-费率查询服务-{}]出现异常]请求参数={}", Thread.currentThread().getStackTrace()[1].getMethodName(), profitInfoParamsDto, cause);
                return Result.error(BasicCodeMsg.RESOURCE_ERROR.setMsg("调用[结算系统-费率查询]异常"), cause);
            }
        };
    }
}
