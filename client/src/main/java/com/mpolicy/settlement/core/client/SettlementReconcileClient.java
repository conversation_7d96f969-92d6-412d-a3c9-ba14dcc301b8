package com.mpolicy.settlement.core.client;

import com.mpolicy.common.result.Result;
import com.mpolicy.settlement.core.common.commission.dto.IncomeProfitInfoParamsDto;
import com.mpolicy.settlement.core.common.commission.dto.IncomeRateOut;
import com.mpolicy.settlement.core.common.commission.dto.ValidateCostConfigParam;
import com.mpolicy.settlement.core.common.reconcile.*;
import com.mpolicy.settlement.core.common.reconcile.diff.DiffBacklogInput;
import com.mpolicy.settlement.core.common.reconcile.diff.ReconcileAmountAccuracyInput;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 结算系统- 保司对账单feign服务
 *
 * <AUTHOR>
 * @since 2023/05/20
 */
@FeignClient(name = "settlement-center-core",
        url = "${ims.feign.settlement-center-core:}",
        configuration = SettlementCoreFeignConfiguration.class,
        fallbackFactory = SettlementReconcileFallback.class)
@Configuration
public interface SettlementReconcileClient {

    /**
     * 上传保司对账单文件
     *
     * @param reconcileCode         对账单唯一编号
     * @param reconcileTemplateCode 对账单模版编码
     * @param fileCode              文件编码
     * @param userName              操作人
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/22 23:49
     */
    @PostMapping("/settlement/reconcile/file/upload/{reconcileCode}")
    Result<String> uploadReconcileFile(@PathVariable @ApiParam(name = "reconcileCode", value = "小鲸对账单号") String reconcileCode,
                                       @RequestParam(value = "reconcileTemplateCode") @ApiParam(name = "reconcileTemplateCode", value = "文件规则类型编码", example = "F001") String reconcileTemplateCode,
                                       @RequestParam(value = "fileCode") @ApiParam(name = "fileCode", value = "保司对账单文件编码", example = "F001") String fileCode,
                                       @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "zhangsan") String userName);

    /**
     * 重新上传保司对账单文件
     *
     * @param reconcileCode         对账单唯一编号
     * @param reconcileTemplateCode 对账单模版编码
     * @param sourceFileCode        原文件编码
     * @param fileCode              文件编码
     * @param userName              操作人
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/22 23:50
     */
    @PostMapping("/settlement/reconcile/file/retry_upload/{reconcileCode}")
    Result<String> retryUploadReconcileFile(@PathVariable @ApiParam(name = "reconcileCode", value = "小鲸对账单号") String reconcileCode,
                                            @RequestParam(value = "reconcileTemplateCode") @ApiParam(name = "reconcileTemplateCode", value = "文件规则类型编码", example = "F001") String reconcileTemplateCode,
                                            @RequestParam(value = "sourceFileCode") @ApiParam(name = "sourceFileCode", value = "保司对账单文件编码", example = "F001") String sourceFileCode,
                                            @RequestParam(value = "fileCode") @ApiParam(name = "fileCode", value = "保司对账单文件编码", example = "F001") String fileCode,
                                            @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "zhangsan") String userName);

    /**
     * 删除对账关联对账文件
     *
     * @param reconcileCode     对账单唯一编号
     * @param reconcileFileCode 文件编码
     * @param userName          操作人
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/22 23:51
     */
    @PostMapping("/settlement/reconcile/file/remove/{reconcileCode}/{reconcileFileCode}")
    Result<String> removeReconcileFile(@PathVariable @ApiParam(name = "reconcileCode", value = "小鲸对账单号") String reconcileCode,
                                       @PathVariable @ApiParam(name = "reconcileFileCode", value = "小鲸对账单号") String reconcileFileCode,
                                       @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "zhangsan") String userName);

    /**
     * 开始对账
     *
     * @param reconcileCode 对账单唯一编号
     * @param userName      操作员
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/24 21:22
     */
    @PostMapping("/settlement/reconcile/start/{reconcileCode}")
    Result<String> startReconcile(@PathVariable @ApiParam(name = "reconcileCode", value = "小鲸对账单号") String reconcileCode,
                                  @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "zhangsan") String userName);

    /**
     * 重新对账
     *
     * @param reconcileCode 对账单唯一编号
     * @param userName      操作员
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/24 21:22
     */
    @PostMapping("/settlement/reconcile/retry_start/{reconcileCode}")
    Result<String> retryStartReconcile(@PathVariable @ApiParam(name = "reconcileCode", value = "小鲸对账单号") String reconcileCode,
                                       @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "zhangsan") String userName);

    /**
     * 完成对账
     *
     * @param reconcileCode 对账单唯一编号
     * @param userName      操作员
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/24 21:23
     */
    @PostMapping("/settlement/reconcile/finish/{reconcileCode}")
    Result<String> finishReconcile(@PathVariable @ApiParam(name = "reconcileCode", value = "小鲸对账单号") String reconcileCode,
                                   @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "zhangsan") String userName);

    /**
     * 批量精度处理
     *
     * @param reconcileAmountAccuracyInput 批量处理对账单精度信息
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/25 17:16
     */
    @PostMapping("/settlement/reconcile/reconcile_amount_accuracy/action")
    Result<String> reconcileAmountAccuracy(@RequestBody @ApiParam(name = "reconcileAmountAccuracyInput", value = "批量处理对账单精度信息") ReconcileAmountAccuracyInput reconcileAmountAccuracyInput);

    /**
     * 差异处理申请
     *
     * @param diffBacklogInput 差异处理申请对象
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/24 22:01
     */
    @PostMapping("/settlement/reconcile/diff_backlog")
    Result<String> diffBacklog(@RequestBody @ApiParam(name = "diffBacklogInput", value = "差异处理申请对象") DiffBacklogInput diffBacklogInput);

    /**
     * 关闭对账
     *
     * @param reconcileCode 对账单唯一编号
     * @param userName      操作员
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/24 21:23
     */
    @PostMapping("/settlement/reconcile/close/{reconcileCode}")
    Result<String> closeReconcile(@PathVariable @ApiParam(name = "reconcileCode", value = "小鲸对账单号") String reconcileCode,
                                  @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "zhangsan") String userName);

    /**
     * 刷新对账单保单数据
     *
     * @param reconcileCode 对账单唯一编号
     * @param userName      操作员
     * @return com.mpolicy.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @since 2023/5/24 21:23
     */
    @PostMapping("/settlement/reconcile/refresh_policy/{reconcileCode}")
    Result<String> reconcileRefreshPolicy(@PathVariable @ApiParam(name = "reconcileCode", value = "小鲸对账单号") String reconcileCode,
                                          @RequestParam(value = "userName") @ApiParam(name = "userName", value = "操作员", example = "zhangsan") String userName);


    /**
     * 重置结算单状态 已经存在 对账编码的不处理
     *
     * @param params 刷新条件
     * @return
     */
    @PostMapping("/settlement/reconcile/handleSettlementPolicyInfo")
    Result<String> handleSettlementPolicyInfo(@RequestBody @Valid HandleSettlementPolicyInfoVo params);


    /**
     * 更新费率信息
     *
     * @param params
     * @return
     */
    @PostMapping("/settlement/reconcile/refreshSettlementPolicyInfoCommission")
    Result<String> refreshSettlementPolicyInfoCommission(@RequestBody @Valid RefreshSettlementPolicyInfoCommissionVo params);

    /**
     * 刷新结算任务
     *
     * @param params
     * @return
     */
    @PostMapping("/settlement/reconcile/refreshSettlementEventJob")
    Result<String> refreshSettlementEventJob(@RequestBody @Valid RefreshSettlementEventJobVo params);

    /**
     * 批量刷新结算单费率信息
     *
     * @param reconcileCode 小鲸对账单号
     * @return
     */
    @PostMapping("/settlement/reconcile/batchRefreshRate/{reconcileCode}")
    Result<String> batchRefreshRate(@PathVariable @ApiParam(name = "reconcileCode", value = "小鲸对账单号") String reconcileCode);

    /**
     * 创建对账单
     *
     * @param params 小鲸对账单号
     * @return
     */
    @PostMapping("/settlement/reconcile/forceCreateReconcile")
    Result<String> forceCreateReconcile(@RequestBody @Valid CreateReconcileVo params);

    /**
     * 对账单报送
     *
     * @param reconcileCode 对账单编码
     * @return
     */
    @PostMapping("/settlement/reconcile/regulatory/submit/{reconcileCode}")
    Result<String> reconcileRegulatorySubmit(@PathVariable("reconcileCode") String reconcileCode);

    /**
     * 查询保单是否存在完成对账的记录
     *
     * @param policyNo 保单号
     * @return 返回是否完成过对账 true存在 false不存在
     */
    @GetMapping("/settlement/reconcile/isCompletedReconcileRecord/{policyNo}")
    Result<Boolean> isCompletedReconcileRecord(@PathVariable("policyNo") String policyNo);

    @PostMapping("/settlement/reconcile/batchIsCompletedReconcileRecord")
    Result<List<BatchIsCompletedReconcileRecord>> batchIsCompletedReconcileRecord(@RequestBody List<String> policyNoList);

    @PostMapping("/settlement/reconcile/batchIsCompletedReconcileRecordPreservation")
    Result<List<BatchIsCompletedReconcileRecordPreservation>> batchIsCompletedReconcileRecordPreservation(@RequestBody List<String> preservationCodeList);

    @PostMapping("/settlement/reconcile/handleSettlementEventJob")
    Result<String> handleSettlementEventJob(@RequestBody @NotEmpty(message = "事件编码不能为空") List<String> codeList);

    @PostMapping("/settlement/reconcile/rectificationOne")
    Result<String> rectificationOne(@RequestBody @Valid RectificationOneVo vo);

    @PostMapping("/settlement/reconcile/query/rate")
    Result<List<IncomeRateOut>> queryIncomeReconcileRate(@NotEmpty(message = "参数不能为空") @RequestBody IncomeProfitInfoParamsDto profitInfoParamsDto);

}
