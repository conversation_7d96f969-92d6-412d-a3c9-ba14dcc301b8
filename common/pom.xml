<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <artifactId>mpolicy-settlement-center-core-common</artifactId>
    <version>${project.settlement.core.common.version}</version>
    <packaging>jar</packaging>
    <name>mpolicy-settlement-center-core-common</name>
    <description>mpolicy-settlement-center-core-common</description>
    <parent>
        <groupId>com.mpolicy</groupId>
        <artifactId>mpolicy-settlement-center-core</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <properties>
        <project.settlement.core.common.version>1.0.47-SNAPSHOT</project.settlement.core.common.version>
        <mpolicy.result.version>2.0.2</mpolicy.result.version>
    </properties>
    <profiles>
        <profile>
            <id>pro</id>
            <properties>
                <project.settlement.core.common.version>1.0.47</project.settlement.core.common.version>
            </properties>
        </profile>
    </profiles>
    <dependencies>
        <!-- 基础模块 -->
        <dependency>
            <groupId>com.mpolicy</groupId>
            <artifactId>mpolicy-common-result</artifactId>
            <version>${mpolicy.result.version}</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <!--引入JWT依赖,由于是基于Java，所以需要的是java-jwt-->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
        </dependency>
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
        </dependency>
        <!--swagger2-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>