package com.mpolicy.settlement.core.common.reconcile.diff;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 差异申请
 *
 * <AUTHOR>
 * @since 2023-05-24 21:43
 */
@Data
@ApiModel(value = "差异处理请求对象", description = "差异处理请求对象")
public class DiffBacklogInput implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 对账唯一单号
     */
    @ApiModelProperty(value = "对账唯一单号", example = "RC2023052302020200")
    @NotBlank(message = "对账唯一单号不能为空")
    private String reconcileCode;

    /**
     * 对账保单业务汇总编号
     */
    @ApiModelProperty(value = "对账保单业务汇总编号", example = "RC2023052302020200")
    @NotEmpty(message = "对账保单业务汇总编号不能为空")
    private List<String> billCodeList;

    /**
     * 差异原因
     */
    @ApiModelProperty(value = "差异原因", example = "RC2023052302020200")
    @NotBlank(message = "差异原因不能为空")
    private String diffWhy;

    /**
     * 是否指定待办 0 否 1 是
     */
    @ApiModelProperty(value = "是否指定待办0 否 1 是", example = "1")
    @NotNull(message = "是否指定待办不能为空")
    private Integer assignAcceptUser;

    /**
     * 受理用户编号
     */
    private String acceptUserCode;

    /**
     * 受理用户名称
     */
    private String acceptUserName;

    /**
     * 差异处理说明
     */
    @ApiModelProperty(value = "差异处理说明", example = "差异处理说明")
    @NotBlank(message = "差异处理说明不能为空")
    private String diffDesc;

    /**
     * 差异处理说明
     */
    @ApiModelProperty(value = "操作员", example = "张三")
    @NotBlank(message = "差异操作员不能为空")
    private String opeUserName;

    /**
     * 操作类型0 以保司为准 1 以小鲸为准
     */
    @ApiModelProperty(value = "操作类型0 以保司为准 1 以小鲸为准", example = "1")
    @NotNull(message = "操作类型")
    private Integer opeType;
}