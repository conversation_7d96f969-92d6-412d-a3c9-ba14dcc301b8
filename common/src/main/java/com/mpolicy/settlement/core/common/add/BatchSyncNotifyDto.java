package com.mpolicy.settlement.core.common.add;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class BatchSyncNotifyDto {
    /**
     * 交易流水号
     */
    @ApiModelProperty(value = "交易流水号")
    private String requestId;
    /**
     * 活动编码
     */
    @ApiModelProperty(value = "活动编码")
    private Integer activityId;
    /**
     *结算批次
     */
    @ApiModelProperty(value = "结算批次")
    private List<String> batchNo;

    /**
     * 结算状态：0核算中，1待结算，2已结算，3作废
     */
    @ApiModelProperty(value = "结算状态：0核算中，1待结算，2已结算，3作废")
    private Integer settlementState;
}
