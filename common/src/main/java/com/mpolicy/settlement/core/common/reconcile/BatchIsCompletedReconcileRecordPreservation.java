package com.mpolicy.settlement.core.common.reconcile;

import lombok.Data;

import java.io.Serializable;

@Data
public class BatchIsCompletedReconcileRecordPreservation implements Serializable {
    private static final long serialVersionUID = -1541426485537127549L;

    /**
     * 保全编码
     */
    private String preservationCode;
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 是否完成对账
     */
    private Boolean isCompleted;
}
