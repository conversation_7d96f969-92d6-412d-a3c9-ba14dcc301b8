package com.mpolicy.settlement.core.common.reconcile;

import lombok.Data;

import java.io.Serializable;

@Data
public class SettlementStatusOut implements Serializable {
    private static final long serialVersionUID = -7130504446081824727L;
    /**
     * 保单号
     */
    private String policyNo;

    /**
     * 期次
     */
    private Integer renewalPeriod;

    /**
     * 主险编码
     */
    private String mainProductCode;

    /**
     * 0 未结算 1:结算了但是不确定全部结算完成
     */
    private Integer settlementStatus;
}
