package com.mpolicy.settlement.core.common.commission.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CommissionBasicPolicyPrem implements Serializable {

    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 批单号
     */
    private String batchCode;
    private String productCode;
    private String productName;
    /**
     * 保费
     */
    private BigDecimal premium;
    /**
     * 车船税（元）
     */
    private BigDecimal vehicleVesselTax;
    /**
     * 车船税费率
     */
    private BigDecimal vehicleVesselTaxRate;
    /**
     * 基础佣金费率
     */
    private BigDecimal commissionRate;
    /**
     * 结算机构
     */
    private String settlementCompanyCode;
    /**
     * 结算机构
     */
    private String settlementCompanyName;

    /**
     * 保单年期
     */
    private Integer year;
    /**
     * 缴费期次
     */
    private Integer period;
}
