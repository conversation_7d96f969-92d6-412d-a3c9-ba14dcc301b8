package com.mpolicy.settlement.core.common.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 基础参数
 *
 * <AUTHOR>
 * @since 2022/10/10
 */
@Data
@ApiModel(value = "基础参数", description = "基础参数")
public class SettlementGlobalBase {

    @ApiModelProperty(value = "编码", required = true, example = "INS202204212020202")
    private String policyCode;
}
