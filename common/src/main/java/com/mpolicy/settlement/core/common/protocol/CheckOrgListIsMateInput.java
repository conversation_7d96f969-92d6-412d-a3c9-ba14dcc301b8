package com.mpolicy.settlement.core.common.protocol;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class CheckOrgListIsMateInput implements Serializable {
    private static final long serialVersionUID = 2911576894119433183L;

    @NotBlank(message = "唯一值不能为空")
    private  String businessCode;

    @NotBlank(message = "机构编码不能为空")
    private String orgCode;

    @NotBlank(message = "生效时间不能为空")
    private String effectiveDate;

    @NotBlank(message = "险种编码不能为空")
    private String productCode;
}
