package com.mpolicy.settlement.core.common.autocost;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("结算生服对接人导出信息")
public class SettlementPcoInfoExpertDto implements Serializable {
    @ApiModelProperty("区域名称")
    String regionName;

    @ApiModelProperty("分支名称")
    String orgName;

    @ApiModelProperty(value = "分支编码")
    String orgCode;

    @ApiModelProperty("员工工号")
    String employeeCode;

    @ApiModelProperty("员工名称")
    String employeeName;

}
