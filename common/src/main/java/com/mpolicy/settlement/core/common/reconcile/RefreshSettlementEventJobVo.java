package com.mpolicy.settlement.core.common.reconcile;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RefreshSettlementEventJobVo implements Serializable {
    private static final long serialVersionUID = -2058364924952806072L;


    private List<Integer> ids;

    private List<String> pushEventCodeList;

    /**
     * 支出事件状态;0代处理1处理中 3无需处理-1处理失败
     */
    private Integer costEventStatus;

    /**
     * 支出事件响应消息
     */
    private String costEventMessage;


    /**
     * 处理状态
     */
    private Integer eventStatus;

    /**
     * 合约收入事件处理完成时间;0代处理1处理中2处理完成3无需处理-1处理失败
     */
    private Integer contractIncomeEventStatus;

    private String contractIncomeEventMessage;

    /**
     * 小鲸收入事件状态;0代处理1处理中2处理完成3无需处理-1处理失败
     */
    private Integer incomeEventStatus;

    /**
     * 小鲸收入事件响应消息
     */
    private String incomeEventMessage;
}
