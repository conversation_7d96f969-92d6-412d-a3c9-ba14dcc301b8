package com.mpolicy.settlement.core.common.reconcile;

import lombok.Data;

import java.io.Serializable;
@Data
public class SettlementStatusInput implements Serializable {
    private static final long serialVersionUID = 5396923846161336058L;

    /**
     * 保单号
     */
    private String policyNo;

    /**
     * 期次
     */
    private Integer renewalPeriod;

    /**
     * 主险编码
     */
    private String mainProductCode;
}
