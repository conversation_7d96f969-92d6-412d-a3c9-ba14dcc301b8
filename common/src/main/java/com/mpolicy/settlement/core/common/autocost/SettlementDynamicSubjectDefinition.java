package com.mpolicy.settlement.core.common.autocost;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "结算动态科目定义信息", description = "结算动态科目定义信息")
public class SettlementDynamicSubjectDefinition implements Serializable {
    @ApiModelProperty("id")
    private Integer id;

    /**
     * 科目编码
     */
    @ApiModelProperty("科目编码")
    private String subjectCode;

    /**
     * 科目名称
     */
    @ApiModelProperty("科目编码")
    private String subjectName;

    /**
     * 备注
     */
    @ApiModelProperty("科目编码")
    private String remark;
    /**
     * 状态
     */
    @ApiModelProperty(value = "科目编码",hidden = true)
    private Integer status=1;
    @ApiModelProperty(value = "操作人")
    private String updateUser;
    @ApiModelProperty(value = "操作时间")
    private Date updateTime;
}
