package com.mpolicy.settlement.core.common.autocost;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@ApiModel(value = "发放对象结算明细查询", description = "发放对象结算明细查询")
public class WxUserSettlementQuery implements Serializable {
    /**
     * 发放对象编码
     */
    @ApiModelProperty(value = "发放对象编码", required = true, example = "202312")
    @NotBlank(message = "发放对象编码不能为空")
    private String sendObjectCode;
    /**
     * 结算周期
     */
    @ApiModelProperty(value = "结算周期", required = true, example = "202312")
    @NotBlank(message = "结算周期不能为空")
    private String costSettlementCycle;
}
