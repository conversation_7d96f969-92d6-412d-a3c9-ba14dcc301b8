package com.mpolicy.settlement.core.common.reconcile;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class RectificationOneVo implements Serializable {
    private static final long serialVersionUID = -4456835466928395018L;


    /**
     * 事件源编码
     */
    private String eventSourceCode;

    private List<String> eventSourceCodeList;
    /**
     * 结算类型 0:小鲸 1:非小鲸
     */
    private Integer reconcileType;
    /**
     * 是否运行Job任务 0:否不运行 1:运行
     */
    @NotNull(message = "任务运行状态不能为空")
    private Integer runJob;
}
