package com.mpolicy.settlement.core.common.autocost;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel(value = "发放对象结算科目明细数据", description = "发放对象结算科目明细数据")
public class WxUserSubjectDetail implements Serializable {
    /**
     * 科目编码
     */
    @ApiModelProperty(value = "科目编码", required = true, example = "202312")
    private String sendSubjectCode;
    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称", required = true, example = "202312")
    private String sendSubjectName;
    /**
     * 发放金额
     */
    @ApiModelProperty(value = "发放金额", required = true, example = "202312")
    private BigDecimal grantAmount;
}
