package com.mpolicy.settlement.core.modules.autocost.service;

import com.mpolicy.settlement.core.modules.autocost.service.impl.HrGrantPersonSyncServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Hr发放人员同步服务测试
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@ExtendWith(MockitoExtension.class)
class HrGrantPersonSyncServiceTest {

    @Mock
    private HrGrantPersonSyncService hrGrantPersonSyncService;

    @InjectMocks
    private HrGrantPersonSyncServiceImpl hrGrantPersonSyncServiceImpl;

    private static final DateTimeFormatter CYCLE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");

    @Test
    void testSettlementCycleFormat() {
        // 测试结算周期格式
        String validCycle = "202506";
        assertDoesNotThrow(() -> {
            YearMonth yearMonth = YearMonth.parse(validCycle, CYCLE_FORMATTER);
            assertEquals(2025, yearMonth.getYear());
            assertEquals(6, yearMonth.getMonthValue());
        });

        // 测试无效格式
        String invalidCycle = "2025-06-30";
        assertThrows(Exception.class, () -> {
            YearMonth.parse(invalidCycle, CYCLE_FORMATTER);
        });
    }

    @Test
    void testTimeRangeCalculation() {
        // 测试时间范围计算
        String settlementCycle = "202506";
        YearMonth yearMonth = YearMonth.parse(settlementCycle, CYCLE_FORMATTER);
        
        // 验证时间计算逻辑
        assertEquals("2025-05-31", yearMonth.minusMonths(1).atEndOfMonth().toString());
        assertEquals("2025-06-01", yearMonth.atDay(1).toString());
        assertEquals("2025-06-30", yearMonth.atEndOfMonth().toString());
    }

    @Test
    void testCycleValidation() {
        // 测试各种结算周期格式
        String[] validCycles = {"202501", "202512", "202406"};
        String[] invalidCycles = {"2025-06", "20250601", "202513", "202500"};

        for (String cycle : validCycles) {
            assertDoesNotThrow(() -> YearMonth.parse(cycle, CYCLE_FORMATTER),
                    "Valid cycle should not throw exception: " + cycle);
        }

        for (String cycle : invalidCycles) {
            assertThrows(Exception.class, () -> YearMonth.parse(cycle, CYCLE_FORMATTER),
                    "Invalid cycle should throw exception: " + cycle);
        }
    }
}
