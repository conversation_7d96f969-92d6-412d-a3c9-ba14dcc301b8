package com.mpolicy.settlement.core.modules.autocost.dto;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.modules.autocost.enums.SettlementInstitutionEnum;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = true)
@ApiModel("绩效补发excel导入文档")
public class SupervisorReissuePerformanceExcelDto extends BaseRowModel implements Serializable {
    @ApiModelProperty("统计日期")
    @ExcelProperty(value="统计日期", index = 0)
    Date businessAccountTime;

    @ApiModelProperty("区域名称")
    @ExcelProperty(value="区域名称", index = 1)
    String regionName;

    @ApiModelProperty("发放对象机构名称")
    @ExcelProperty(value="分支名称", index = 2)
    String objectOrgName;

    @ApiModelProperty(value = "发放对象机构编码",hidden = true)
    String objectOrgCode;

    @ApiModelProperty("发放对象编码")
    @ExcelProperty(value="工号", index = 3)
    String sendObjectCode;

    @ApiModelProperty("发放对象名称")
    @ExcelProperty(value="姓名", index = 4)
    String sendObjectName;

    @ApiModelProperty("保险金额")
    @ExcelProperty(value="保险金额", index = 5)
    BigDecimal premium;

    @ApiModelProperty("自动结算金额")
    @ExcelProperty(value="绩效奖励", index = 6)
    BigDecimal commissionAmount;

    @ApiModelProperty("是否涉及长期续期率")
    @ExcelProperty(value="是否涉及长期续期率", index = 7)
    String renewalFlag;

    @ApiModelProperty("结算机构名称")
    @ExcelProperty(value="结算机构", index = 8)
    String settlementInstitutionName;

    @ApiModelProperty(value = "结算机构编码" ,hidden = true)
    String settlementInstitution;


    public String getSettlementInstitution() {
        return SettlementInstitutionEnum.getByName(settlementInstitutionName).getCode();
    }

    @ApiModelProperty("续期率")
    @ExcelProperty(value="续期率", index = 9)
    BigDecimal renewalRate;



    @ApiModelProperty("补发金额")
    @ExcelProperty(value="应补发", index = 10)
    BigDecimal grantAmount;

    /**
     * 补发比例
     * @return
     */
    public BigDecimal getGrantRate(){
        if(grantAmount == null || commissionAmount == null){
            return BigDecimal.ZERO;
        }
        if(commissionAmount.compareTo(BigDecimal.ZERO)==0){
            return BigDecimal.ZERO;
        }
        return grantAmount.divide(commissionAmount,2,BigDecimal.ROUND_HALF_UP);
    }

    public static void validate(SupervisorReissuePerformanceExcelDto dto){
        if(StringUtils.isBlank(dto.getSendObjectCode())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("工号为空"));
        }
        if(StringUtils.isBlank(dto.getSendObjectName())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("工号为{}的姓名为空",dto.getSendObjectCode())));
        }

        if(StringUtils.isBlank(dto.getObjectOrgName())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("工号为{}的机构为空",dto.getSendObjectCode())));
        }

        if(dto.getCommissionAmount()==null){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("工号为{}的绩效金额为空",dto.getSendObjectCode())));
        }else if(dto.getPremium()!=null && dto.getCommissionAmount().abs().compareTo(dto.getPremium().abs())>0){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("工号为{}的绩效金额异常，大于保险金额",dto.getPremium())));
        }

        if(StringUtils.isBlank(dto.getSettlementInstitutionName())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("工号为{}的结算机构为空",dto.getSendObjectCode())));
        }
    }

}
