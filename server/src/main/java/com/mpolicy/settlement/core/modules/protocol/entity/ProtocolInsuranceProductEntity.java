package com.mpolicy.settlement.core.modules.protocol.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 协议产品信息
 * 
 * <AUTHOR>
 * @date 2023-05-20 20:31:56
 */
@TableName("ep_protocol_insurance_product")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProtocolInsuranceProductEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 保司编码
	 */
	private String companyCode;
	/**
	 * 保司名称
	 */
	private String companyName;
	/**
	 * 保司简称
	 */
	private String companyShortName;
	/**
	 * 协议保司产品编码
	 */
	private String insuranceProductCode;
	/**
	 * 结算类型0:小鲸 1:非小鲸
	 */
	private Integer reconcileType;
	/**
	 * 协议保司产品名称
	 */
	private String insuranceProductName;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 修改人
	 */
	private String updateUser;
	/**
	 * 修改时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
}
