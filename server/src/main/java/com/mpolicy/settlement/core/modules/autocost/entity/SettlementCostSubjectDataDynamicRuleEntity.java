package com.mpolicy.settlement.core.modules.autocost.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 科目范围(动态科目)规则
 * 
 * <AUTHOR>
 * @since 2023-11-27 20:42:05
 */
@TableName("settlement_cost_subject_data_dynamic_rule")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostSubjectDataDynamicRuleEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 动态科目申请编号
	 */
	private String applyCode;
	/**
	 * 所属周期
	 */
	private String costSettlementCycle;
	/**
	 * 动态科目编号
	 */
	private String dynamicSubjectCode;
	/**
	 * 动态科目名称
	 */
	private String dynamicSubjectName;
	/**
	 * 动态科目编号分摊规则
	 */
	private String dynamicRuleCode;
	/**
	 * 动态科目编号分摊规则
	 */
	private String dynamicRuleName;
	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
