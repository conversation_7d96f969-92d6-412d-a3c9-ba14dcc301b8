package com.mpolicy.settlement.core.modules.autocost.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.dao.SettlementCostSubjectDataPolicyCommDao;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataPolicyComm;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostSubjectDataPolicyCommEntity;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostSubjectDataPolicyCommService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 科目范围(基础佣金)数据
 *
 * <AUTHOR>
 * @since 2023-10-31 20:03:47
 */
@Slf4j
@Service("settlementCostSubjectDataPolicyCommService")
public class SettlementCostSubjectDataPolicyCommServiceImpl extends ServiceImpl<SettlementCostSubjectDataPolicyCommDao, SettlementCostSubjectDataPolicyCommEntity> implements SettlementCostSubjectDataPolicyCommService {
    @Override
    public int saveList(List<SubjectDataPolicyComm> list) {
        // 进行批量vo 转换 po
        List<SettlementCostSubjectDataPolicyCommEntity> listData = new ArrayList<>();
        SettlementCostSubjectDataPolicyCommEntity bean;
        for (SubjectDataPolicyComm x : list){
            bean = new SettlementCostSubjectDataPolicyCommEntity();
            BeanUtils.copyProperties(x, bean);
            listData.add(bean);
        }
        int result = 0;
        // 批量写入
        if (list.size() > 600) {
            List<List<SettlementCostSubjectDataPolicyCommEntity>> partition = ListUtils.partition(listData, 600);
            for (List<SettlementCostSubjectDataPolicyCommEntity> x : partition) {
                result = result + baseMapper.insertBatchSomeColumn(x);
            }
        } else {
            result = baseMapper.insertBatchSomeColumn(listData);
        }
        return result;
    }

    @Override
    public List<SubjectDataPolicyComm> querySubjectData(String costSettlementCycle, String subjectCode) {
        // 1 获取基础po数据
        List<SettlementCostSubjectDataPolicyCommEntity> list = lambdaQuery()
                .eq(SettlementCostSubjectDataPolicyCommEntity::getCostSettlementCycle, costSettlementCycle)
                .eq(SettlementCostSubjectDataPolicyCommEntity::getSubjectCode, subjectCode)
                .list();

        // 2 执行po 转换vo
        List<SubjectDataPolicyComm> result = new  ArrayList<>();
        SubjectDataPolicyComm bean;
        for (SettlementCostSubjectDataPolicyCommEntity x : list){
            bean = new SubjectDataPolicyComm();
            BeanUtils.copyProperties(x, bean);
            result.add(bean);
        }
        log.info("结算周期={} 科目编码={} 查询结果条数：{}", costSettlementCycle, subjectCode, result.size());
        return result;
    }

    @Override
    public List<SubjectDataPolicyComm> querySubjectData(String costSettlementCycle, String subjectCode, int maxId, int limitSize) {
        /*// 1 获取基础po数据
        List<SettlementCostSubjectDataPolicyCommEntity> list = lambdaQuery()
                .eq(SettlementCostSubjectDataPolicyCommEntity::getCostSettlementCycle, costSettlementCycle)
                .eq(SettlementCostSubjectDataPolicyCommEntity::getSubjectCode, subjectCode)
                .gt(SettlementCostSubjectDataPolicyCommEntity::getId, maxId)
                .last(" limit "+ limitSize)
                .list();
        // 2 执行po 转换vo
        List<SubjectDataPolicyComm> result = new  ArrayList<>();
        SubjectDataPolicyComm bean;
        for (SettlementCostSubjectDataPolicyCommEntity x : list){
            bean = new SubjectDataPolicyComm();
            BeanUtils.copyProperties(x, bean);
            result.add(bean);
        }
        return result;*/
        return querySubjectDataInfo(costSettlementCycle,subjectCode,maxId,limitSize,Boolean.FALSE);
    }
    public List<SubjectDataPolicyComm> queryAddSubjectData(String costSettlementCycle, String subjectCode, int maxId, int limitSize){
        return querySubjectDataInfo(costSettlementCycle,subjectCode,maxId,limitSize,Boolean.TRUE);
    }


    private List<SubjectDataPolicyComm> querySubjectDataInfo(String costSettlementCycle, String subjectCode, int maxId, int limitSize,Boolean isLong) {
        // 1 获取基础po数据
        List<SettlementCostSubjectDataPolicyCommEntity> list = lambdaQuery()
                .eq(SettlementCostSubjectDataPolicyCommEntity::getCostSettlementCycle, costSettlementCycle)
                .eq(SettlementCostSubjectDataPolicyCommEntity::getSubjectCode, subjectCode)
                .gt(SettlementCostSubjectDataPolicyCommEntity::getId, maxId)
                .eq(isLong,SettlementCostSubjectDataPolicyCommEntity::getLongShortFlag,1)
                .last(" limit "+ limitSize)
                .list();
        // 2 执行po 转换vo
        List<SubjectDataPolicyComm> result = new  ArrayList<>();
        SubjectDataPolicyComm bean;
        for (SettlementCostSubjectDataPolicyCommEntity x : list){
            bean = new SubjectDataPolicyComm();
            BeanUtils.copyProperties(x, bean);
            result.add(bean);
        }
        return result;
    }

    @Override
    public void removePolicyCommonByIdList(List<Integer> idList) {
        if(idList.isEmpty()){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("idList参数缺失，无法设置作废结算科目数据"));
        }
        lambdaUpdate().set(SettlementCostSubjectDataPolicyCommEntity::getDeleted, -1)
                .in(SettlementCostSubjectDataPolicyCommEntity::getId,idList)
                .update();
    }

    @Override
    public void removePolicyCommonAndRemarkByIdList(List<Integer> idList,String remark) {
        if(idList.isEmpty()){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("idList参数缺失，无法设置作废结算科目数据"));
        }
        lambdaUpdate().set(SettlementCostSubjectDataPolicyCommEntity::getDeleted, -1)
                .set(SettlementCostSubjectDataPolicyCommEntity::getRemark,remark)
                .in(SettlementCostSubjectDataPolicyCommEntity::getId,idList)

                .update();
    }

    public List<SubjectDataPolicyComm> listLongPromotionDataByPolicyNos(List<String> policyNos) {
        if(CollectionUtils.isEmpty(policyNos)){
            return Collections.emptyList();
        }
        // 1 获取基础po数据
        List<SettlementCostSubjectDataPolicyCommEntity> list = lambdaQuery()
                .eq(SettlementCostSubjectDataPolicyCommEntity::getSubjectCode, CostSubjectEnum.LONG_PROMOTION.getCode())
                .in(SettlementCostSubjectDataPolicyCommEntity::getPolicyNo,policyNos)
                .list();
        // 2 执行po 转换vo
        List<SubjectDataPolicyComm> result = new  ArrayList<>();
        SubjectDataPolicyComm bean;
        for (SettlementCostSubjectDataPolicyCommEntity x : list){
            bean = new SubjectDataPolicyComm();
            BeanUtils.copyProperties(x, bean);
            result.add(bean);
        }
        return result;
    }
}
