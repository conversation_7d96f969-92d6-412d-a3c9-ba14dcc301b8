package com.mpolicy.settlement.core.modules.autocost.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@TableName("settlement_cost_import_operation_record")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostImportOperationRecordEntity implements Serializable {
    /**
     * id
     */
    @TableId
    private Integer id;
    /**
     * 动态科目申请编号
     */
    @ApiModelProperty(value = "文件类型", example = "KMH")
    private String fileType;

    /**
     * 所属周期
     */
    @ApiModelProperty(value = "所属周期", example = "202311")
    private String costSettlementCycle;
    /**
     * 操作类型 新增ins 更新upd 删除 del
     */
    @ApiModelProperty(value = "操作类型 新增ins 更新upd 删除 del", example = "del")
    private String opType;

    /**
     * 前一个文件导入申请编码
     */
    @ApiModelProperty(value = "前一个文件导入申请编码", example = "KMH")
    private String beforeApplyCode;

    /**
     * 前一个文件名称
     */
    private String beforeFileName;

    /**
     * 前一个域名访问地址
     */
    private String beforeDomainPath;

    /**
     * 当前导入申请编号
     */
    @ApiModelProperty(value = "当前导入申请编号", example = "KMH")
    private String currentApplyCode;

    @TableLogic
    private Integer deleted;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @Version
    @TableField(fill = FieldFill.INSERT)
    private Integer revision;
}
