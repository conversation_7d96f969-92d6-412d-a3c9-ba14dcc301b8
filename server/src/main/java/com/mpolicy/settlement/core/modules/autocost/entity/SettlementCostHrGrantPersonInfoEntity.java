package com.mpolicy.settlement.core.modules.autocost.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 结算Hr发放人员表
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */
@TableName("settlement_cost_hr_grant_person_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostHrGrantPersonInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 工号
     */
    private String sendObjectCode;

    /**
     * 姓名
     */
    private String sendObjectName;

    /**
     * hr发放标志位 0 hr不发放，1 hr发放
     */
    private Integer hrGrantFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除;0有效-1删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 乐观锁
     */
    @Version
    @TableField(fill = FieldFill.INSERT)
    private Integer revision;
}
