package com.mpolicy.settlement.core.modules.reconcile.dto.policy;

import com.baomidou.mybatisplus.annotation.TableField;
import com.mpolicy.policy.common.ep.policy.preserve.EpPreserveProductInsuredMapVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class EpPreserveAddSubtractDetailDto {
    private static final long serialVersionUID = 1L;

    /**
     * 保全流水号
     */
    private String preservationCode;
    /**
     * 保单中心保单唯一号
     */
    private String contractCode;
    /**
     * 保单号
     */
    private String policyCode;

    private String insuredCode;
    /**
     * 序号
     */
    private String peopleNumber;
    /**
     * 增减员类型 加人/减人
     */
    private String peopleType;

    /**
     * 主被保人标识
     */
    private String mainInsuredFlag;

    /**
     * 对应主被保人
     */
    private String mainInsuredName;

    /**
     * 被保人姓名
     */
    private String insuredName;
    /**
     * 与主被保人关系
     */
    private String firstInsuredRelation;
    /**
     * 被保人性别
     */
    private String insuredGender;
    /**
     * 被保人出生日期
     */
    private String insuredBirthday;
    /**
     * 被保人证件类型
     */
    private String insuredIdType;
    /**
     * 被保人证件号码
     */
    private String insuredIdCard;
    /**
     * 证件有效期
     */
    private String insuredIdCardValidityEnd;
    /**
     * 国籍
     */
    private String insuredNation;
    /**
     * 地址
     */
    private String insuredAddress;
    /**
     * 保险计划编码
     */
    private String productCode;
    /**
     * 保险计划编码
     */
    private String planCode;

    /**
     * 计划名称
     */
    private String planName;
    /**
     *  虚拟计划编码
     */
    private String virtualPlanCode;
    /**
     *  虚拟计划名称
     */
    private String virtualPlanName;
    /**
     * 保费
     */
    private BigDecimal singlePremium;

    /**
     * 职业代码
     */
    private String insuredCareer;
    /**
     * 职业类别
     */
    private String insuredOccupationalCategory;
    /**
     * 主被保人证件号码
     */
    private String mainInsuredIdCard;
    /**
     * 联系方式
     */
    private String insuredMobile;
    /**
     * 电子邮箱
     */
    private String insuredEmail;
    /**
     * 工作单位
     */
    private String insuredCompany;

    @TableField("referrer_code")
    private String referrerCode;

    @TableField("channel_referrer_code")
    private String channelReferrerCode;

    @TableField("channel_branch_code")
    private String channelBranchCode;

    @TableField("channel_code")
    private String channelCode;

    @TableField("org_code")
    private String orgCode;

    @ApiModelProperty("增员批改单号")
    private String addEndorsementNo;

    @ApiModelProperty("减员批改单号")
    private String subtractEndorsementNo;

    /**
     * 客户经理
     */
    private String customerManagerCode;

    /**
     * 客户经理渠道编码
     */
    private String customerManagerChannelCode;
    /**
     * 客户经理所属分支机构
     */
    private String customerManagerOrgCode;
    /**
     * 客户经理渠道机构编码
     */
    private String customerManagerChannelOrgCode;
    /**
     * 客户经理督导
     */
    private String customerManagerSupervisor;

    @ApiModelProperty("被保人险种明细")
    private List<EpPreserveProductDto> insuredProductList;
}
