package com.mpolicy.settlement.core.modules.autocost.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.mpolicy.service.common.mapper.ImsBaseMapper;
import com.mpolicy.settlement.core.modules.autocost.dto.employee.EmployeeInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Employee服务持久服务
 *
 * <AUTHOR>
 * @since 2023-11-11 10:29
 */
public interface EmployeeDao extends ImsBaseMapper<EmployeeInfo> {

    /**
     * 获取员工集合
     *
     * @param wrapper 查询器
     * @return 员工集合
     * <AUTHOR>
     * @since 2023/11/11 10:48
     */
    List<EmployeeInfo> selectEmployees(@Param(Constants.WRAPPER) Wrapper wrapper);
}