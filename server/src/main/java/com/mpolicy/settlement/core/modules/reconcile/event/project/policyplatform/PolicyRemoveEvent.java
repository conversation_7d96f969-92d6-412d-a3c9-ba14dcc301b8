package com.mpolicy.settlement.core.modules.reconcile.event.project.policyplatform;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileStatusEnum;
import com.mpolicy.settlement.core.modules.govern.dto.proejct.RemovePolicyData;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostCorrectionDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.project.common.AbsPolicyCommonEvent;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventCheckResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.SYSTEM_CORRECTION_USER;

/**
 * <AUTHOR>
 * @description 保单删除
 * @date 2024/2/26 12:03 上午
 * @Version 1.0
 */
@Service
@Slf4j
public class PolicyRemoveEvent extends AbsPolicyCommonEvent {
    @Override
    public HandleEventCheckResult handleIncomeEventCheck(SettlementEventJobEntity eventJob, Integer reconcileType) {
        RemovePolicyData changePolicyCodeData = JSONObject.parseObject(eventJob.getEventRequest(),
                RemovePolicyData.class);
        String policyNo = changePolicyCodeData.getPolicyCode();
        // 2.参数校验
        if (StrUtil.isEmpty(policyNo)) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg("保单删除事件没有获取到保单号").build();
        }
        // 判断事件是否已经生成了明细数据,如果生成了就不在运行了
        boolean checkSettlementPolicyInfo = checkSettlementPolicyInfo(eventJob.getPushEventCode(), reconcileType);
        // 如果存在纪录
        if (checkSettlementPolicyInfo) {
            return HandleEventCheckResult.builder().checkStatus(false)
                .checkMsg(StrUtil.format("保单删除事件已经写入明细")).build();
        }
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleIncomeEvent(SettlementEventJobEntity eventJob, HandleEventData handleEventData) {
        RemovePolicyData changePolicyCodeData = JSONObject.parseObject(eventJob.getEventRequest(),
                RemovePolicyData.class);
        String policyNo = changePolicyCodeData.getPolicyCode();
        // 2.参数校验
        if (StrUtil.isEmpty(policyNo)) {
            return "事件报文没有获取到保单号";
        }
        // 3.判断原批单号是否存在数据记录
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList = settlementPolicyInfoService.lambdaQuery()
            .eq(SettlementPolicyInfoEntity::getPolicyNo, policyNo)
            .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode())
            .eq(SettlementPolicyInfoEntity::getReconcileType, handleEventData.getReconcileType()).list();
        if (settlementPolicyInfoList.isEmpty()) {
            return "批单号没有获取到结算任务";
        }
        // 判断一下 是否存在已经结算的数据,如果存在,那么提示失败
        boolean anyMatch = settlementPolicyInfoList.stream()
            .anyMatch(a -> ReconcileStatusEnum.RECONCILE_FINISH.getStatusCode().equals(a.getReconcileStatus()));
        if (anyMatch) {
            return "明细存在已经结算的数据,暂不处理";
        }
        // 只做冲正处理了
        settlementPolicyInfoRectification(settlementPolicyInfoList);
        return "success";
    }

    @Override
    public HandleEventCheckResult handleCostEventCheck(SettlementEventJobEntity eventJob) {

        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleCostEvent(SettlementEventJobEntity eventJob, HandleEventData eventData) {
        RemovePolicyData removePolicyData = JSONObject.parseObject(eventJob.getEventRequest(), RemovePolicyData.class);
        String policyNo = removePolicyData.getPolicyCode();
        if (StrUtil.isEmpty(policyNo)) {
            return StrUtil.format("保单号-{}不存在-success", policyNo);
        }

        List<SettlementCostInfoEntity> costInfoEntityList = settlementCostInfoService.lambdaQuery()
                .eq(SettlementCostInfoEntity::getContractCode, removePolicyData.getContractCode())
                .eq(SettlementCostInfoEntity::getCorrectionFlag, 0)
                .list();
        if (CollectionUtils.isEmpty(costInfoEntityList)) {
            return StrUtil.format("保单号/合同号-{}/{}不存在-success", policyNo, removePolicyData.getContractCode());
        }

        SettlementCostPolicyInfoEntity costPolicy =
                settlementCostPolicyInfoService.getCostPolicyInfoEntityByContractCode(eventJob.getContractCode());

        if (Objects.isNull(costPolicy)) {
            return "结算保单信息缺失-success";
        }
        //2、参数验证，验证不通过抛出异常
        settlementCostProcessService.validParam(eventJob,null);

        //冲正
        CostCorrectionDto costCorrectionDto = new CostCorrectionDto();


        List<SettlementCostInfoEntity> newCostList = costInfoEntityList.stream().map(
                x -> {
                    //生成一条对冲信息
                    return settlementCostProcessService.builderOffsetCostInfo(eventJob, handlerEventType(), x,eventJob.getCreateTime(),
                            SYSTEM_CORRECTION_USER, "批单号变更冲正", Boolean.TRUE);
                }
        ).collect(Collectors.toList());


        costCorrectionDto.setNewCostList(newCostList);
        costCorrectionDto.setCorrectionOldIds(costInfoEntityList.stream().map(SettlementCostInfoEntity::getId).collect(Collectors.toList()));
        costCorrectionDto.setPolicy(costPolicy);

        settlementCostCorrectionService.saveCostCommissionRecord(eventJob, costCorrectionDto);
        policyCenterBaseClient.governApplyConfirm(eventJob.getEventBusinessCode(), "settlement_server", "支出端");
        return "success";
    }

    @Override
    public SettlementEventTypeEnum handlerEventType() {
        return SettlementEventTypeEnum.REMOVE_POLICY;
    }
}
