package com.mpolicy.settlement.core.modules.reconcile.event.project.policy;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.PolicyInterruptDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.ProductStatusEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.project.common.AbsPolicyCommonEvent;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventCheckResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 长险断保事件
 * @date 2023/10/26 9:23 下午
 * @Version 1.0
 */
@Service
@Slf4j
public class PolicyInterruptEvent extends AbsPolicyCommonEvent {

    public static int DEFAULT_POLICY_PERIOD_YEAR = 2;
    @Override
    public HandleEventCheckResult handleIncomeEventCheck(SettlementEventJobEntity eventJob,Integer reconcileType) {
        return HandleEventCheckResult.builder().checkStatus(false).checkMsg("长险断保事件业务不处理").build();
    }

    @Override
    public String handleIncomeEvent(SettlementEventJobEntity eventJob, HandleEventData eventData) {
        return "长险断保事件业务不处理-success";
    }

    @Override
    public HandleEventCheckResult handleCostEventCheck(SettlementEventJobEntity eventJob) {
        JSONObject eventData = JSONObject.parseObject(eventJob.getEventRequest());
        //初始推荐人变更是内部事件，与保司无关，所以不需要根据保司保全号判断记录是否存在
        // 1 根据保全操作唯一编号判断是否存在支出明细记录
        Integer count = settlementCostInfoService.lambdaQuery()
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                .eq(SettlementCostInfoEntity::getContractCode, eventJob.getContractCode())
                .eq(SettlementCostInfoEntity::getSettlementSubjectCode,SettlementEventTypeEnum.POLICY_INTERRUPT.getRelationCostSubject())
                .count();
        // 如果存在纪录
        if (count > 0) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg(StrUtil.format("支出端-该保单未续回扣事件已存在，保单号={} 保全申请单号={}", eventJob.getEventBusinessCode(), eventJob.getEventBusinessCode())).build();
        }
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleCostEvent(SettlementEventJobEntity eventJob, HandleEventData eventData) {
        EpContractInfoVo policyInfo = eventData.getPolicyInfo();

        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        //1、参数验证，验证不通过抛出异常
        settlementCostProcessService.validParam(eventJob, policyInfo, () -> {
            if (policyProductTypeEnum == null) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("长险未续回扣事件无法获取保单类，保单号={}, 保单类型={}", eventJob.getEventBusinessCode(), eventJob.getEventBusinessCode())));
            }
        });
        if(!Objects.equals(PolicyProductTypeEnum.PERSONAL.getCode(),policyProductTypeEnum.getCode())){
            return "非个险直接通过-success";
        }

        // 主险 如果是保单是长线的话，退保是不需要做结算处理
        ProductBase mainProductBase = productBaseService.getProductInfo(policyInfo.getContractBaseInfo().getMainProductCode());
        if (mainProductBase.getLongShortFlag() != null && mainProductBase.getLongShortFlag() != 1) {
            return "非长险直接通过-success";
        }
        //
        PolicyInterruptDto dto = getPolicyInterruptDto(JSONObject.parseObject(eventJob.getEventRequest()));
        if(dto.getPolicyPeriodYear()==null){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("长险未续回扣事件，保单年度不能为空，保单号={}, 保单类型={}", eventJob.getEventBusinessCode(), eventJob.getEventBusinessCode())));
        }else if(dto.getPolicyPeriodYear()!= DEFAULT_POLICY_PERIOD_YEAR){
            //第三期及以后中断直接不处理
            String msg = StrUtil.format("长险保单{}第{}年度断保，不做处理",dto.getContractCode(),dto.getPolicyPeriodYear());
            log.info(msg);
            return msg+"-success";
        }
        //2、获取所有小鲸险种集合
        Map<String, ProductBase> productMap = settlementCostProcessService.getProductBaseMap(policyInfo.getProductInfoList());

        //3、支出端-创建保单基础信息
        SettlementCostPolicyInfoEntity costPolicy = settlementCostPolicyInfoService.getCostPolicyInfoEntityByContractCode(policyInfo.getContractCode());
        if (Objects.isNull(costPolicy)) {
            costPolicy = settlementCostProcessService.buildSettlementCostPolicyInfo(policyInfo);
        }else{
            settlementCostProcessService.builderUpdateSettlementCostPolicyInfo(policyInfo,costPolicy);
            //断保
            costPolicy.setPolicyStatus(ProductStatusEnum.DISCONTINUE.getCode());
        }

        //4、生成佣金记录
        List<SettlementCostInfoEntity> costInfoList = settlementCostProcessService.builderPolicyInterruptCostInfo(eventJob, CostSubjectEnum.LONG_NOT_RENEWAL_REBATE_COMM, SettlementEventTypeEnum.POLICY_INTERRUPT, productMap, policyInfo,dto);

        //处理农保员工在职状态
        settlementCostOwnerService.handlerOwnerThirdStatus(costPolicy,costInfoList);
        //5、保存记录信息
        settlementCostProcessService.saveCostCommissionRecord(eventJob, costPolicy, costInfoList);

        return "success";
    }

    private PolicyInterruptDto getPolicyInterruptDto(JSONObject eventData){
        PolicyInterruptDto dto =  PolicyInterruptDto.builder()
                .contractCode(eventData.getString("contractCode"))
                .policyNo(eventData.getString("policyNo"))
                .policyPeriodYear(eventData.getInteger("policyPeriodYear"))
                .interruptTime(eventData.getDate("interruptTime"))
                .build();

        return dto;
    }

    @Override
    public SettlementEventTypeEnum handlerEventType() {
        return SettlementEventTypeEnum.POLICY_INTERRUPT;
    }
}
