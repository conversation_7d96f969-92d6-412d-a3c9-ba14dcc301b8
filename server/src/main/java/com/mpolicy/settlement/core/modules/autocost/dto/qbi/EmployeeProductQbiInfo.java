package com.mpolicy.settlement.core.modules.autocost.dto.qbi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 支出结算员工险种指标基本信息
 *
 * <AUTHOR>
 * @since 2023-11-05 19:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "支出结算员工险种指标基本信息", description = "支出结算员工险种指标基本信息")
public class EmployeeProductQbiInfo extends ProductCommonQbiInfo implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 员工编号
     */
    @ApiModelProperty(value = "员工编号", example = "ZHNX202020")
    private String employeeCode;

    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称", example = "张三")
    private String employeeName;

    /**
     * 分支编号
     */
    @ApiModelProperty(value = "分支编号", example = "ZHNX202020")
    private String orgCode;

    /**
     * 分支名称
     */
    @ApiModelProperty(value = "分支名称", example = "ZHNX202020")
    private String orgName;


    /**
     * 长险推广费
     */
    @ApiModelProperty(value = "长险推广费", example = "0.8")
    private BigDecimal longPromotion;

    /**
     * 长险结算保费
     */
    @ApiModelProperty(value = "长险结算保费", example = "0.8")
    private BigDecimal longSettlementPremium;

    /**
     * 短险推广费
     */
    @ApiModelProperty(value = "短险推广费", example = "0.8")
    private BigDecimal shortPromotion;

    /**
     * 短险保费
     */
    @ApiModelProperty(value = "短险结算保费", example = "0.8")
    private BigDecimal shortSettlementPremium;

    /**
     * 短期险折标保费
     */
    @ApiModelProperty(value = "短期险折标保费", example = "0.8")
    private BigDecimal shortAssessConvertInsurancePremium;


    /**
     * 长期险折标保费
     */
    @ApiModelProperty(value = "长期险折标保费", example = "0.8")
    private BigDecimal longAssessConvertInsurancePremium;
}
