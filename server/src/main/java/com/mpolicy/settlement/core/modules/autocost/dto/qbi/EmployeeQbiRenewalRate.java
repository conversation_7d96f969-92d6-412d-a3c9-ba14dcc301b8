package com.mpolicy.settlement.core.modules.autocost.dto.qbi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 保险结算员工粒度R13表信息
 *
 * <AUTHOR>
 * @since 2023-12-16 19:50
 */
@Data
@ApiModel(value = "保险结算员工粒度R13表信息", description = "保险结算员工粒度R13表信息")
public class EmployeeQbiRenewalRate implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 员工编号
     */
    @ApiModelProperty(value = "员工编号", example = "ZHNX202020")
    private String employeeCode;

    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称", example = "张三")
    private String employeeName;

    /**
     * 结算月份
     */
    @ApiModelProperty(value = "结算月份")
    private String bizMonth;

    /**
     * pco等级
     */
    @ApiModelProperty(value = "pco等级", example = "S")
    private String pcoLevel;

    /**
     * 续期率
     */
    @ApiModelProperty(value = "R13续期率", example = "0.8")
    private BigDecimal renewalRate;

    /**
     * 应缴金额
     */
    private BigDecimal payAmt;
    /**
     * 实缴金额
     */
    private BigDecimal actPayAmt;

    /**
     * 快照分区
     */
    @ApiModelProperty(value = "快照分区", example = "20231212")
    private Integer pt;
}
