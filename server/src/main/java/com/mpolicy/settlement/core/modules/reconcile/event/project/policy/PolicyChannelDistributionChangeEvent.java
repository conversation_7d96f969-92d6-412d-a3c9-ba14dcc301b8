package com.mpolicy.settlement.core.modules.reconcile.event.project.policy;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.policy.common.ep.policy.preserve.ChannelDistributionChangeForm;
import com.mpolicy.policy.common.ep.policy.preserve.EpPreservationV2Vo;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.project.common.AbsPolicyCommonEvent;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventCheckResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 结算事件-【保单中心】保全-保单图例变更
 */
@Service
@Slf4j
public class PolicyChannelDistributionChangeEvent extends AbsPolicyCommonEvent {
    @Override
    public HandleEventCheckResult handleIncomeEventCheck(SettlementEventJobEntity eventJob, Integer reconcileType) {
        // 2 获取保全的批改单号，判断是否存在结算明细纪录
        JSONObject eventData = JSONObject.parseObject(eventJob.getEventRequest());
        String preservationCode = eventData.getString("preservationCode");
        if (StrUtil.isBlank(preservationCode)) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg(
                    StrUtil.format("保全申请编码不存在,数据结构异常,不做处理 PushEventCode={}", eventJob.getPushEventCode())).build();
        }
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleIncomeEvent(SettlementEventJobEntity eventJob, HandleEventData handleEventData) {
        try {
            // 从请求报文里获取期数属性
            JSONObject eventRequest = JSONObject.parseObject(eventJob.getEventRequest());
            String preservationCode = eventRequest.getString("preservationCode");
            if (StrUtil.isBlank(preservationCode)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                        StrUtil.format("保单图例变更 保单号={}, 缺少保全编码", eventJob.getEventBusinessCode())));
            }
            // 获取保全详情
            EpPreservationV2Vo epPreservationV2Vo = policyCenterBaseClient.getPreservationDetail(preservationCode);
            if (epPreservationV2Vo == null) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                        StrUtil.format("保单图例变更 保单号={},保全编码={},保全详情不存在", eventJob.getEventBusinessCode(), preservationCode)));
            }
            ChannelDistributionChangeForm channelDistributionChangeVo = epPreservationV2Vo.getChannelDistributionChangeVo();
            if (channelDistributionChangeVo == null || StrUtil.isBlank(channelDistributionChangeVo.getCorrectedChannelDistributionCode())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                        StrUtil.format("保单图例变更 保单号={},保全编码={},图例信息不存在", eventJob.getEventBusinessCode(), preservationCode)));
            }

            // 需要修改的渠道信息
            String channelDistributionCode = channelDistributionChangeVo.getCorrectedChannelDistributionCode();
            // 是否已经存在已结算的数据,如果存在那么就要提示不能处理了.
            Integer count = settlementPolicyInfoService.lambdaQuery()
                    .eq(SettlementPolicyInfoEntity::getReconcileType, handleEventData.getReconcileType())
                    .eq(SettlementPolicyInfoEntity::getContractCode, eventJob.getContractCode())
                    .eq(SettlementPolicyInfoEntity::getReconcileStatus, 3)
                    .ne(SettlementPolicyInfoEntity::getChannelDistributionCode, channelDistributionCode)
                    .count();
            if (count != null && count > 0) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                        StrUtil.format("保单图例变更 保单号={}, 存在已结算明细记录", eventJob.getEventBusinessCode())));
            }
            // 变更数据 图例信息
            settlementPolicyInfoService.lambdaUpdate()
                    .eq(SettlementPolicyInfoEntity::getReconcileType, handleEventData.getReconcileType())
                    .eq(SettlementPolicyInfoEntity::getContractCode, eventJob.getContractCode())
                    .ne(SettlementPolicyInfoEntity::getReconcileStatus, 3)
                    .set(SettlementPolicyInfoEntity::getChannelDistributionCode, channelDistributionCode)
                    .update();
        } catch (GlobalException e) {
            throw e;
        } catch (Exception e) {
            String msg = StrUtil.format("生成结算明细异常，保单号={}, 事件编码={}", eventJob.getEventBusinessCode(),
                    eventJob.getPushEventCode());
            log.warn(msg, e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(msg));
        }
        return "success";
    }

    @Override
    public HandleEventCheckResult handleCostEventCheck(SettlementEventJobEntity eventJob) {

        return HandleEventCheckResult.builder().checkStatus(false).checkMsg("图例变更事件暂不处理").build();
    }

    @Override
    public String handleCostEvent(SettlementEventJobEntity eventJob, HandleEventData eventData) {

        return "success";
    }

    @Override
    public SettlementEventTypeEnum handlerEventType() {
        return SettlementEventTypeEnum.POLICY_CHANNEL_DISTRIBUTION_CHANGE;
    }
}
