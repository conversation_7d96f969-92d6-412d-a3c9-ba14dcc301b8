package com.mpolicy.settlement.core.modules.autocost.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Maps;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.modules.autocost.dto.base.SettlementInstitutionInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.cache.OrganizationCache;
import com.mpolicy.settlement.core.modules.autocost.dto.dynamic.*;
import com.mpolicy.settlement.core.modules.autocost.helper.SettlementEmployeeHelper;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostDynamicSubjectErrorDataService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostSubjectDataDynamicInfoService;
import com.mpolicy.settlement.core.modules.referrer.entity.ChannelApplicationReferrerEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 动态科目-【发放名单】
 *
 * <AUTHOR>
 * @since 2023-11-28 18:48
 */
@Slf4j
public class DynamicSubjectDataInfoListener extends SettlementObjectEventListener<Object, SubjectDataDynamicInfo> {

    private Map<Integer, SubjectDataDynamicRule> dynamicRuleRow = new HashMap<>();

    private final Map<String, SubjectDataDynamicRule> dynamicRuleMap;

    private final SettlementCostSubjectDataDynamicInfoService settlementCostSubjectDataDynamicInfoService;

    private final SettlementCostDynamicSubjectErrorDataService settlementCostDynamicSubjectErrorDataService;

    private final SubjectDataDynamicBase dataDynamicBase;

    private List<SubjectDataDynamicInfo> insertData = new ArrayList<>();

    private final Map<String, SettlementInstitutionInfo> settlementInstitutionMap;

    private Map<Integer,SubjectDataDynamicErrorBase> errorData = new HashMap<>();

    private Boolean haveErrorData = Boolean.FALSE;

    private Map<String,String> employeeMap = Maps.newHashMap();


    public DynamicSubjectDataInfoListener(SettlementCostSubjectDataDynamicInfoService settlementCostSubjectDataDynamicInfoService,
                                          SettlementCostDynamicSubjectErrorDataService settlementCostDynamicSubjectErrorDataService,
                                          SubjectDataDynamicBase dataDynamicBase,
                                          Map<String, SettlementInstitutionInfo> settlementInstitutionMap,
                                          Map<String, SubjectDataDynamicRule> dynamicRuleMap) {
        this.settlementCostSubjectDataDynamicInfoService = settlementCostSubjectDataDynamicInfoService;
        this.settlementCostDynamicSubjectErrorDataService = settlementCostDynamicSubjectErrorDataService;
        this.dataDynamicBase = dataDynamicBase;
        this.settlementInstitutionMap = settlementInstitutionMap;
        this.dynamicRuleMap = dynamicRuleMap;
    }

    /**
     * 通过 AnalysisContext 对象还可以获取当前 sheet，当前行等数据
     */
    @Override
    public void invoke(Object object, AnalysisContext context) {
        String xlsLineData = JSON.toJSONString(object);
        log.debug("当前sheet:{},当前行:{},data:{}", context.getCurrentSheet().getSheetNo(), context.getCurrentRowNum(), xlsLineData);
        JSONArray lineData = JSON.parseArray(xlsLineData);
        // 1 读取列信息，校验动态科目是否配置了规则
        if (context.getCurrentRowNum() == 0) {
            AtomicInteger ruleRowIndex = new AtomicInteger(0);
            // 1-1 读取lineData 数组大于6的为动态科目
            lineData.stream().skip(6).forEach(x -> {
                String sn = String.valueOf(x).trim();
                if (!dynamicRuleMap.containsKey(sn)) {
                    addErrorData(context,null,StrUtil.format("分摊信息配置不存在，科目名称:{}", x));
                    //throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("分摊信息配置不存在，科目名称:{}", x)));
                    SubjectDataDynamicRule rule = new SubjectDataDynamicRule();
                    rule.setDynamicSubjectName(sn);
                    dynamicRuleRow.put(ruleRowIndex.get() + 6,rule);
                }else {
                    dynamicRuleRow.put(ruleRowIndex.get() + 6, dynamicRuleMap.get(sn));
                }
                ruleRowIndex.getAndIncrement();
            });
            return;
        }

        if (StringUtils.isNotBlank(lineData.getString(0)) && lineData.size() > 5) {
            AtomicInteger ruleDataIndex = new AtomicInteger(0);
            String employeeCode = lineData.getString(5);

            if(employeeMap.containsKey(employeeCode)){
                addErrorData(context,lineData.getIntValue(0),StrUtil.format("工号{}存在重复数据",employeeCode));
            }else{
                employeeMap.put(employeeCode,lineData.getString(4));
            }
            ChannelApplicationReferrerEntity referrerEntity = SettlementEmployeeHelper.getReferrerEntityByEmployeeCode(employeeCode);
            if(referrerEntity == null){
                addErrorData(context,lineData.getIntValue(0),StrUtil.format("工号{}对应的信息不存在",employeeCode));
            }else if(!Objects.equals(referrerEntity.getReferrerName(),lineData.getString(4))){
                addErrorData(context,lineData.getIntValue(0),StrUtil.format("工号{}对应的姓名{}与导入的姓名{}不一致",employeeCode,referrerEntity.getReferrerName(),lineData.getString(4)));
            }

            lineData.stream().skip(6).forEach(x -> {
                if (x != null) {

                    SubjectDataDynamicInfo bean = new SubjectDataDynamicInfo();
                    bean.setSheetIndex(context.getCurrentSheet().getSheetNo());
                    bean.setSheetName(context.getCurrentSheet().getSheetName());
                    bean.setRowNum(context.getCurrentRowNum());
                    // 申请信息
                    bean.setApplyCode(dataDynamicBase.getApplyCode());
                    bean.setCostSettlementCycle(dataDynamicBase.getCostSettlementCycle());
                    bean.setUserName(dataDynamicBase.getUserName());
                    // 结算机构
                    bean.setSerialNumber(lineData.getIntValue(0));
                    if (!settlementInstitutionMap.containsKey(lineData.getString(1))) {
                        addErrorData(context,bean.getSerialNumber(),StrUtil.format("结算机构信息不支持"));
                        //throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("结算机构信息不支持，结算机构名称:{}", lineData.getString(1))));
                    }
                    bean.setSettlementInstitution(settlementInstitutionMap.get(lineData.getString(1)).getSettlementInstitution());
                    bean.setSettlementInstitutionName(lineData.getString(1));
                    // 区域 + 机构信息
                    OrganizationCache region = SettlementEmployeeHelper.getOrganizationByRegionName(lineData.getString(2));
                    if (region == null) {
                        log.warn("区域信息不存在，区域名称:{}",lineData.getString(2));
                        //throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("区分信息不存在，区域名称:{}", lineData.getString(2))));
                    }else {
                        bean.setRegionCode(region.getRegionCode());
                        bean.setRegionName(lineData.getString(2));
                    }

                    OrganizationCache org = SettlementEmployeeHelper.getOrganizationByOrgName(lineData.getString(3));
                    if (org == null) {
                        addErrorData(context,bean.getSerialNumber(),StrUtil.format("分支机构信息不存在，机构名称:{}", lineData.getString(3)));
                        //throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("分支机构信息不存在，区域名称:{}", lineData.getString(3))));
                    }else {
                        bean.setOrgCode(org.getBranchCode());
                        bean.setOrgName(lineData.getString(3));
                    }

                    if (StringUtils.isBlank(lineData.getString(4))) {
                        addErrorData(context,bean.getSerialNumber(),StrUtil.format("缺少发放对象姓名"));
                        //throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("发放名单序号={}，缺少发放对象姓名", lineData.getString(0))));
                    }
                    if (StringUtils.isBlank(lineData.getString(5))) {
                        addErrorData(context,bean.getSerialNumber(),StrUtil.format("缺少发放对象工号"));
                        //throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("发放名单序号={}，缺少发放对象工号", lineData.getString(0))));
                    }
                    bean.setEmployeeName(lineData.getString(4));
                    bean.setEmployeeCode(lineData.getString(5));

                    // 扩展动态科目信息
                    if (!dynamicRuleRow.containsKey(ruleDataIndex.get() + 6)) {
                        addErrorData(context,bean.getSerialNumber(),StrUtil.format("无法识别动态科目信息"));
                        //throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("发放名单序号={}，无法识别动态科目信息", lineData.getString(0))));
                    }
                    SubjectDataDynamicRule dataDynamicRule = dynamicRuleRow.get(ruleDataIndex.get() + 6);
                    bean.setDynamicSubjectCode(dataDynamicRule.getDynamicSubjectCode());
                    bean.setDynamicSubjectName(dataDynamicRule.getDynamicSubjectName());

                    try {
                        if(!validateAmount(String.valueOf(x))){
                            addErrorData(context,bean.getSerialNumber(),StrUtil.format("{}的金额格式不正确",dataDynamicRule.getDynamicSubjectName()));
                        }
                        bean.setDynamicSubjectCash(new BigDecimal(String.valueOf(x)));
                    } catch (Exception e) {
                        addErrorData(context,bean.getSerialNumber(),StrUtil.format("{}的金额格式不正确",dataDynamicRule.getDynamicSubjectName()));
                        //throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("发放名单序号={}，发放对象金额={}，金额格式不正确", lineData.getString(0), x)));
                    }
                    insertData.add(bean);
                }
                ruleDataIndex.getAndIncrement();
            });
        }




        if (insertData.size() >= 800) {
            doSomething(context);
            insertData = new ArrayList<>();
            log.info("清空存储data");
        }
        //防止异常数据过大，超过阀值，则写入一部分，并且设置haveErrorData=true
        if(CollectionUtils.isNotEmpty(errorData.values()) && errorData.size()>=800){
            //入库
            settlementCostDynamicSubjectErrorDataService.saveList(errorData.values().stream().collect(Collectors.toList()));
            errorData = new HashMap<>();
            haveErrorData = Boolean.TRUE;
        }
    }

    /**
     * 根据业务自行实现该方法
     */
    private void doSomething(AnalysisContext context) {
        if(haveErrorData || CollectionUtils.isNotEmpty(errorData.values())){
            log.info("动态科目-{}存在异常数据",context.getCurrentSheet().getSheetName());
            return ;
        }
        log.info("数据写入数据库，写入条目={}", insertData.size());
        settlementCostSubjectDataDynamicInfoService.saveList(insertData);
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if(haveErrorData || CollectionUtils.isNotEmpty(errorData.values())){
            if(CollectionUtils.isNotEmpty(errorData.values())){
                log.info("存在未完成写入的异常数据，执行写入，写入条目={}", errorData.size());
                settlementCostDynamicSubjectErrorDataService.saveList(errorData.values().stream().collect(Collectors.toList()));
                haveErrorData = Boolean.TRUE;
            }
            log.info("完成异常数据写入....");
            return ;
        }

        if (!insertData.isEmpty()) {
            log.info("存在未完成写入的数据，执行写入，写入条目={}", insertData.size());
            settlementCostSubjectDataDynamicInfoService.saveList(insertData);
        }
        log.info("完成数据写入....");
    }

    protected void addErrorData(AnalysisContext context,Integer serialNumber,String errorMsg){
        addErrorData(context.getCurrentSheet().getSheetNo(),context.getCurrentSheet().getSheetName(),context.getCurrentRowNum(),serialNumber,errorMsg);
    }

    protected void addErrorData(Integer sheetIndex,String sheetName,Integer rowNum,Integer serialNumber,String errorMsg){
        if(errorData.containsKey(rowNum)){
            errorData.get(rowNum).addErrorMessage(errorMsg);
        }else {
            SubjectDataDynamicErrorBase errorBase = new SubjectDataDynamicErrorBase();
            errorBase.setSheetIndex(sheetIndex);
            errorBase.setSheetName(Objects.isNull(sheetName)?"sheet"+sheetIndex:sheetName);
            errorBase.setRowNum(rowNum);
            errorBase.setSerialNumber(serialNumber);
            errorBase.setApplyCode(dataDynamicBase.getApplyCode());
            errorBase.setUserName(dataDynamicBase.getUserName());
            errorBase.setCostSettlementCycle(dataDynamicBase.getCostSettlementCycle());
            errorBase.addErrorMessage(errorMsg);
            errorData.put(rowNum,errorBase);
        }
    }

    @Override
    public DynamicReadData<SubjectDataDynamicInfo> getReadData() {

        DynamicReadData readData = new DynamicReadData();
        readData.setErrorData(errorData.values().stream().collect(Collectors.toList()));
        readData.setInsertData(null);
        readData.setHaveErrorData(haveErrorData);
        return readData;
    }
}
