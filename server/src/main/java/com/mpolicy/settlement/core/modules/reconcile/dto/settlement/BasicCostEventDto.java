package com.mpolicy.settlement.core.modules.reconcile.dto.settlement;

import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 支出端事件信息
 * @date 2023/11/26 5:26 下午
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BasicCostEventDto implements Serializable {
    /**
     * 事件push编码
     */
    private String pushEventCode;


    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 事件名称
     */
    private String eventDesc;

    /**
     * 业务编码
     */
    private String eventBusinessCode;

    /**
     * 保单中心合同编号
     */
    private String contractCode;

    /**
     * 事件来源 保单中心、协议管理、车险佣金
     */
    private String eventSource;

    /**
     * 签约类型 1:小鲸业务、2:非小鲸业务
     */
    private Integer businessSignType = 1;
    /**
     * 业务请求事件报文
     */
    private String eventRequest;
    /**
     * 科目
     */
    CostSubjectEnum subjectEnum;
    /**
     * 事件类型
     */
    SettlementEventTypeEnum eventType;

}
