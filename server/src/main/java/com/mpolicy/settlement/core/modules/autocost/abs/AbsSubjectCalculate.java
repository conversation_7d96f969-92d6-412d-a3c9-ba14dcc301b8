package com.mpolicy.settlement.core.modules.autocost.abs;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Maps;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.redis.redisson.RedissLockUtil;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.common.SubjectCalculateBasicService;
import com.mpolicy.settlement.core.modules.autocost.dto.*;
import com.mpolicy.settlement.core.modules.autocost.dto.calculate.CostSubjectCalculateRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.calculate.SubjectCalculateRequest;
import com.mpolicy.settlement.core.modules.autocost.dto.data.CostSubjectDataRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataPolicyComm;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataRequest;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataResponse;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.*;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostLogEntity;
import com.mpolicy.settlement.core.modules.autocost.enums.AccountFlagEnum;
import com.mpolicy.settlement.core.modules.autocost.factory.SubjectDataFactory;
import com.mpolicy.settlement.core.modules.autocost.handler.SubjectCalculateHandler;
import com.mpolicy.settlement.core.modules.autocost.handler.SubjectDataQueryHandler;
import com.mpolicy.settlement.core.modules.autocost.helper.SubjectDataHelper;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.utils.LambdaUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 【自动结算】科目计算处理抽象
 *
 * <AUTHOR>
 * @since 2023-10-22 12:50
 */
@Slf4j
public abstract class AbsSubjectCalculate<T extends SubjectCalculateRequest<?>> extends SubjectCalculateBasicService implements SubjectCalculateHandler {

    protected static Integer MAX_HANDLER_CALCULATE_EMPLOYEE_SIZE = 50;

    public  static BigDecimal DEFAULT_GRANT_RATE= new BigDecimal("100");
    /**
     * 科目计算处理模版模式
     * 1、xxxx
     * 2、xxx
     *
     * <AUTHOR>
     * @since 2023/10/22 17:51
     */
    @Override
    public final CostSubjectCalculateRecord handle(String programmeCode,String costSettlementCycle,boolean force) {
        CostSubjectEnum subject= getSubjectDataEnum();
        boolean lock = RedissLockUtil.tryLock(StrUtil.format("{}:{}", Constant.SUBJECT_CALCULATE, subject.getCode()), 200, -1);
        if (!lock) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("当前科目={} 正在执行数据计算，请稍后再试", subject.getName())));
        }
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        log.info("执行科目结算计算，运算科目={} ", subject.getName());
        CostSubjectInfo subjectInfo = new CostSubjectInfo();
        // 构建生成科目结算数据明细的记录
        CostSubjectCalculateRecord record = new CostSubjectCalculateRecord();
        record.setCostSettlementCycle(costSettlementCycle);
        record.setSubjectCode(subject.getCode());
        record.setProgrammeCode(programmeCode);
        record.setDocumentCode(generateDocumentCode());
        try {
            // 1 判断方案信息是否正常
            subjectInfo = getCostSubjectInfo(subject);
            if (subjectInfo.getSubjectStatus() != 1) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("科目状态异常，科目={}", subject.getName())));
            }
            // 2、验证数据是否抽取完成
            CostSubjectDataRecord costSubjectDataRecord = SubjectDataHelper.checkSubjectDataSuccess(costSettlementCycle, subject.getCode());
            if (Objects.isNull(costSubjectDataRecord)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("数据抽取未完成，无法进行科目计算，周期={} 科目={}", costSettlementCycle, subject.getName())));
            }

            if(force){
                CostSubjectCalculateRecord oldRecord = getSubjectCalculateRecord(record.getCostSettlementCycle(),record.getSubjectCode());
                if(oldRecord!=null) {
                    int resetSize = resetSubjectCalculate(costSettlementCycle,oldRecord.getDocumentCode());
                }
            }else {
                // 非强制生成系统需要判断是否存在成功记录，存在不执行
                boolean isSuccess = checkSubjectCalculateSuccess(costSettlementCycle, subject.getCode());
                if (isSuccess) {
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("无法再次生成，存在未结算数据，周期={} 科目={}", costSettlementCycle, subject.getName())));
                }
            }

            // 4、生成初始化科目计算记录
            record.setSubjectCalculateStatus(0);
            record.setSubjectCalculateDesc(Constant.DEFAULT_INIT);
            saveSubjectCalculateRecord(record);

            // 5、科目计算
            handleCostCalculate(record);



            record.setSubjectCalculateStatus(1);
            record.setSubjectCalculateDesc(Constant.DEFAULT_SUCCESS);
        }catch (GlobalException g) {
            SettlementCostLogEntity logEntity = SettlementCostLogEntity.builder()
                    .costBusinessType("SUBJECT_CALCULATE")
                    .businessCode(subject.getCode())
                    .businessName(subject.getName())
                    .businessKey(costSettlementCycle)
                    .businessExceptionCode(String.valueOf(g.getCode()))
                    .businessExceptionType("GlobalException")
                    .businessData(StrUtil.format("所属科目={} 所属周期={}", subject.getName(), costSettlementCycle, JSON.toJSONString(subjectInfo)))
                    .businessDesc(g.getMessage())
                    .build();
            saveCostLog(logEntity);
            record.setSubjectCalculateStatus(-1);
            record.setSubjectCalculateDesc(StrUtil.format("出现自定义异常：{}", g.getMessage()));

        }catch (Exception e) {
            e.printStackTrace();
            log.warn("{}计算异常:{}",getSubjectDataEnum().getName(),e);
            SettlementCostLogEntity logEntity = SettlementCostLogEntity.builder()
                    .costBusinessType("SUBJECT_CALCULATE")
                    .businessCode(subject.getCode())
                    .businessName(subject.getName())
                    .businessKey(costSettlementCycle)
                    .businessExceptionCode("未定义")
                    .businessExceptionType("Exception")
                    .businessData(StrUtil.format("所属科目={} 所属周期={} 科目信息={}", subject.getName(), costSettlementCycle, JSON.toJSONString(subjectInfo)))
                    .businessDesc(ExceptionUtil.stacktraceToString(e))
                    .build();
            saveCostLog(logEntity);
            record.setSubjectCalculateStatus(-1);
            record.setSubjectCalculateDesc(StrUtil.format("出现未知异常，请查询日志记录表核查"));
        } finally {
            RedissLockUtil.unlock(StrUtil.format("{}:{}", Constant.SUBJECT_CALCULATE, subject.getCode()));
            log.info("计算完成，释放锁完成....");
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        record.setFinishMillis((int)millis);
        //更新计算状态
        updateSubjectCalculateRecordStatus(record);

        if (record.getSubjectCalculateStatus() != 1) {

            int resetSize = resetSubjectCalculate(costSettlementCycle,record.getDocumentCode());
            log.info("出现异常，进行补充重置，所属周期={} 科目={} 重置数量={}", costSettlementCycle, subject.getName(), resetSize);
        } else {
            // 如果成功的话，写入客户记录表
            CostSubjectRecord subjectRecord = new CostSubjectRecord();
            subjectRecord.setCostSettlementCycle(costSettlementCycle);
            subjectRecord.setSubjectCode(subject.getCode());
            subjectRecord.setSubjectCostSize(record.getSubjectCalculateSize());
            subjectRecord.setSubjectCostCash(record.getSubjectCalculateCash());
            subjectRecord.setSubjectCostFinishTime(new Date());
            subjectService.saveOrUpdateCostSubjectRecord(subjectRecord);
        }
        log.info("构建科目={} 科目计算完成，耗时={}", subject.getName(), millis);
        return record;
    }

    protected CostAutoRecord initCostAutoRecord(String programmeCode,String documentCode,String costSettlementCycle){
        Date date = DateUtil.parse(costSettlementCycle,"yyyyMM");
        return CostAutoRecord.builder()
                .autoCostCode(generateAutoCostCode())
                .subjectCode(getSubjectDataEnum().getCode())
                .subjectName(getSubjectDataEnum().getName())
                //发放科目默认与实际科目一样，动态科目时，在业务中重新设置
                .sendSubjectCode(getSubjectDataEnum().getCode())
                .sendSubjectName(getSubjectDataEnum().getName())
                .dynamicFlag(0)
                .costSettlementTime(new Date())
                .settlementDate(DateUtil.beginOfMonth(new Date()))
                //数据统计月，计算周期-1
                .statisticsMonth(DateUtil.format(DateUtil.offsetMonth(date,-1),"yyyy-MM"))
                //默认记录无需入账
                .accountFlag(AccountFlagEnum.NO_NEED.getCode())
                .programmeCode(programmeCode)
                .documentCode(documentCode)
                .confirmStatus(0)
                .build();
    }


    /**
     * 规则处理
     * @param ruleConfigMap
     * @param params
     * @param costAutoRecord
     */
    protected void execScript(Map<String, CostSubjectCalculateRuleConfig> ruleConfigMap, Map<String,Object> params, CostAutoRecord costAutoRecord){
        if(CollectionUtils.isEmpty(ruleConfigMap)){
            return ;
        }
        // todo 后续可以增加规则脚本计算顺序
        for(String key : ruleConfigMap.keySet()){
            try {
                CostSubjectCalculateRuleConfig config = ruleConfigMap.get(key);
                if(Objects.equals(config.getScriptFlag(), Constant.SCRIPT_FLAG_Y)) {
                    costSubjectCalculateRuleService.setFactorValueByScript(costAutoRecord, config, params);
                }else{
                    costSubjectCalculateRuleService.setFactorValueByConfig(costAutoRecord,config);
                }
            }catch (Exception g){
                log.warn("脚本规则运行异常",g);
                //todo
                //throw new GlobalException();
            }
        }
    }



    /**
     * 根据结算记录获取督导分摊明细数据
     * @param costAutoRecordList
     * @param costSettlementCycle
     * @return
     */
    protected Map<String, List<SupervisorProductQbiInfo>> getSupervisorProductQbiInfoMap(List<CostAutoRecord> costAutoRecordList, String costSettlementCycle) {
        List<String> employeeCodes = costAutoRecordList.stream().map(CostAutoRecord::getSendObjectCode).collect(Collectors.toList());
        List<SupervisorProductQbiInfo> dtos = settlementCostQbiService.listSupervisorProductQbi(employeeCodes,costSettlementCycle);
        return LambdaUtils.groupBy(dtos, supervisorProductQbiInfo -> getItemMapKey(supervisorProductQbiInfo.getEmployeeCode(),supervisorProductQbiInfo.getOrgCode(),supervisorProductQbiInfo.getSettlementOrgCode()));

    }
    /**
     * 根据结算记录获取分支分摊明细数据
     * @param costAutoRecordList
     * @param costSettlementCycle
     * @return
     */
    protected Map<String, List<OrgProductQbiInfo>> getOrgProductQbiInfoMap(List<CostAutoRecord> costAutoRecordList, String costSettlementCycle) {
        List<String> orgCodes = costAutoRecordList.stream().map(CostAutoRecord::getObjectOrgCode).distinct().collect(Collectors.toList());
        List<OrgProductQbiInfo> dtos = settlementCostQbiService.listOrgProductQbi(orgCodes,costSettlementCycle);
        Map<String,List<OrgProductQbiInfo>> orgMap =  LambdaUtils.groupBy(dtos, orgProductQbiInfo -> getOrgItemMapKey(orgProductQbiInfo.getOrgCode(),orgProductQbiInfo.getSettlementOrgCode()));
        Map<String,List<OrgProductQbiInfo>> personMap = Maps.newHashMap();
        for(CostAutoRecord record: costAutoRecordList){
            personMap.put(getItemMapKey(record.getSendObjectCode(),record.getObjectOrgCode(),record.getSettlementInstitution()),orgMap.get(getOrgItemMapKey(record.getObjectOrgCode(),record.getSettlementInstitution())));
        }
        return personMap;
    }

    protected List<SubjectDataPolicyComm> getPolicyCommSourceData(String costSettlementCycle){
        SubjectDataQueryHandler<SubjectDataRequest<String>, List<SubjectDataPolicyComm>> queryHandler = SubjectDataFactory.getSubjectDataQueryHandler(getSubjectDataEnum());
        SubjectDataRequest<String> request = new SubjectDataRequest<>();
        request.setCostSettlementCycle(costSettlementCycle);
        SubjectDataResponse<List<SubjectDataPolicyComm>> result = queryHandler.getSubjectData(request);
        if(Objects.nonNull(result)){
            return result.getData()!=null?result.getData(): Collections.EMPTY_LIST;
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 计算发放金额
     * @param promotion      推广费
     * @param commissionRate 提成比例（默认为100）
     * @param grantRate      发放比例
     * @return
     */
    @Deprecated
    protected BigDecimal calcGrantAmt(BigDecimal promotion, BigDecimal commissionRate, BigDecimal grantRate){
        if(promotion == null || grantRate == null){
            return null;
        }

        if(commissionRate == null){
            commissionRate = new BigDecimal("100");
        }
        BigDecimal commission = PolicySettlementUtils.calcAmtByPercent(promotion,commissionRate,2);
        return PolicySettlementUtils.calcAmtByPercent(commission,grantRate,2);
    }

    /**
     * key
     * @param employeeCode
     * @param orgCode
     * @param settlementInstitution
     * @return
     */
    public String getItemMapKey(String employeeCode,String orgCode,String settlementInstitution){
        return StrUtil.format("{}_{}_{}", StringUtil.isNotBlank(employeeCode)?employeeCode:"",
                StringUtil.isNotBlank(orgCode)?orgCode:"",StringUtil.isNotBlank(settlementInstitution)?settlementInstitution:"");
    }

    /**
     * key
     * @param employeeCode
     * @param orgCode
     * @return
     */
    public String getEmployeeItemMapKey(String employeeCode,String orgCode){
        return StrUtil.format("{}_{}", StringUtil.isNotBlank(employeeCode)?employeeCode:"",
                StringUtil.isNotBlank(orgCode)?orgCode:"");
    }

    /**
     * key
     * @param employeeCode
     * @param settlementInstitution
     * @return
     */
    public String getSupervisorItemMapKey(String employeeCode,String settlementInstitution){
        return StrUtil.format("{}_{}", StringUtil.isNotBlank(employeeCode)?employeeCode:"",
                StringUtil.isNotBlank(settlementInstitution)?settlementInstitution:"");
    }

    /**
     * key
     * @param orgCode
     * @param employeeCode
     * @param settlementInstitution
     * @return
     */
    public String getSupervisorItemMapKeyV2(String orgCode,String employeeCode,String settlementInstitution){
        return StrUtil.format("{}_{}_{}",StringUtil.isNotBlank(orgCode)?orgCode:"", StringUtil.isNotBlank(employeeCode)?employeeCode:"",
                StringUtil.isNotBlank(settlementInstitution)?settlementInstitution:"");
    }

    /**
     * key
     * @param orgCode
     * @param settlementInstitution
     * @return
     */
    public String getOrgItemMapKey(String orgCode,String settlementInstitution){
        return StrUtil.format("{}_{}", StringUtil.isNotBlank(orgCode)?orgCode:"",
                StringUtil.isNotBlank(settlementInstitution)?settlementInstitution:"");
    }


    /**
     * 计算提成金额，默认保留6位小数
     * @param amt
     * @param rate
     * @return
     */
    protected BigDecimal calcCommissionAmt(BigDecimal amt,BigDecimal rate){
        if(amt == null || rate == null){
            return null;
        }
        return PolicySettlementUtils.calcAmtByPercent(amt,rate,6);
    }

    /**
     * 计算发放金额，默认保留2位小数
     * @param amt
     * @param rate
     * @return
     */
    protected BigDecimal calcGrantAmt(BigDecimal amt,BigDecimal rate){
        if(amt == null || rate == null){
            return null;
        }
        return PolicySettlementUtils.calcAmtByPercent(amt,rate,2);
    }



    /**
     * 计算发放金额，默认保留2位小数
     * @param amt
     * @param rate 利率为百分比
     * @return
     */
    protected BigDecimal calcGrantAmt2ByPercent(BigDecimal amt,BigDecimal rate){
        if(amt == null || rate == null){
            return null;
        }
        return amt.multiply(rate.multiply(new BigDecimal("0.01"))).setScale(2,BigDecimal.ROUND_HALF_UP);
    }


    /**
     * 获取员工当前结算周期某个月的续期率
     * @param costSettlementCycle
     * @param renewalRateCycle
     * @param objectCodeList
     * @return
     */
    protected Map<String, EmployeeQbiRenewalRate> builderEmployeeRenewalRateMap(String costSettlementCycle, String renewalRateCycle, List<String> objectCodeList) {
        List<EmployeeQbiRenewalRate> qbiResult = settlementCostQbiService.listEmployeeQbiRenewalRate(objectCodeList, renewalRateCycle, costSettlementCycle);
        Map<String, EmployeeQbiRenewalRate> result = qbiResult.stream().collect(Collectors.toMap(EmployeeQbiRenewalRate::getEmployeeCode, Function.identity(), (k1, k2) -> k1));
        return result;
    }

    /**
     * 获取督导当前结算周期某个月的续期率
     * @param costSettlementCycle
     * @param renewalRateCycle
     * @param objectCodeList
     * @return
     */
    protected Map<String, SupervisorQbiRenewalRate> builderSupervisorRenewalRateMap(String costSettlementCycle, String renewalRateCycle, List<String> objectCodeList) {
        List<SupervisorQbiRenewalRate> qbiResult = settlementCostQbiService.listSupervisorQbiRenewalRate(objectCodeList, renewalRateCycle, costSettlementCycle);
        Map<String, SupervisorQbiRenewalRate> result = qbiResult.stream().collect(Collectors.toMap(SupervisorQbiRenewalRate::getEmployeeCode, Function.identity(), (k1, k2) -> k1));
        return result;
    }
    /**
     * 获取督导当前结算周期某个月的续期率
     * @param costSettlementCycle
     * @param renewalRateCycle
     * @param orgCodeList
     * @return
     */
    protected Map<String, OrgQbiRenewalRate> builderOrgRenewalRateMap(String costSettlementCycle, String renewalRateCycle, List<String> orgCodeList) {
        List<OrgQbiRenewalRate> qbiResult = settlementCostQbiService.listOrgQbiRenewalRate(orgCodeList, renewalRateCycle, costSettlementCycle);
        Map<String, OrgQbiRenewalRate> result = qbiResult.stream().collect(Collectors.toMap(OrgQbiRenewalRate::getOrgCode, Function.identity(), (k1, k2) -> k1));
        return result;
    }

    protected String generateAutoCostCode(){
        return PolicySettlementUtils.createCodeLastNumber("CAC");
    }

    protected String generateDocumentCode(){
        return PolicySettlementUtils.createCodeLastNumber("DC");
    }




    /**
     * 科目处理
     *
     * @return java.lang.String
     * <AUTHOR>
     * @since 2023/10/22 17:54
     */
    public abstract String handleCostCalculate(CostSubjectCalculateRecord record);

    /**
     * 重置科目计算信息
     * @param costSettlementCycle
     * @return
     */
    public abstract Integer resetSubjectCalculate(String costSettlementCycle,String documentCode);


}