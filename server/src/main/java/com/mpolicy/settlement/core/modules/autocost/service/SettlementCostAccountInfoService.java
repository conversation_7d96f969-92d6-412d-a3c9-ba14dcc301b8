package com.mpolicy.settlement.core.modules.autocost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.autocost.dto.base.SettlementCostAccountInfo;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostAccountInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostQbiOrgProductEntity;

import java.util.List;

/**
 * 结算账户服务
 *
 * <AUTHOR>
 * @since 2024-05-31 3:34:32
 */
public interface SettlementCostAccountInfoService extends IService<SettlementCostAccountInfoEntity> {
    /**
     * 根据工号查询员工账户信息
     * @param sendObjectCodes
     * @return
     */
    List<SettlementCostAccountInfo> listBySendObjectCodes(List<String> sendObjectCodes);

    /**
     *
     * @param list
     * @return
     */
    int saveList(List<SettlementCostAccountInfo> list);
    /**
     * 批量更新账户金额，
     * 注意 ：当这个表作为真正的账户，不能这样更新
     * @param list
     * @return
     */
    int updateCostAccountInfoList(List<SettlementCostAccountInfo> list);

    /**
     * 更新账户信息
     * @param accountInfo
     * @return
     */
    int updateCostAccountInfo(SettlementCostAccountInfo accountInfo);
}

