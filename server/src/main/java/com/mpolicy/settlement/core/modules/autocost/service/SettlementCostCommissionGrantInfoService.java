package com.mpolicy.settlement.core.modules.autocost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostCommissionGrantInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostUploadPcoInfoEntity;

import java.util.List;

public interface SettlementCostCommissionGrantInfoService extends IService<SettlementCostCommissionGrantInfoEntity> {

    int saveList(List<SettlementCostCommissionGrantInfoEntity> list);
}
