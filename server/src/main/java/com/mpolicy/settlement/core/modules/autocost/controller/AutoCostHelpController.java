package com.mpolicy.settlement.core.modules.autocost.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.dto.CostProgrammeRecordInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.data.CostSubjectDataRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.*;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostProgrammeInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostProgrammeRecordEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostProgrammeSubjectEntity;
import com.mpolicy.settlement.core.modules.autocost.enums.ConfirmStatusEnum;
import com.mpolicy.settlement.core.modules.autocost.factory.SubjectCalculateFactory;
import com.mpolicy.settlement.core.modules.autocost.factory.SubjectDataFactory;
import com.mpolicy.settlement.core.modules.autocost.handler.SubjectCalculateHandler;
import com.mpolicy.settlement.core.modules.autocost.handler.SubjectDataBuilderHandler;
import com.mpolicy.settlement.core.modules.autocost.service.*;
import com.mpolicy.settlement.core.modules.autocost.utils.AutoCostUtils;
import com.mpolicy.web.common.annotation.PassToken;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 结算帮助服务 controller
 *
 * <AUTHOR>
 * @since 2022-11-18 12:30
 */
@RestController
@RequestMapping("/settlement/auto_cost/help")
@Api(tags = "结算帮助服务")
@Slf4j
public class AutoCostHelpController {

    @Autowired
    @Qualifier("simpleTaskExecutor")
    private AsyncTaskExecutor taskExecutor;

    @Autowired
    private SettlementCostQbiService settlementCostQbiService;

    @Autowired
    private SettlementCostProgrammeInfoService settlementCostProgrammeInfoService;


    @Autowired
    private SettlementCostProgrammeSubjectService settlementCostProgrammeSubjectService;

    @Autowired
    private SubjectDataService subjectDataService;

    @Autowired
    private CostProgrammeService costProgrammeService;

    @Autowired
    private SettlementCostProgrammeRecordService settlementCostProgrammeRecordService;


    /**
     * 手动触发方案结佣
     */
    @ApiOperation(value = "手动触发方案结佣", notes = "手动触发方案结佣")
    @PostMapping("/cost_programme_job")
    @PassToken
    public Result<String> costProgrammeJob(@RequestParam @ApiParam(name = "programmeCode", value = "方案编码") String programmeCode,
                                           @RequestParam @ApiParam(name = "settlementCycle", value = "指定结算周期") String settlementCycle) {
        // 1 获取生效中的方案集合
        SettlementCostProgrammeInfoEntity costProgramme = Optional.ofNullable(settlementCostProgrammeInfoService.lambdaQuery()
                .eq(SettlementCostProgrammeInfoEntity::getProgrammeCode, programmeCode)
                .eq(SettlementCostProgrammeInfoEntity::getProgrammeStatus, 1)
                .one())
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("方案编码不存在")));

        log.info("方案信息：{}", JSON.toJSONString(costProgramme));
        // 2 处理方案
        // 2-1 获取方案配置的科目
        List<SettlementCostProgrammeSubjectEntity> costProgrammeSubject = settlementCostProgrammeSubjectService.lambdaQuery()
                .eq(SettlementCostProgrammeSubjectEntity::getProgrammeCode, costProgramme.getProgrammeCode())
                .orderByAsc(SettlementCostProgrammeSubjectEntity::getOrderNumber)
                .list();
        // 2-2 生成方案结算周期
        String costSettlementCycle = StringUtils.isNotBlank(settlementCycle) ? settlementCycle : AutoCostUtils.costSettlementMonthSnapshot();

        //必须依照月份顺序完成结算,验证结算前是否还有未确认结算的
        List<SettlementCostProgrammeRecordEntity> oldNotConfirmed =  settlementCostProgrammeRecordService.lambdaQuery().eq(SettlementCostProgrammeRecordEntity::getProgrammeCode,costProgramme.getProgrammeCode())
                .lt(SettlementCostProgrammeRecordEntity::getCostSettlementCycle,costSettlementCycle)
                .ne(SettlementCostProgrammeRecordEntity::getConfirmStatus, ConfirmStatusEnum.CONFIRMED.getCode())
                .list();
        if(CollectionUtils.isNotEmpty(oldNotConfirmed)){
            String msg = StrUtil.format("必须依照月份顺序完成结算，结算周期{}前有未确认的结算任务{}",costSettlementCycle,JSON.toJSONString(oldNotConfirmed));
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(msg));
        }

        // 2-3 执行方案科目数据构建处理
        costProgrammeSubject.forEach(c -> {
            CostSubjectEnum costSubject = CostSubjectEnum.matchSearchCode(c.getSubjectCode());
            SubjectDataBuilderHandler builderDataHandler = SubjectDataFactory.getSubjectBuilderDataHandler(costSubject);
            CostSubjectDataRecord builderResult = builderDataHandler.handle(costSettlementCycle, true);
            if (builderResult.getSubjectDataStatus() == 1) {
                log.info("科目数据构建处理成功，科目={}，准备执行运算", costSubject.getName());
                SubjectCalculateHandler builderCalculateHandler = SubjectCalculateFactory.getSubjectCalculateHandler(costSubject);
                builderCalculateHandler.handle(c.getProgrammeCode(), costSettlementCycle, true);
            } else {
                log.info("科目数据构建处理失败，科目={} 错误摘要={}", costSubject.getName(), builderResult.getSubjectDataDesc());
            }
        });

        // 2-4 设置方案记录情况
        CostProgrammeRecordInfo recordInfo = new CostProgrammeRecordInfo();
        // 方案周期 + 方案编码
        recordInfo.setCostSettlementCycle(costSettlementCycle);
        recordInfo.setProgrammeCode(costProgramme.getProgrammeCode());

        /************科目方案数据处理状态 start***************/
        // 获取方案配置的客户编码集合
        List<String> costProgrammeSubjectCodeList = costProgrammeSubject.stream().map(SettlementCostProgrammeSubjectEntity::getSubjectCode).collect(Collectors.toList());
        // 2-5 获取方案数据范围生成状况
        List<CostSubjectDataRecord> costSubjectDataRecords = subjectDataService.querySubjectDataRecord(costSettlementCycle);
        // 2-5-1 【所有科目记录】 + 【成功科目记录】+ 【失败科目记录】
        List<String> allSubjectDataRecord = costSubjectDataRecords.stream().map(CostSubjectDataRecord::getSubjectCode).distinct().collect(Collectors.toList());
        List<String> successSubject = costSubjectDataRecords.stream().filter(f -> f.getSubjectDataStatus() == 1).map(CostSubjectDataRecord::getSubjectCode).distinct().collect(Collectors.toList());
        List<String> errorSubject = costSubjectDataRecords.stream().filter(f -> f.getSubjectDataStatus() != 1).map(CostSubjectDataRecord::getSubjectCode).distinct().collect(Collectors.toList());
        // 2-5-2 判断是否存在未完成的抽数的科目
        Collection<String> subtract = CollUtil.subtract(costProgrammeSubjectCodeList, allSubjectDataRecord);
        if (subtract.isEmpty()) {
            // 如果【成功科目记录】 ==  所需的科目数据
            if (CollUtil.subtract(costProgrammeSubjectCodeList, successSubject).isEmpty()) {
                recordInfo.setSubjectDataStatus(1);
                recordInfo.setSubjectDataDesc(StrUtil.format("所有科目数据采集完成：科目信息={}", costSubjectDataRecords.stream().map(CostSubjectDataRecord::getSubjectCode).distinct().collect(Collectors.toList())));
            } else {
                recordInfo.setSubjectDataStatus(0);
                recordInfo.setSubjectDataDesc(StrUtil.format("存在未完成科目数据采集：未完成科目信息={}", JSON.toJSONString(CollUtil.subtract(errorSubject, successSubject))));
            }
        } else {
            recordInfo.setSubjectDataStatus(0);
            recordInfo.setSubjectDataDesc(StrUtil.format("存在客户数据采集差异：差异科目信息={}", JSON.toJSONString(subtract)));
        }
        /************科目方案数据处理状态 end***************/

        // TODO: 2023/11/7 根据方案周期 + 方案编码获取运算的状态 zhangjian
        // 2-6 保存或修改方案记录信息
        costProgrammeService.saveOrUpdateSettlementCostProgrammeRecord(recordInfo);
        return Result.success(Constant.DEFAULT_SUCCESS);
    }


    /**
     * 取员工指标数据
     *
     * @param employeeCode 员工编码
     * @return com.mpolicy.common.result.Result<com.mpolicy.settlement.core.modules.autocost.dto.qbi.EmployeeQbiInfo>
     * <AUTHOR>
     * @since 2023/11/16 20:01
     */
    @ApiOperation(value = "取员工指标数据", notes = "取员工指标数据")
    @PostMapping("/qbi/employee")
    @PassToken
    public Result<EmployeeQbiInfo> queryEmployeeQbi(@RequestParam @ApiParam(name = "employeeCode", value = "员工编码") String employeeCode) {
        return Result.success(settlementCostQbiService.queryEmployeeQbi(employeeCode, AutoCostUtils.costSettlementMonthSnapshot()));
    }

    /**
     * 取员工集合指标数据
     *
     * @param employeeCodeList 员工编码集合
     * @return com.mpolicy.common.result.Result<java.util.List < com.mpolicy.settlement.core.modules.autocost.dto.qbi.EmployeeQbiInfo>>
     * <AUTHOR>
     * @since 2023/11/16 20:01
     */
    @ApiOperation(value = "取员工集合指标数据", notes = "取员工集合指标数据")
    @PostMapping("/qbi/employee/list")
    @PassToken
    public Result<List<EmployeeQbiInfo>> listEmployeeQbi(@RequestParam @ApiParam(name = "employeeCodeList", value = "员工编码集合") List<String> employeeCodeList) {
        return Result.success(settlementCostQbiService.listEmployeeQbi(employeeCodeList, AutoCostUtils.costSettlementMonthSnapshot()));
    }

    /**
     * 取员工结算机构险种集合数据
     *
     * @param employeeCode 员工编码
     * @return com.mpolicy.common.result.Result<java.util.List < com.mpolicy.settlement.core.modules.autocost.dto.qbi.EmployeeProductQbiInfo>>
     * <AUTHOR>
     * @since 2023/11/16 20:01
     */
    @ApiOperation(value = "取员工结算机构险种集合数据", notes = "取员工结算机构险种集合数据")
    @PostMapping("/qbi/employee/product")
    @PassToken
    public Result<List<EmployeeProductQbiInfo>> queryEmployeeProductQbi(@RequestParam @ApiParam(name = "employeeCode", value = "员工编码") String employeeCode) {
        return Result.success(settlementCostQbiService.listEmployeeProductQbi(employeeCode, AutoCostUtils.costSettlementMonthSnapshot()));
    }

    /**
     * 取督导指标数据
     *
     * @param employeeCode 督导员工编码
     * @return com.mpolicy.common.result.Result<com.mpolicy.settlement.core.modules.autocost.dto.qbi.SupervisorQbiInfo>
     * <AUTHOR>
     * @since 2023/11/16 20:01
     */
    @ApiOperation(value = "取督导指标数据", notes = "取督导指标数据")
    @PostMapping("/qbi/supervisor")
    @PassToken
    public Result<SupervisorQbiInfo> querySupervisorQbiInfo(@RequestParam @ApiParam(name = "employeeCode", value = "督导员工编码") String employeeCode) {
        return Result.success(settlementCostQbiService.querySupervisorQbiInfo(employeeCode, AutoCostUtils.costSettlementMonthSnapshot()));
    }

    /**
     * 取督导集合指标数据
     *
     * @param employeeCodeList 督导员工编码集合
     * @return com.mpolicy.common.result.Result<java.util.List < com.mpolicy.settlement.core.modules.autocost.dto.qbi.SupervisorQbiInfo>>
     * <AUTHOR>
     * @since 2023/11/16 20:01
     */
    @ApiOperation(value = "取督导集合指标数据", notes = "取督导集合指标数据")
    @PostMapping("/qbi/supervisor/list")
    @PassToken
    public Result<List<SupervisorQbiInfo>> listSupervisorQbiInfo(@RequestParam @ApiParam(name = "employeeCodeList", value = "督导员工编码集合") List<String> employeeCodeList) {
        return Result.success(settlementCostQbiService.listSupervisorQbiInfo(employeeCodeList, AutoCostUtils.costSettlementMonthSnapshot()));
    }

    /**
     * 取督导结算机构险种集合数据
     *
     * @param employeeCode 员工编码
     * @return com.mpolicy.common.result.Result<java.util.List < com.mpolicy.settlement.core.modules.autocost.dto.qbi.SupervisorProductQbiInfo>>
     * <AUTHOR>
     * @since 2023/11/16 20:01
     */
    @ApiOperation(value = "取督导结算机构险种集合数据", notes = "取督导结算机构险种集合数据")
    @PostMapping("/qbi/supervisor/product")
    @PassToken
    public Result<List<SupervisorProductQbiInfo>> querySupervisorProductQbi(@RequestParam @ApiParam(name = "employeeCode", value = "员工编码") String employeeCode) {
        return Result.success(settlementCostQbiService.listSupervisorProductQbi(employeeCode, AutoCostUtils.costSettlementMonthSnapshot()));
    }

    /**
     * 取机构指标数据
     *
     * @param orgCode 机构编码
     * @return com.mpolicy.common.result.Result<com.mpolicy.settlement.core.modules.autocost.dto.qbi.OrgQbiInfo>
     * <AUTHOR>
     * @since 2023/11/16 20:02
     */
    @ApiOperation(value = "取机构指标数据", notes = "取机构指标数据")
    @PostMapping("/qbi/org")
    @PassToken
    public Result<OrgQbiInfo> queryOrgQbi(@RequestParam @ApiParam(name = "orgCode", value = "机构编码") String orgCode) {
        return Result.success(settlementCostQbiService.queryOrgQbi(orgCode, AutoCostUtils.costSettlementMonthSnapshot()));
    }

    /**
     * 取机构集合指标数据
     *
     * @param orgCodeList 机构编码集合
     * @return com.mpolicy.common.result.Result<java.util.List < com.mpolicy.settlement.core.modules.autocost.dto.qbi.OrgQbiInfo>>
     * <AUTHOR>
     * @since 2023/11/16 20:02
     */
    @ApiOperation(value = "取机构集合指标数据", notes = "取机构集合指标数据")
    @PostMapping("/qbi/org/list")
    @PassToken
    public Result<List<OrgQbiInfo>> listOrgQbi(@RequestParam @ApiParam(name = "orgCodeList", value = "机构编码集合") List<String> orgCodeList) {
        return Result.success(settlementCostQbiService.listOrgQbi(orgCodeList, AutoCostUtils.costSettlementMonthSnapshot()));
    }

    /**
     * 取机构结算机构险种集合数据
     *
     * @param orgCode 机构编码
     * @return com.mpolicy.common.result.Result<java.util.List < com.mpolicy.settlement.core.modules.autocost.dto.qbi.OrgProductQbiInfo>>
     * <AUTHOR>
     * @since 2023/11/16 20:02
     */
    @ApiOperation(value = "取机构结算机构险种集合数据", notes = "取机构结算机构险种集合数据")
    @PostMapping("/qbi/org/product")
    @PassToken
    public Result<List<OrgProductQbiInfo>> queryOrgProductQbi(@RequestParam @ApiParam(name = "orgCode", value = "机构编码") String orgCode) {
        return Result.success(settlementCostQbiService.listOrgProductQbi(orgCode, AutoCostUtils.costSettlementMonthSnapshot()));
    }


    /**
     * [员工]继续率指标R13数据
     *
     * @param employeeCode 员工工号
     * @param bizMonth     结算月份
     * @return [员工]继续率指标R13数据
     * <AUTHOR>
     * @since 2023/12/16
     */
    @ApiOperation(value = "[员工]继续率指标R13数据", notes = "[员工]继续率指标R13数据")
    @PostMapping("/qbi/employee/renewal_rate/info")
    @PassToken
    public Result<EmployeeQbiRenewalRate> queryEmployeeQbiRenewalRate(@RequestParam @ApiParam(name = "employeeCode", value = "员工工号") String employeeCode,
                                                                      @RequestParam @ApiParam(name = "bizMonth", value = "结算月份") String bizMonth) {
        return Result.success(settlementCostQbiService.queryEmployeeQbiRenewalRate(employeeCode, bizMonth, AutoCostUtils.costSettlementMonthSnapshot()));
    }


    /**
     * [员工]继续率指标R13数据集合
     *
     * @param employeeCode 员工工号
     * @return [员工]继续率指标R13数据
     * <AUTHOR>
     * @since 2023/12/16
     */
    @ApiOperation(value = "[员工]继续率指标R13数据集合", notes = "[员工]继续率指标R13数据集合")
    @PostMapping("/qbi/employee/renewal_rate/list")
    @PassToken
    public Result<List<EmployeeQbiRenewalRate>> listEmployeeQbiRenewalRate(@RequestParam @ApiParam(name = "employeeCode", value = "员工工号") String employeeCode) {
        return Result.success(settlementCostQbiService.listEmployeeQbiRenewalRate(employeeCode, AutoCostUtils.costSettlementMonthSnapshot()));
    }

    /**
     * [督导]继续率指标R13数据
     *
     * @param supervisorCode 督导工号
     * @param bizMonth       结算月份
     * @return 督导继续率指标R13数据
     * <AUTHOR>
     * @since 2023/12/16
     */
    @ApiOperation(value = "[督导]继续率指标R13数据", notes = "[督导]继续率指标R13数据")
    @PostMapping("/qbi/supervisor/renewal_rate/info")
    @PassToken
    public Result<SupervisorQbiRenewalRate> querySupervisorQbiRenewalRate(@RequestParam @ApiParam(name = "supervisorCode", value = "督导工号") String supervisorCode,
                                                                          @RequestParam @ApiParam(name = "bizMonth", value = "结算月份") String bizMonth) {
        return Result.success(settlementCostQbiService.querySupervisorQbiRenewalRate(supervisorCode, bizMonth, AutoCostUtils.costSettlementMonthSnapshot()));
    }

    /**
     * [督导]继续率指标R13数据集合
     *
     * @param supervisorCode 督导工号
     * @return 督导继续率指标R13数据
     * <AUTHOR>
     * @since 2023/12/16
     */
    @ApiOperation(value = "[督导]继续率指标R13数据集合", notes = "[督导]继续率指标R13数据集合")
    @PostMapping("/qbi/supervisor/renewal_rate/list")
    @PassToken
    public Result<List<SupervisorQbiRenewalRate>> listSupervisorQbiRenewalRate(@RequestParam @ApiParam(name = "supervisorCode", value = "督导工号") String supervisorCode) {
        return Result.success(settlementCostQbiService.listSupervisorQbiRenewalRate(supervisorCode, AutoCostUtils.costSettlementMonthSnapshot()));
    }

    /**
     * [机构]继续率指标R13数据
     *
     * @param orgCode  分支机构编码
     * @param bizMonth 结算月份
     * @return 继续率指标R13数据
     * <AUTHOR>
     * @since 2023/12/16
     */
    @ApiOperation(value = "[机构]继续率指标R13数据", notes = "[机构]继续率指标R13数据")
    @PostMapping("/qbi/org/renewal_rate/info")
    @PassToken
    public Result<OrgQbiRenewalRate> queryOrgQbiRenewalRate(@RequestParam @ApiParam(name = "orgCode", value = "分支机构编码") String orgCode,
                                                            @RequestParam @ApiParam(name = "bizMonth", value = "结算月份") String bizMonth) {
        return Result.success(settlementCostQbiService.queryOrgQbiRenewalRate(orgCode, bizMonth, AutoCostUtils.costSettlementMonthSnapshot()));
    }


    /**
     * [机构]继续率指标R13数据集合
     *
     * @param orgCode 分支机构编码
     * @return 继续率指标R13数据
     * <AUTHOR>
     * @since 2023/12/16
     */
    @ApiOperation(value = "[机构]继续率指标R13数据集合", notes = "[机构]继续率指标R13数据集合")
    @PostMapping("/qbi/org/renewal_rate/list")
    @PassToken
    public Result<List<OrgQbiRenewalRate>> listOrgQbiRenewalRate(@RequestParam @ApiParam(name = "orgCode", value = "分支机构编码") String orgCode) {
        return Result.success(settlementCostQbiService.listOrgQbiRenewalRate(orgCode, AutoCostUtils.costSettlementMonthSnapshot()));
    }
}
