package com.mpolicy.settlement.core.modules.autocost.project.calculate.common;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.abs.AbsSubjectCalculate;
import com.mpolicy.settlement.core.modules.autocost.dto.CostAutoRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectCalculateRuleConfig;
import com.mpolicy.settlement.core.modules.autocost.dto.calculate.CostSubjectCalculateRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataDynamic;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataDynamicInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataRequest;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataResponse;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.EmployeeProductQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.OrgProductQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.SupervisorProductQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.enums.AutoCostAmountTypeEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.DynamicSubjectDataRuleEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.EmployeeRoleEnum;
import com.mpolicy.settlement.core.modules.autocost.factory.SubjectDataFactory;
import com.mpolicy.settlement.core.modules.autocost.handler.SubjectDataQueryHandler;
import com.mpolicy.settlement.core.modules.autocost.utils.AutoCostPoolUtil;
import com.mpolicy.settlement.core.utils.LambdaUtils;
import com.mpolicy.settlement.core.utils.SettlementCoreUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/29 1:11 上午
 * @Version 1.0
 */
@Slf4j
public abstract class DynamicBaseCalculateService<T> extends AbsSubjectCalculate {
    @Override
    public String handleCostCalculate(CostSubjectCalculateRecord record) {
        log.info("【科目】{}科目数据计算 构建开始......", getSubjectDataEnum().getName());
        /******1、获取科目源数据******/
        T sourceData = getCalculateSourceData(record.getCostSettlementCycle());
        if(Objects.isNull(sourceData)){
            log.info("【科目】{}科目数据计算 ......",getSubjectDataEnum().getName());
            return "";
        }
        /******2、按发送科目分组 ******/
        Map<String,SubjectDataDynamic> itemDetailMap = getShareDetail(sourceData);

        /******3、根据源数据初始化佣金记录列表 ******/
        Map<String,List<CostAutoRecord>> costAutoRecordMap = initCostAutoRecordBySource(record.getDocumentCode(),record, sourceData);

        /******4、执行任务计算 ******/
        //暂时去掉多线程计算
        /*List<CompletableFuture<BigDecimal>> tasks = Collections.synchronizedList(new ArrayList<>());
        for(String key : costAutoRecordMap.keySet()){

            List<CostAutoRecord> costAutoRecordList = costAutoRecordMap.get(key);
            SubjectDataDynamic dynamic = itemDetailMap.get(key);
            CompletableFuture<BigDecimal> completableUserInfoFuture = CompletableFuture.supplyAsync(() ->
                    calcTask(record,costAutoRecordList,dynamic), AutoCostPoolUtil.getThreadPool());
            tasks.add(completableUserInfoFuture);
        }
        CompletableFuture<BigDecimal> sumFuture = CompletableFuture.allOf(tasks.toArray(new CompletableFuture[0]))
                .thenApply(v -> {
                    BigDecimal sum = BigDecimal.ZERO;
                    for (CompletableFuture<BigDecimal> future : tasks) {
                        sum = sum.add(future.join());
                    }
                    return sum;
                });
        BigDecimal totalCash = sumFuture.join();
        //关闭线程池
        AutoCostPoolUtil.shutDown();*/
        BigDecimal totalCash = BigDecimal.ZERO;
        for(String key : costAutoRecordMap.keySet()){
            List<CostAutoRecord> costAutoRecordList = costAutoRecordMap.get(key);
            SubjectDataDynamic dynamic = itemDetailMap.get(key);
            totalCash = totalCash.add(calcTask(record,costAutoRecordList,dynamic));
        }
        /***5、数据存储****/

        List<CostAutoRecord> totalList = Lists.newArrayList();
        costAutoRecordMap.forEach((k,v)->totalList.addAll(v));
        log.info("生成的动态科目记录：{}", JSON.toJSONString(totalList));
        autoCostProcessService.saveAutoCostResult(totalList);
        record.setSubjectCalculateCash(totalCash);
        record.setSubjectCalculateSize(totalList.size());
        return "success";
    }

    public BigDecimal calcTask(CostSubjectCalculateRecord record,List<CostAutoRecord> costAutoRecordList,SubjectDataDynamic subjectDataDynamic){
        log.info("线程[{}/{}]_【科目】{}",Thread.currentThread().getId(),Thread.currentThread().getName(),subjectDataDynamic.getDynamicSubjectName());
        DynamicSubjectDataRuleEnum ruleEnum = subjectDataDynamic.getDynamicSubjectDataRuleEnum();
        Map<String, CostSubjectCalculateRuleConfig> ruleConfigMap = costSubjectCalculateRuleService.mapRuleConfig(getSubjectDataEnum().getCode());
        log.info("线程[{}/{}]_【科目】{}科目数据计算 获取科目规则集={}",Thread.currentThread().getId(),Thread.currentThread().getName(),getSubjectDataEnum().getName(),ruleConfigMap);
        log.info("DynamicSubjectDataRuleEnum={}",ruleEnum);
        DynamicSubjectShareService dynamicSubjectShareService = route(ruleEnum);

        //获取分摊明细Map对象
        log.info("subjectDataDynamic={}",subjectDataDynamic);
        log.info("costAutoRecordList={}",costAutoRecordList);
        log.info("record={}",record);
        Map<String,List> itemMap = dynamicSubjectShareService.mapShareItemDetail(subjectDataDynamic,costAutoRecordList,record.getCostSettlementCycle());
        log.info("itemMap={}",itemMap);

        /******4、计算科目规则,生成数据******/
        BigDecimal totalCash = BigDecimal.ZERO;

        for(CostAutoRecord costAutoRecord : costAutoRecordList){

            log.info("costAutoRecord={}",costAutoRecord);
            /***4-1、计算科目规则脚本入参****/
            log.info("线程[{}/{}]_【科目】{}科目数据计算 开始获取科目规则集",Thread.currentThread().getId(),Thread.currentThread().getName(),subjectDataDynamic.getDynamicSubjectName());
            Map<String,Object> params = createScriptParams(costAutoRecord);
            execScript(ruleConfigMap,params,costAutoRecord);

            /***4-2、校验科目规则生成的数据****/
            log.info("线程[{}/{}]_【科目】{}科目数据计算 校验科目规则生成的数据",Thread.currentThread().getId(),Thread.currentThread().getName(),subjectDataDynamic.getDynamicSubjectName());
            checkCostAutoRecord(ruleEnum,costAutoRecord);

            /***4-3、计算汇总数据***/
            log.info("线程[{}/{}]_【科目】{}科目数据计算 计算汇总数据",Thread.currentThread().getId(),Thread.currentThread().getName(),subjectDataDynamic.getDynamicSubjectName());
            calcCostAutoRecordTotal(ruleEnum,costAutoRecord);

            /***4-4、计算明细数据****/
            log.info("线程[{}/{}]_【科目】{}科目数据计算 计算明细数据",Thread.currentThread().getId(),Thread.currentThread().getName(),subjectDataDynamic.getDynamicSubjectName());
            //calcCostAutoRecordItem(ruleEnum,costAutoRecord,itemList);
            dynamicSubjectShareService.calcCostAutoRecordShareItem(costAutoRecord,itemMap.get(dynamicSubjectShareService.getItemMapKey(costAutoRecord)));
            totalCash = totalCash.add(costAutoRecord.getGrantAmount());

        }
        return totalCash;
    }


    private T getCalculateSourceData(String costSettlementCycle){
        SubjectDataQueryHandler<SubjectDataRequest<String>, T> queryHandler = SubjectDataFactory.getSubjectDataQueryHandler(getSubjectDataEnum());
        SubjectDataRequest<String> request = new SubjectDataRequest<>();
        request.setCostSettlementCycle(costSettlementCycle);
        SubjectDataResponse<T> result = queryHandler.getSubjectData(request);
        if(Objects.nonNull(result)){
            return result.getData()!=null?result.getData(): null;
        }
        return null;
    }

    protected List<CostAutoRecord> builderCostAutoRecordList(String documentCode, CostSubjectCalculateRecord record, SubjectDataDynamic sourceData){
        List<CostAutoRecord> costAutoRecords = Lists.newArrayList();
        CostAutoRecord costAutoRecord = null;
        Map<String,List<SubjectDataDynamicInfo>> map = LambdaUtils.groupBy(sourceData.getDynamicInfoList(),SubjectDataDynamicInfo::getEmployeeCode);
        String costDataType = sourceData.getDynamicSubjectDataRuleEnum().getDataDimension();
        for(String employeeCode : map.keySet()) {
            List<SubjectDataDynamicInfo> employeeList = map.get(employeeCode);

            for(SubjectDataDynamicInfo dynamicInfo : employeeList){
                costAutoRecord = builderCostAutoRecordByDynamic(documentCode,record,EmployeeRoleEnum.ZHNX, AutoCostAmountTypeEnum.OTHER,dynamicInfo);
                if(Objects.equals(getSubjectDataEnum().getCode(),CostSubjectEnum.DYNAMIC_SUBJECT.getCode())){
                    costAutoRecord.setSendSubjectCode(sourceData.getDynamicSubjectCode());
                    costAutoRecord.setSendSubjectName(sourceData.getDynamicSubjectName());
                    costAutoRecord.setDynamicFlag(1);
                }
                costAutoRecord.setCostDataType(costDataType);
                costAutoRecord.setAmount(dynamicInfo.getDynamicSubjectCash());
                costAutoRecord.setCommissionRate(new BigDecimal("100"));
                costAutoRecord.setCommissionAmount(calcCommissionAmt(costAutoRecord.getAmount(),costAutoRecord.getCommissionRate()));
                costAutoRecord.setGrantRate(new BigDecimal("100"));
                costAutoRecord.setGrantAmount(calcGrantAmt(costAutoRecord.getCommissionAmount(),costAutoRecord.getGrantRate()));
                costAutoRecords.add(costAutoRecord);
            }
        }
        return costAutoRecords;
    }



    /**
     * 动态科目初始化佣金记录
     * @param documentCode
     * @param record
     * @param roleEnum
     * @param amountTypeEnum
     * @param employee
     * @return
     */
    protected CostAutoRecord builderCostAutoRecordByDynamic(String documentCode,
                                                            CostSubjectCalculateRecord record,
                                                            EmployeeRoleEnum roleEnum,
                                                            AutoCostAmountTypeEnum amountTypeEnum,
                                                            SubjectDataDynamicInfo employee) {
        CostAutoRecord costAutoRecord = initCostAutoRecord(record.getProgrammeCode(),documentCode,record.getCostSettlementCycle());
        costAutoRecord.setCostSettlementCycle(record.getCostSettlementCycle());
        costAutoRecord.setSendObjectType(roleEnum.getCode());
        costAutoRecord.setSendObjectCode(employee.getEmployeeCode());
        costAutoRecord.setSendObjectName(employee.getEmployeeName());
        costAutoRecord.setObjectOrgCode(employee.getOrgCode());
        costAutoRecord.setObjectOrgName(employee.getOrgName());
        costAutoRecord.setAmountType(amountTypeEnum.getCode());
        costAutoRecord.setSettlementInstitution(employee.getSettlementInstitution());
        costAutoRecord.setSettlementInstitutionName(employee.getSettlementInstitutionName());

        return costAutoRecord;
    }

    @Override
    public Integer resetSubjectCalculate(String costSettlementCycle,String documentCode) {
        autoCostProcessService.resetAutoCostDynamic(getSubjectDataEnum().getCode(),costSettlementCycle);
        return 0;
    }



    /**
     * 根据源数据初始化佣金记录列表
     * @param documentCode
     * @param record
     * @param sourceData
     * @return
     */
    protected abstract Map<String,List<CostAutoRecord>> initCostAutoRecordBySource(String documentCode,CostSubjectCalculateRecord record,T sourceData);


    /**
     * 获取明细数据
     * @param sourceData
     * @return
     */
    protected abstract Map<String,SubjectDataDynamic> getShareDetail(T sourceData);

    /**
     * 组装脚本入参
     * @return
     */
    protected abstract Map<String,Object> createScriptParams(CostAutoRecord costAutoRecord);
    /**
     * 校验科目规则生成的数据
     * @return
     */
    protected abstract void checkCostAutoRecord(DynamicSubjectDataRuleEnum ruleEnum,CostAutoRecord costAutoRecord);

    /**
     * 计算主表数据
     */
    protected abstract  void calcCostAutoRecordTotal(DynamicSubjectDataRuleEnum ruleEnum,CostAutoRecord costAutoRecord);

}
