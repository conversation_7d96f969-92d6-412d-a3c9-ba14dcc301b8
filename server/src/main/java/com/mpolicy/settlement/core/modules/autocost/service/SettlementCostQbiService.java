package com.mpolicy.settlement.core.modules.autocost.service;

import com.mpolicy.settlement.core.modules.autocost.dto.qbi.*;

import java.util.List;
import java.util.Map;

/**
 * 中和农信qbi服务接口
 * 温馨提示：
 * pco + 主任=【分支机构指标】
 * 督导 = 【督导指标】
 * 所有员工 = 【员工基础指标】
 * 文档：<a href="https://cfpamf.yuque.com/xg4y3f/voxwu3/fgzub8uka17isqv6">中和农信qbi服务接口</a>
 *
 * <AUTHOR>
 * @since 2023-10-31 22:11
 */
public interface SettlementCostQbiService {

    /**
     * 根据员工编号获取员工指标数据
     *
     * @param employeeCode  员工编码
     * @param monthSnapshot 快照月份[yyyyMM]
     * @return 客户基础qbi指标信息
     * <AUTHOR>
     * @since 2023/11/8 16:52
     */
    EmployeeQbiInfo queryEmployeeQbi(String employeeCode, String monthSnapshot);

    /**
     * 根据员工编号集合获取员工指标数据
     *
     * @param employeeCodeList 员工编码集合
     * @param monthSnapshot    快照月份[yyyyMM]
     * @return 客户基础qbi指标信息
     * <AUTHOR>
     * @since 2023/11/8 16:40
     */
    List<EmployeeQbiInfo> listEmployeeQbi(List<String> employeeCodeList, String monthSnapshot);

    /**
     * 根据员工编码集合获取map指标信息
     *
     * @param employeeCodeList 员工编码集合
     * @param monthSnapshot    快照月份[yyyyMM]
     * @return map key=员工工号 value=基础指标
     * <AUTHOR>
     * @since 2023/11/8 16:53
     */
    Map<String, EmployeeQbiInfo> mapEmployeeQbi(List<String> employeeCodeList, String monthSnapshot);

    /**
     * 根据员工编码获取结算机构险种信息
     *
     * @param employeeCode  员工编码
     * @param monthSnapshot 快照月份[yyyyMM]
     * @return 结算机构险种信息
     * <AUTHOR>
     * @since 2023/11/16 19:44
     */
    List<EmployeeProductQbiInfo> listEmployeeProductQbi(String employeeCode, String monthSnapshot);

    /**
     * 根据员工编码集合获取结算机构险种信息
     *
     * @param employeeCodeList 员工编码集合
     * @param monthSnapshot    快照月份[yyyyMM]
     * @return 结算机构险种信息
     * <AUTHOR>
     * @since 2023/11/16 19:44
     */
    List<EmployeeProductQbiInfo> listEmployeeProductQbi(List<String> employeeCodeList, String monthSnapshot);

    /**
     * 根据督导员工编号获取员工指标数据
     *
     * @param supervisorEmployeeCode 督导员工编码
     * @param monthSnapshot          快照月份[yyyyMM]
     * @return 客户基础qbi指标信息
     * <AUTHOR>
     * @since 2023/11/8 16:52
     */
    SupervisorQbiInfo querySupervisorQbiInfo(String supervisorEmployeeCode, String monthSnapshot);

    /**
     * 根据督导员工编号集合获取员工指标数据
     *
     * @param supervisorEmployeeCodeList 督导员工编码集合
     * @param monthSnapshot              快照月份[yyyyMM]
     * @return 客户基础qbi指标信息
     * <AUTHOR>
     * @since 2023/11/8 16:40
     */
    List<SupervisorQbiInfo> listSupervisorQbiInfo(List<String> supervisorEmployeeCodeList, String monthSnapshot);

    /**
     * 根据督导员工编码集合获取map指标信息
     *
     * @param supervisorEmployeeCodeList 督导员工编码集合
     * @param monthSnapshot              快照月份[yyyyMM]
     * @return map key=员工工号 value=基础指标
     * <AUTHOR>
     * @since 2023/11/8 16:53
     */
    Map<String, SupervisorQbiInfo> mapSupervisorQbiInfo(List<String> supervisorEmployeeCodeList, String monthSnapshot);

    /**
     * 根据督导员工编码获取结算机构险种信息
     *
     * @param supervisorEmployeeCode 督导员工编码
     * @param monthSnapshot          快照月份[yyyyMM]
     * @return 结算机构险种信息
     * <AUTHOR>
     * @since 2023/11/16 19:44
     */
    List<SupervisorProductQbiInfo> listSupervisorProductQbi(String supervisorEmployeeCode, String monthSnapshot);

    /**
     * 根据督导员工编码集合获取结算机构险种信息
     *
     * @param supervisorEmployeeCodeList 督导员工编码集合
     * @param monthSnapshot              快照月份[yyyyMM]
     * @return 结算机构险种信息
     * <AUTHOR>
     * @since 2023/11/16 19:44
     */
    List<SupervisorProductQbiInfo> listSupervisorProductQbi(List<String> supervisorEmployeeCodeList, String monthSnapshot);

    /**
     * 根据机构编码获取机构指标
     *
     * @param orgCode       机构编码
     * @param monthSnapshot 快照月份[yyyyMM]
     * @return 机构指标
     * <AUTHOR>
     * @since 2023/11/8 17:02
     */
    OrgQbiInfo queryOrgQbi(String orgCode, String monthSnapshot);

    /**
     * 根据机构编码集合获取机构指标
     *
     * @param orgCodeList   机构编码集合
     * @param monthSnapshot 快照月份[yyyyMM]
     * @return 机构指标
     * <AUTHOR>
     * @since 2023/11/8 17:02
     */
    List<OrgQbiInfo> listOrgQbi(List<String> orgCodeList, String monthSnapshot);

    /**
     * 根据机构编码集合获取map机构指标信息
     *
     * @param orgCodeList   机构编码集合
     * @param monthSnapshot 快照月份[yyyyMM]
     * @return map key=机构编码 value=机构指标
     * <AUTHOR>
     * @since 2023/11/8 17:03
     */
    Map<String, OrgQbiInfo> mapOrgQbi(List<String> orgCodeList, String monthSnapshot);

    /**
     * 机构编码获取结算机构险种信息
     *
     * @param orgCode       机构编码
     * @param monthSnapshot 快照月份[yyyyMM]
     * @return 结算机构险种信息
     * <AUTHOR>
     * @since 2023/11/16 19:46
     */
    List<OrgProductQbiInfo> listOrgProductQbi(String orgCode, String monthSnapshot);
    /**
     * 获取qbi机构险种维度费用相关字段
     * @param orgCodeList 机构编码
     * @param monthSnapshot 快照月份[yyyyMM]
     * @return
     */
    List<OrgProductQbiInfo> listOrgProductQbiCostField(List<String> orgCodeList, String monthSnapshot);

    /**
     * 机构编码集合获取结算机构险种信息
     *
     * @param orgCodeList   机构编码集合
     * @param monthSnapshot 快照月份[yyyyMM]
     * @return 结算机构险种信息
     * <AUTHOR>
     * @since 2023/11/16 19:46
     */
    List<OrgProductQbiInfo> listOrgProductQbi(List<String> orgCodeList, String monthSnapshot);

    /**
     * 获取员工编码继续率
     *
     * @param employeeCode  员工编码
     * @param bizMonth      继续率月份[yyyy-MM]
     * @param monthSnapshot 快照月份[yyyyMM]
     * @return 保险结算员工粒度R13表信息
     * <AUTHOR>
     * @since 2023/12/16
     */
    EmployeeQbiRenewalRate queryEmployeeQbiRenewalRate(String employeeCode, String bizMonth, String monthSnapshot);

    /**
     * map 获取员工编码继续率
     *
     * @param employeeCodeList 员工编码集合
     * @param bizMonth         结算月份
     * @param monthSnapshot    快照月份[yyyyMM]
     * @return 保险结算员工粒度R13表信息
     * <AUTHOR>
     * @since 2023/12/16
     */
    Map<String, EmployeeQbiRenewalRate> mapEmployeeQbiRenewalRate(List<String> employeeCodeList, String bizMonth, String monthSnapshot);

    /**
     * 获取员工编码继续率集合
     *
     * @param employeeCode  员工编码
     * @param monthSnapshot 快照月份[yyyyMM]
     * @return 保险结算员工粒度R13表信息
     * <AUTHOR>
     * @since 2023/12/16
     */
    List<EmployeeQbiRenewalRate> listEmployeeQbiRenewalRate(String employeeCode, String monthSnapshot);

    /**
     * 获取员工编码继续率集合
     *
     * @param employeeCodeList 员工编码集合
     * @param bizMonth         结算月份
     * @param monthSnapshot    快照月份[yyyyMM]
     * @return 保险结算员工粒度R13表信息
     * <AUTHOR>
     * @since 2023/12/16
     */
    List<EmployeeQbiRenewalRate> listEmployeeQbiRenewalRate(List<String> employeeCodeList, String bizMonth, String monthSnapshot);

    /**
     * 获取员工编码继续率集合
     *
     * @param employeeCodeList 员工编码集合
     * @param bizMonthList         结算月份列表
     * @param monthSnapshot    快照月份[yyyyMM]
     * @return 保险结算员工粒度R13表信息
     * <AUTHOR>
     * @since 2024/06/09
     */
    List<EmployeeQbiRenewalRate> listEmployeeQbiRenewalRate(List<String> employeeCodeList, List<String> bizMonthList, String monthSnapshot);

    /**
     * 获取督导继续率信息
     *
     * @param supervisorCode 督导编号
     * @param bizMonth       继续率月份[yyyy-MM]
     * @param monthSnapshot  快照月份[yyyyMM]
     * @return 保险结算督导粒度R13表信息
     * <AUTHOR>
     * @since 2023/12/16
     */
    SupervisorQbiRenewalRate querySupervisorQbiRenewalRate(String supervisorCode, String bizMonth, String monthSnapshot);

    /**
     * map 获取督导编码继续率
     *
     * @param supervisorCodeList 督导员工编码集合
     * @param bizMonth           继续率月份[yyyy-MM]
     * @param monthSnapshot      快照月份[yyyyMM]
     * @return 督导粒度R13表信息
     * <AUTHOR>
     * @since 2023/12/16
     */
    Map<String, SupervisorQbiRenewalRate> mapSupervisorQbiRenewalRate(List<String> supervisorCodeList, String bizMonth, String monthSnapshot);

    /**
     * 获取督导继续率信息集合
     *
     * @param supervisorCode 督导编号
     * @param monthSnapshot  快照月份[yyyyMM]
     * @return 保险结算督导粒度R13表信息
     * <AUTHOR>
     * @since 2023/12/16
     */
    List<SupervisorQbiRenewalRate> listSupervisorQbiRenewalRate(String supervisorCode, String monthSnapshot);

    /**
     * 获取督导编码继续率集合
     *
     * @param supervisorCodeList 督导员工编码集合
     * @param bizMonth           继续率月份[yyyy-MM]
     * @param monthSnapshot      快照月份[yyyyMM]
     * @return 督导粒度R13表信息
     * <AUTHOR>
     * @since 2023/12/16
     */
    List<SupervisorQbiRenewalRate> listSupervisorQbiRenewalRate(List<String> supervisorCodeList, String bizMonth, String monthSnapshot);

    /**
     * 获取机构继续率信息
     *
     * @param orgCode       机构编码
     * @param bizMonth      继续率月份[yyyy-MM]
     * @param monthSnapshot 快照月份[yyyyMM]
     * @return 分支指标基本信息
     * <AUTHOR>
     * @since 2023/12/16
     */
    OrgQbiRenewalRate queryOrgQbiRenewalRate(String orgCode, String bizMonth, String monthSnapshot);

    /**
     * map 获取机构继续率
     *
     * @param orgCodeList   机构编码集合
     * @param bizMonth      继续率月份[yyyy-MM]
     * @param monthSnapshot 快照月份[yyyyMM]
     * @return 督导粒度R13表信息
     * <AUTHOR>
     * @since 2023/12/16
     */
    Map<String, OrgQbiRenewalRate> mapOrgQbiRenewalRate(List<String> orgCodeList, String bizMonth, String monthSnapshot);

    /**
     * 获取机构继续率信息集合
     *
     * @param orgCode       机构编码
     * @param monthSnapshot 快照月份[yyyyMM]
     * @return 分支指标基本信息
     * <AUTHOR>
     * @since 2023/12/16
     */
    List<OrgQbiRenewalRate> listOrgQbiRenewalRate(String orgCode, String monthSnapshot);

    /**
     * 获取机构继续率
     *
     * @param orgCodeList   机构编码集合
     * @param bizMonth      继续率月份[yyyy-MM]
     * @param monthSnapshot 快照月份[yyyyMM]
     * @return 督导粒度R13表信息
     * <AUTHOR>
     * @since 2023/12/16
     */
    List<OrgQbiRenewalRate> listOrgQbiRenewalRate(List<String> orgCodeList, String bizMonth, String monthSnapshot);
}