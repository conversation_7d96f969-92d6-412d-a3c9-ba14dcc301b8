package com.mpolicy.settlement.core.modules.autocost.project.data;

import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataDynamic;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataRequest;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataRuleInfo;
import com.mpolicy.settlement.core.modules.autocost.project.data.common.DynamicDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 【科目16】代发区域营销费数据生成服务
 * 中和农信员工（代发区域营销费，名单业务人员导入）
 * 业务人员导入，系统自动根据机构的农机险险种保费按比例分摊
 *
 * <AUTHOR>
 * @since 2023-11-05 17:58
 */
@Service
@Slf4j
public class IssuingRegionalMarketingFeeDataService extends DynamicDataService<SubjectDataRequest<String>, SubjectDataDynamic> {


    @Override
    public CostSubjectEnum getSubjectDataEnum() {
        return CostSubjectEnum.ISSUING_REGIONAL_MARKETING_FEE;
    }

    /**
     * 代发区域营销费生成数据明细
     * 1、获取动态科目为代发区域营销费
     * 3、解析构建科目目标数据
     * 4、写入科目范围数据
     */
    @Override
    public int builderCostSubjectData(String costSettlementCycle, CostSubjectInfo subjectInfo, String batchCode, List<SubjectDataRuleInfo> subjectDataRuleList) {
        log.info("【代发区域营销费】科目数据计算 构建开始......");
        log.info("【代发区域营销费】属于xls动态导入方式，无需处理builder科目数据......");
        log.info("【代发区域营销费】科目数据计算 构建完成......");
        return 0;
    }

    @Override
    public int resetSubjectData(String costSettlementCycle) {
        log.info("【代发区域营销费】无需操作，直接重置完成了......");
        return 0;
    }

    @Override
    public SubjectDataDynamic subjectData(SubjectDataRequest<String> request) {
        return builderSubjectDataDynamic(request.getCostSettlementCycle(), CostSubjectEnum.ISSUING_REGIONAL_MARKETING_FEE, CostSubjectEnum.ISSUING_REGIONAL_MARKETING_FEE.getCode());
    }
}