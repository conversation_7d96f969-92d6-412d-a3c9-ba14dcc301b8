package com.mpolicy.settlement.core.modules.protocol.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 保司协议险种名称和小鲸险种对应关系
 * 
 * <AUTHOR>
 * @date 2023-05-20 20:31:56
 */
@TableName("ep_protocol_insurance_product_product")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProtocolInsuranceProductProductEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private Integer id;
	/**
	 * 协议保司产品编码
	 */
	private String insuranceProductCode;
	/**
	 * 产品Id
	 */
	private Integer insuranceProductId;
	/**
	 * 协议保司产品名称
	 */
	private String insuranceProductName;
	/**
	 * 小鲸险种编码
	 */
	private String productCode;
	/**
	 * 小鲸险种名称
	 */
	private String productName;
	/**
	 * 计划名称
	 */
	private String productPlan;
}
