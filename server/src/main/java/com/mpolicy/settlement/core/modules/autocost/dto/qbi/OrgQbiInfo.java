package com.mpolicy.settlement.core.modules.autocost.dto.qbi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 分支指标基本信息
 *
 * <AUTHOR>
 * @since  2023-11-05 19:50
 */
@Data
@ApiModel(value = "分支指标基本信息", description = "分支指标基本信息")
public class OrgQbiInfo implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 分支编号
     */
    @ApiModelProperty(value = "分支编号", example = "ZHNX202020")
    private String orgCode;

    /**
     * 分支名称
     */
    @ApiModelProperty(value = "分支名称", example = "ZHNX202020")
    private String orgName;

    /**
     * 续期率
     */
    @ApiModelProperty(value = "续期率", example = "0.8")
    private BigDecimal renewalRate;


    /**
     * 长险推广费
     */
    @ApiModelProperty(value = "长险推广费", example = "0.8")
    private BigDecimal longPromotion;

    /**
     * 长险结算保费
     */
    @ApiModelProperty(value = "长险结算保费", example = "0.8")
    private BigDecimal longSettlementPremium;

    /**
     * 短险推广费
     */
    @ApiModelProperty(value = "短险推广费", example = "0.8")
    private BigDecimal shortPromotion;

    /**
     * 短险结算保费
     */
    @ApiModelProperty(value = "短险结算保费", example = "0.8")
    private BigDecimal shortSettlementPremium;

    /**
     * 整村推进推广费
     */
    @ApiModelProperty(value = "整村推进推广费", example = "0.8")
    private BigDecimal ruralProxyPromotion;

    /**
     * 整村推进算佣金费
     */
    @ApiModelProperty(value = "整村推进算佣金费", example = "0.8")
    private BigDecimal ruralProxyPremium;

    /**
     * pco等级
     */
    @ApiModelProperty(value = "pco等级", example = "S")
    private String pcoLevel;

    /**
     * 员工注册客户数
     */
    @ApiModelProperty(value = "员工注册客户数", example = "8")
    private Integer registerUserCount;

    /**
     * 人均注册客户数
     */
    @ApiModelProperty(value = "人均注册客户数", example = "8")
    private BigDecimal avgRegisterUserCount;

    /**
     * 农机险推广费
     */
    @ApiModelProperty(value = "农机险推广费", example = "0.8")
    private BigDecimal agriculturalPromotion;

    /**
     * 农机险结算保费
     */
    @ApiModelProperty(value = "农机险结算保费", example = "0.8")
    private BigDecimal agriculturalSettlementPremium;

    /**
     * 短期险折标保费
     */
    @ApiModelProperty(value = "短期险折标保费", example = "0.8")
    private BigDecimal shortAssessConvertInsurancePremium;


    /**
     * 长期险折标保费
     */
    @ApiModelProperty(value = "长期险折标保费", example = "0.8")
    private BigDecimal longAssessConvertInsurancePremium;

    /**
     * 应缴金额
     */
    private BigDecimal payAmt;
    /**
     * 实缴金额
     */
    private BigDecimal actPayAmt;

    /**
     * 生服库存盘点率
     */
    private BigDecimal smCheckSignTaskRadio;
    /**
     * 酒水个人仓建仓率
     */
    private BigDecimal personalWarehouseCreateRadio;
    /**
     * 当月酒水个人仓使用率
     */
    private BigDecimal smPersonalWarehouseUseRadio;

    /**
     * 多次驳回率
     */
    private BigDecimal smRejectManyRate;
    /**
     * pco培训会次数
     */
    private Integer smPcoTraining;
    /**
     * 当月酒水营收
     */
    private BigDecimal smWineRevenueAmt;

    /**
     * 上月月末续保率
     */
    @ApiModelProperty(value = "上月月末续保率", example = "0.85")
    private BigDecimal smRenewalRate;
}
