package com.mpolicy.settlement.core.modules.autocost.project.calculate.common;

import cn.hutool.core.util.StrUtil;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.modules.autocost.abs.AbsSubjectCalculate;
import com.mpolicy.settlement.core.modules.autocost.dto.CostAutoProduct;
import com.mpolicy.settlement.core.modules.autocost.dto.CostAutoRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectCalculateRuleConfig;
import com.mpolicy.settlement.core.modules.autocost.dto.calculate.CostSubjectCalculateRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.data.*;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.ProductCommonQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.enums.AutoCostAmountTypeEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.CostDataTypeEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.EmployeeRoleEnum;
import com.mpolicy.settlement.core.modules.autocost.factory.SubjectDataFactory;
import com.mpolicy.settlement.core.modules.autocost.handler.SubjectDataQueryHandler;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * 计算流程：
 * 1、获取科目源数据
 * 2、根据源数据初始化佣金记录列表
 * 3、获取分摊数据明细
 * 4、获取科目规则集
 * 5、计算科目规则,生成数据
 *    5-1、处理科目规则脚本入参
 *    5-2、校验科目规则生成的数据
 *    5-3、计算汇总数据
 *    5-4、计算明细数据
 * 6、数据存储
 * @date 2023/11/21 10:01 下午
 * @Version 1.0
 */
@Slf4j
public abstract class ProductCalculateNewService<V,R> extends AbsSubjectCalculate {
    @Override
    public String handleCostCalculate(CostSubjectCalculateRecord record) {
        log.info("【科目】{}科目数据计算 构建开始......", getSubjectDataEnum().getName());
        /******1、获取科目源数据******/
        List<V> sourceDataList = getCalculateSourceData(record.getCostSettlementCycle());
        if(CollectionUtils.isEmpty(sourceDataList)){
            log.info("【科目】{}科目数据计算 ......",getSubjectDataEnum().getName());
            return "";
        }

        /******2、根据源数据初始化佣金记录列表 ******/
        log.info("根据源数据，开始初始化每个人的{}结算佣金记录",getSubjectDataEnum().getName());
        List<CostAutoRecord> costAutoRecordList = initCostAutoRecordBySource(record.getDocumentCode(),record, sourceDataList);

        /******3、获取分摊数据明细 ******/
        Map<String,List<R>> itemDetailMap = getPersonalProductQbiInfoMap(costAutoRecordList,record.getCostSettlementCycle());

        /******4、获取科目规则集******/
        Map<String, CostSubjectCalculateRuleConfig> ruleConfigMap = costSubjectCalculateRuleService.mapRuleConfig(getSubjectDataEnum().getCode());
        log.info("【科目】{}科目数据计算 获取科目规则集={}",getSubjectDataEnum().getName(),ruleConfigMap);

        /******5、计算科目规则,生成数据******/
        BigDecimal totalCash = BigDecimal.ZERO;
        for(CostAutoRecord costAutoRecord : costAutoRecordList){
            List<R> itemList = itemDetailMap.get(getItemMapKey(costAutoRecord.getSendObjectCode(),costAutoRecord.getObjectOrgCode(),costAutoRecord.getSettlementInstitution()));

            /***5-1、计算科目规则脚本入参****/
            Map<String,Object> params = createScriptParams(costAutoRecord);
            execScript(ruleConfigMap,params,costAutoRecord);

            /***5-2、校验科目规则生成的数据****/
            checkCostAutoRecord(costAutoRecord);

            /***5-3、计算汇总数据***/
            calcCostAutoRecordTotal(costAutoRecord);

            /***5-4、计算明细数据****/
            calcCostAutoRecordItem(costAutoRecord,itemList);
            totalCash = totalCash.add(costAutoRecord.getGrantAmount());

        }
        /***6、数据存储****/
        //过滤发放金额为空的记录
        autoCostProcessService.saveAutoCostProductResult(costAutoRecordList);
        record.setSubjectCalculateCash(totalCash);
        record.setSubjectCalculateSize(costAutoRecordList.size());

        return "success";
    }







    private List<V> getCalculateSourceData(String costSettlementCycle){
        SubjectDataQueryHandler<SubjectDataRequest<String>, List<V>> queryHandler = SubjectDataFactory.getSubjectDataQueryHandler(getSubjectDataEnum());
        SubjectDataRequest<String> request = new SubjectDataRequest<>();
        request.setCostSettlementCycle(costSettlementCycle);
        SubjectDataResponse<List<V>> result = queryHandler.getSubjectData(request);
        if(Objects.nonNull(result)){
            return result.getData()!=null?result.getData(): Collections.EMPTY_LIST;
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 根据SubjectDataPolicyComm数据 初始化佣金记录
     * @param documentCode
     * @param record
     * @param roleEnum
     * @param amountTypeEnum
     * @param policyComm
     * @return
     */
    protected CostAutoRecord builderCostAutoRecordByPolicyComm(String documentCode,
                                                            CostSubjectCalculateRecord record,
                                                            EmployeeRoleEnum roleEnum,
                                                            AutoCostAmountTypeEnum amountTypeEnum,
                                                            SubjectDataPolicyComm policyComm) {
        CostAutoRecord costAutoRecord = initCostAutoRecord(record.getProgrammeCode(),documentCode,record.getCostSettlementCycle());
        costAutoRecord.setCostSettlementCycle(record.getCostSettlementCycle());
        costAutoRecord.setSendObjectType(roleEnum.getCode());
        costAutoRecord.setSendObjectCode(policyComm.getEmployeeCode());
        costAutoRecord.setSendObjectName(policyComm.getEmployeeName());
        costAutoRecord.setObjectOrgCode(policyComm.getOrgCode());
        costAutoRecord.setObjectOrgName(policyComm.getOrgName());
        costAutoRecord.setAmountType(amountTypeEnum.getCode());
        costAutoRecord.setCostDataType(CostDataTypeEnum.PRODUCT.getCode());

        return costAutoRecord;
    }

    /**
     * 根据SubjectDataPolicyGroup数据 初始化佣金记录
     * @param documentCode
     * @param record
     * @param roleEnum
     * @param amountTypeEnum
     * @param groupPolicy
     * @return
     */
    protected CostAutoRecord builderCostAutoRecordByPolicyGroup(String documentCode,
                                                               CostSubjectCalculateRecord record,
                                                               EmployeeRoleEnum roleEnum,
                                                               AutoCostAmountTypeEnum amountTypeEnum,
                                                                SubjectDataPolicyGroup groupPolicy) {
        CostAutoRecord costAutoRecord = initCostAutoRecord(record.getProgrammeCode(),documentCode,record.getCostSettlementCycle());
        costAutoRecord.setCostSettlementCycle(record.getCostSettlementCycle());
        costAutoRecord.setSendObjectType(roleEnum.getCode());
        costAutoRecord.setSendObjectCode(groupPolicy.getEmployeeCode());
        costAutoRecord.setSendObjectName(groupPolicy.getEmployeeName());
        costAutoRecord.setObjectOrgCode(groupPolicy.getOrgCode());
        costAutoRecord.setObjectOrgName(groupPolicy.getOrgName());
        costAutoRecord.setAmountType(amountTypeEnum.getCode());
        costAutoRecord.setSettlementInstitution(groupPolicy.getSettlementInstitution());
        costAutoRecord.setSettlementInstitutionName(groupPolicy.getSettlementInstitutionName());
        costAutoRecord.setCostDataType(CostDataTypeEnum.PRODUCT.getCode());
        costAutoRecord.setBizMonth(groupPolicy.getBizMonth());
        costAutoRecord.setRenewalRate(groupPolicy.getRenewalRate());

        return costAutoRecord;
    }

    /**
     * 根据SubjectDataPolicyGroup数据 初始化佣金记录
     * @param documentCode
     * @param record
     * @param roleEnum
     * @param amountTypeEnum
     * @param reissue
     * @return
     */
    protected CostAutoRecord builderCostAutoRecordByReissue(String documentCode,
                                                                CostSubjectCalculateRecord record,
                                                                EmployeeRoleEnum roleEnum,
                                                                AutoCostAmountTypeEnum amountTypeEnum,
                                                            SubjectDataReissue reissue) {
        CostAutoRecord costAutoRecord = initCostAutoRecord(record.getProgrammeCode(),documentCode,record.getCostSettlementCycle());
        costAutoRecord.setCostSettlementCycle(record.getCostSettlementCycle());
        costAutoRecord.setSendObjectType(roleEnum.getCode());
        costAutoRecord.setSendObjectCode(reissue.getSendObjectCode());
        costAutoRecord.setSendObjectName(reissue.getSendObjectName());
        costAutoRecord.setObjectOrgCode(reissue.getObjectOrgCode());
        costAutoRecord.setObjectOrgName(reissue.getObjectOrgName());
        costAutoRecord.setSettlementInstitution(reissue.getSettlementInstitution());
        costAutoRecord.setSettlementInstitutionName(reissue.getSettlementInstitutionName());
        costAutoRecord.setAmountType(amountTypeEnum.getCode());
        costAutoRecord.setCostDataType(CostDataTypeEnum.PRODUCT.getCode());
        costAutoRecord.setBizMonth(reissue.getBizMonth());

        return costAutoRecord;
    }

    /**
     * 根据SubjectDataPolicyComm数据 初始化佣金记录
     * @param documentCode
     * @param record
     * @param roleEnum
     * @param amountTypeEnum
     * @param employee
     * @return
     */
    protected CostAutoRecord builderCostAutoRecordByDataEmployee(String documentCode,
                                                                CostSubjectCalculateRecord record,
                                                                EmployeeRoleEnum roleEnum,
                                                                AutoCostAmountTypeEnum amountTypeEnum,
                                                                 SubjectDataEmployee employee) {
        CostAutoRecord costAutoRecord = initCostAutoRecord(record.getProgrammeCode(),documentCode,record.getCostSettlementCycle());
        costAutoRecord.setCostSettlementCycle(record.getCostSettlementCycle());
        costAutoRecord.setSendObjectType(roleEnum.getCode());
        costAutoRecord.setSendObjectCode(employee.getEmployeeCode());
        costAutoRecord.setSendObjectName(employee.getEmployeeName());
        costAutoRecord.setObjectOrgCode(employee.getOrgCode());
        costAutoRecord.setObjectOrgName(employee.getOrgName());

        costAutoRecord.setAmountType(amountTypeEnum.getCode());
        costAutoRecord.setCostDataType(CostDataTypeEnum.PRODUCT.getCode());

        return costAutoRecord;
    }





    /**
     *
     * @param costAutoRecord
     * @param qbiInfo
     * @return
     */
    protected CostAutoProduct builderCostAutoProduct(CostAutoRecord costAutoRecord, R qbiInfo){
        CostAutoProduct product = new CostAutoProduct();
        BeanUtils.copyProperties(costAutoRecord,product);
        product.setCostProductCode(PolicySettlementUtils.createCodeLastNumber(Constant.AUTO_COST_PRODUCT));

        //险种信息设置
        builderItemProductInfo((ProductCommonQbiInfo)qbiInfo,product);


        return product;
    }
    protected void builderItemProductInfo(ProductCommonQbiInfo qbiInfo, CostAutoProduct product){
        product.setSettlementInstitution(qbiInfo.getSettlementOrgCode());
        product.setSettlementInstitutionName(qbiInfo.getSettlementOrgName());
        product.setProductCode(qbiInfo.getProductCode());
        product.setProductName(qbiInfo.getProductName());
        product.setProductType(qbiInfo.getProductType());
        product.setProductGroup(qbiInfo.getProductGroup());
        product.setLevel2Code(qbiInfo.getLevel2Code());
        product.setLevel2Name(qbiInfo.getLevel2Name());
        product.setLevel3Code(qbiInfo.getLevel3Code());
        product.setLevel3Name(qbiInfo.getLevel3Name());
        product.setLongShortFlag(qbiInfo.getLongShortFlag());
    }




    @Override
    public Integer resetSubjectCalculate(String costSettlementCycle,String documentCode) {
        autoCostProcessService.resetAutoCostProduct(getSubjectDataEnum().getCode(),costSettlementCycle);
        return 0;
    }





    /**
     * 根据源数据初始化佣金记录列表
     * @param documentCode
     * @param record
     * @param sourceDataList
     * @return
     */
    protected abstract List<CostAutoRecord> initCostAutoRecordBySource(String documentCode,CostSubjectCalculateRecord record,List<V> sourceDataList);

    /**
     * 获取明细数据
     * @param costAutoRecordList
     * @param costSettlementCycle
     * @return
     */
    protected abstract Map<String, List<R>> getPersonalProductQbiInfoMap(List<CostAutoRecord> costAutoRecordList, String costSettlementCycle);
    /**
     * 组装脚本入参
     * @return
     */
    protected abstract Map<String,Object> createScriptParams(CostAutoRecord costAutoRecord);

    /**
     * 校验科目规则生成的数据
     * @return
     */
    protected abstract void checkCostAutoRecord(CostAutoRecord costAutoRecord);

    /**
     * 计算主表数据
     */
    protected abstract  void calcCostAutoRecordTotal(CostAutoRecord costAutoRecord);

    /**
     * 计算明细
     */
    protected abstract  void calcCostAutoRecordItem(CostAutoRecord costAutoRecord,List<R> oldCost);
}
