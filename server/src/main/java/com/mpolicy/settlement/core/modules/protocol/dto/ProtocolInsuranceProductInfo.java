package com.mpolicy.settlement.core.modules.protocol.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ProtocolInsuranceProductInfo implements Serializable {
    private static final long serialVersionUID = -7551902523385602892L;
    /**
     * 保司产品编码
     */
    private String companyCode;
    /**
     * 保司产品名称
     */
    private String companyName;
    /**
     * 保司简称
     */
    private String companyShortName;
    /**
     * 协议保司产品编码
     */
    private String insuranceProductCode;
    /**
     * 协议保司产品名称
     */
    private String insuranceProductName;
    /**
     * 关联的小鲸险种信息
     */
    private List<String> productCodeList;
}
