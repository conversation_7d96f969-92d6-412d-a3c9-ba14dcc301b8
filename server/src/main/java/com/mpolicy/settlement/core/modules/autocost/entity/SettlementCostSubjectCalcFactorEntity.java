package com.mpolicy.settlement.core.modules.autocost.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 自动结算科目计算因子表
 * 
 * <AUTHOR>
 * @since 2023-11-04 17:34:05
 */
@TableName("settlement_cost_subject_calc_factor")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostSubjectCalcFactorEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 科目编码
	 */
	private String subjectCode;
	/**
	 * 因子编码
	 */
	private String factorCode;
	/**
	 * 因子名称
	 */
	private String factorName;
	/**
	 * 因子数据来源
	 */
	private String dataSource;
	/**
	 * 是否有运行脚本规则
	 */
	private Integer scriptFlag;
	/**
	 * 规则编码
	 */
	private String ruleCode;
	/**
	 * 字典项类型  数据取值为字典范围
	 */
	private String dictType;
	/**
	 * 顺序
	 */
	private Integer sortNum;
	/**
	 * 因子默认值
	 */
	private String factorDefaultValue;
	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
