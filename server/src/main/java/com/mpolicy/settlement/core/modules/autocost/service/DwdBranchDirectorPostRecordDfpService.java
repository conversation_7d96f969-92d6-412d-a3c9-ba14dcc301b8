package com.mpolicy.settlement.core.modules.autocost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.autocost.dto.BranchDirectorPostRecordDto;
import com.mpolicy.settlement.core.modules.autocost.entity.DwdBranchDirectorPostRecordDfpEntity;

import java.util.Date;
import java.util.List;

public interface DwdBranchDirectorPostRecordDfpService  extends IService<DwdBranchDirectorPostRecordDfpEntity> {
    /**
     * 获取某个周期内的主任和副主任的任职记录
     * @param generateCycle
     * @return
     */
    List<BranchDirectorPostRecordDto> listBranchDirectorPostRecord(String generateCycle);

    /**
     *
     * @param cycle
     * @param bchCodes
     * @return
     */
    List<BranchDirectorPostRecordDto> listPostRecordByCycleAndBchCodes(String cycle,List<String> bchCodes);

    /**
     * 获取机构领导任职记录（主任、副主任、管理顾问、主任助理）
     * @param generateCycle
     * @return
     */
    public List<BranchDirectorPostRecordDto> listBranchLeaderPostRecord(String generateCycle, Date firstDayOfMonth);

    /**
     * 获取机构生服对接人
     * @param generateCycle
     * @return
     */
    public List<BranchDirectorPostRecordDto> listLifeServicerPostRecord(String generateCycle, Date firstDayOfMonth);
}
