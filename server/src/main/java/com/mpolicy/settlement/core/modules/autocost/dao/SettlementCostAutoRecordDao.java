package com.mpolicy.settlement.core.modules.autocost.dao;

import com.mpolicy.service.common.mapper.ImsBaseMapper;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostAutoRecordEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 自动结算科目佣金记录表
 * 
 * <AUTHOR>
 * @since 2023-11-01 22:26:32
 */
public interface SettlementCostAutoRecordDao extends ImsBaseMapper<SettlementCostAutoRecordEntity> {
    /**
     * 根据结算周期物理删除自动结算科目佣金记录(慎用)
     * 暂时测试人员在生产冒烟验证
     * @param costSettlementCycle
     * @return
     */
    @Update(" delete from settlement_cost_auto_record where `cost_settlement_cycle` =#{costSettlementCycle}")
    int deletePhysicalByCostSettlementCycle(@Param("costSettlementCycle") String costSettlementCycle) ;

    /**
     * 根据结算周期、科目编码 物理删除自动结算科目佣金记录(慎用)
     * 暂时测试人员在生产冒烟验证
     * @param costSettlementCycle
     * @param subjectCode
     * @return
     */
    @Update(" delete from settlement_cost_auto_record where `cost_settlement_cycle` =#{costSettlementCycle} and subject_code=#{subjectCode}")
    int deletePhysicalBySubjectCode(@Param("costSettlementCycle") String costSettlementCycle,@Param("subjectCode") String subjectCode) ;

    /**
     * 获取逻辑删除且已入账的记录
     * @param costSettlementCycle
     * @param subjectCodes
     * @return
     */
    @Select("<script>" +
            " select * " +
            " from settlement_cost_auto_record " +
            " where cost_settlement_cycle =#{costSettlementCycle} and subject_code in " +
            " <foreach collection='subjectCodes' item='key' open='(' separator=',' close=')'> " +
            "    #{key}" +
            " </foreach> "+
            " and account_flag = 1 and deleted = -1 "+
            " </script>")
    List<SettlementCostAutoRecordEntity> listLogicDeleteAndAccountFlagDone(@Param("costSettlementCycle") String costSettlementCycle,@Param("subjectCodes") List<String> subjectCodes);
}
