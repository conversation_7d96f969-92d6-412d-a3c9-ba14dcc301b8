package com.mpolicy.settlement.core.modules.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/08/05
 */
@Data
@TableName("settlement_prem_change_log")
public class SettlementPremChangeLogEntity implements Serializable {

    @TableId(type = IdType.AUTO,value = "id")
    private Integer id;

    /**
     * 推送标识
     */
    private String pushEventCode;
    /**
     * 业务编码
     */
    private String businessCode;

    /**
     * 变更类型
     */
    private Integer changeType;
    /**
     * 数据体
     */
    private String dataJson;
    /**
     * MQ发送状态  -1无需发送MQ，0 未发送 1 发送中，2 发送成功，3发送失败
     */
    private Integer sendStatus;

    /**
     * MQ发送时间
     */
    private Date sendTime;
    /**
     * 发送次数
     */
    private Integer sendCount;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
