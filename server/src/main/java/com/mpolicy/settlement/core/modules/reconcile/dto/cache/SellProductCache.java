package com.mpolicy.settlement.core.modules.reconcile.dto.cache;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "售卖产品信息缓存", description = "售卖产品信息缓存")
public class SellProductCache implements Serializable {
    private static final long serialVersionUID = 1;
    private String productCode;
    @ApiModelProperty(
            value = "商品名称",
            example = "名称"
    )
    private String productName;
    @ApiModelProperty(
            value = "商品简称",
            example = "简称"
    )
    private String productAbbreviation;
    @ApiModelProperty(
            value = "客户端类型 1:小程序 2:APP 3:小程序+APP",
            example = "3"
    )
    private Integer clientType;
    @ApiModelProperty(
            value = "产品状态0:下架 1:上架",
            example = "0"
    )
    private Integer productStatus;
    @ApiModelProperty("是否加入计划书0:否 1:是")
    private Integer joinPlan;
    @ApiModelProperty(
            value = "组合编码",
            example = "XZ123456789"
    )
    private String portfolioCode;
    @ApiModelProperty("组合编码")
    private String portfolioName;
    @ApiModelProperty("组合简称")
    private String portfolioShortName;
    @ApiModelProperty("组合拼音")
    private String pinyin;
    @ApiModelProperty("投保链接")
    private String insureUrl;
    @ApiModelProperty("投保链接类型")
    private String insureUrlType;
    @ApiModelProperty("组合类型 字典")
    private String portfolioType;
    @ApiModelProperty("保险公司编码")
    private String companyCode;
    @ApiModelProperty("保险公司名称")
    private String companyName;
    private Integer isMobileVerify;
    private Integer isPersonReview;
    private Integer isGive;
    private Integer isRenewal;
}
