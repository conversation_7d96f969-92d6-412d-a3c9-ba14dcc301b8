package com.mpolicy.settlement.core.modules.autocost.project.calculate.common.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mpolicy.settlement.core.modules.autocost.dto.CostAutoRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataDynamic;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.EmployeeProductQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.OrgProductQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.SupervisorProductQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.enums.DynamicSubjectDataRuleEnum;
import com.mpolicy.settlement.core.modules.autocost.project.calculate.common.AutoCostProcessService;
import com.mpolicy.settlement.core.modules.autocost.project.calculate.common.DynamicSubjectShareService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostQbiService;
import com.mpolicy.settlement.core.utils.LambdaUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/30 12:41 下午
 * @Version 1.0
 */
@Slf4j
@Service
public class AutoPersonalProductShareServiceImpl extends DynamicSubjectAbstractService implements DynamicSubjectShareService<EmployeeProductQbiInfo> {


    @Override
    public Boolean route(DynamicSubjectDataRuleEnum eventEnum) {
        return Objects.equals(getActionType(), eventEnum);
    }

    @Override
    public DynamicSubjectDataRuleEnum getActionType() {
        return DynamicSubjectDataRuleEnum.AUTO_PERSONAL_PRODUCT;
    }

    @Override
    public String getItemMapKey(CostAutoRecord costAutoRecord) {
        return keyItem(costAutoRecord.getObjectOrgCode(),costAutoRecord.getSendObjectCode(),costAutoRecord.getSettlementInstitution());
    }

    @Override
    public Map<String,List<EmployeeProductQbiInfo>> mapShareItemDetail(SubjectDataDynamic subjectDataDynamic,List<CostAutoRecord> costAutoRecordList, String costSettlementCycle) {
        List<String> employeeCodes = costAutoRecordList.stream().map(CostAutoRecord::getSendObjectCode).distinct().collect(Collectors.toList());

        if(CollectionUtils.isEmpty(employeeCodes)){
            return Collections.EMPTY_MAP;
        }
        log.info("根据员工编码集合获取结算机构险种信息，入参：{},{}", JSON.toJSONString(employeeCodes),costSettlementCycle);
        List<EmployeeProductQbiInfo> list =  settlementCostQbiService.listEmployeeProductQbi(employeeCodes,costSettlementCycle);
        if(CollectionUtils.isEmpty(list)){
            return Collections.EMPTY_MAP;
        }
        return LambdaUtils.groupBy(list,t->keyItem(t.getOrgCode(),t.getEmployeeCode(),t.getSettlementOrgCode()));
    }

    @Override
    public void calcCostAutoRecordShareItem(CostAutoRecord costAutoRecord, List<EmployeeProductQbiInfo> itemList) {
        autoCostProcessService.calcEmployeeProductQbiShare(costAutoRecord,itemList);
    }
}
