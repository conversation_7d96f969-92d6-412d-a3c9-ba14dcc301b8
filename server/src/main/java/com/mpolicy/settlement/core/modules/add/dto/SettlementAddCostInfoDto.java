package com.mpolicy.settlement.core.modules.add.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/7 11:43 上午
 * @Version 1.0
 */
@Data
@ApiModel(value = "农保加佣明细表", description = "农保加佣明细表")
public class SettlementAddCostInfoDto implements Serializable {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 保单状态
     */
    private String policyStatus;

    /**
     * 回访状态 是否需要录入回访 1需要;0不需要
     */
    private Integer revisitStatus;

    /**
     * 回访结果 1成功;0失败
     */
    private Integer revisitResult;
    /**
     * 回访时间
     */
    private Date revisitTime;
    /**
     * 回执状态 是否需要录入回执 1需要;0不需要
     */
    private Integer receiptStatus;
    /**
     * 回执时间
     */
    private Date receiptTime;
    /**
     * 车险评分等级
     */
    private String vehicleBusinessScore;


    /**
     * 支出端保单信息表主键id
     */
    private Integer costPolicyId;
    /**
     * 合同编号
     */
    private String contractCode;
    /**
     * 单据明细唯一编号
     */
    private String addCostCode;
    /**
     * 事件来源编码
     */
    private String eventSourceCode;


    /**
     * 业务类型 1:城市业务、2:农村业务
     */
    private Integer businessType;

    /**
     * 记账日期
     */
    private Date settlementDate;
    /**
     * 记账时间
     */
    private Date settlementTime;
    /**
     * 记账生成类型;1 事件、2系统冲正、3 线下对账单确认
     */
    private Integer settlementGenerateType;
    /**
     * 记账事件编码;新单、续期、保全、费率调整、其他
     */
    private String settlementEventCode;

    /**
     * 初始事件编码，做冲正时直接从源记录上获取
     */
    private String initialEventCode;
    /**
     * 记账事件摘要说明;新契约、犹豫期退保
     */
    private String settlementEventDesc;
    /**
     * 科目编码
     */
    private String settlementSubjectCode;
    /**
     * 科目名称
     */
    private String settlementSubjectName;
    /**
     * 结算机构编码
     */
    private String settlementInstitution;
    /**
     * 结算机构名称
     */
    private String settlementInstitutionName;


    /**
     * 佣金归属人渠道编码
     */
    private String ownerChannelCode;
    /**
     * 佣金归属人类型， 0代理人，1推荐人 2农保推荐人、3 渠道推荐人 9其他
     */
    private Integer ownerType;
    /**
     * 佣金归属人编码
     */
    private String ownerCode;
    /**
     * 佣金归属人名字
     */
    private String ownerName;
    /**
     * 佣金归属人机构编码
     */
    private String ownerOrgCode;
    /**
     * 佣金归属人机构名称
     */
    private String ownerOrgName;
    /**
     * 佣金归属人第三方编码(如：农保工号)
     */
    private String ownerThirdCode;
    /**
     * 佣金归属人第三方机构编码(如：农保机构编码)
     */
    private String ownerThirdOrg;
    /**
     * 佣金归属人第三方上级工号
     */
    private String ownerThirdSuperiorCode;

    /**********佣金信息***************/
    /**
     * 支出类型 common 基础佣金, add 加佣, red 红包
     */
    private String costType;
    /**
     * 佣金类型 common 新契约/续保佣金,renewal 续期,correct_inc保全增员,correct_dec 保全减员,correct_surrender 保全退保
     */
    private String commissionType;

    /**
     * 保费(计算用)
     */
    private BigDecimal premium;
    /**
     * 加佣/红包活动编码
     */
    private String activityCode;

    /**
     * 加佣/红包活动名称
     */
    private String activityName;
    /**
     * 支出佣金配置比例对应key
     */
    private String costConfigKey;
    /**
     * 代理人分佣比例
     */
    private BigDecimal costDivideRate;
    /**
     * 支出佣金配置比例
     */
    private BigDecimal costRate;
    /**
     * 支出佣金实际计算比例
     */
    private BigDecimal costActualRate;
    /**
     * 支出金额
     */
    private BigDecimal costAmount;
    /**
     * 发放月份
     */
    private String grantMonth;

    /**
     * 发放比例
     */
    private BigDecimal grantRate;

    /**
     * 发放佣金金额 = 支出佣金 * 发放比例
     */
    private BigDecimal grantAmount;


    /**********冲正信息***************/
    /**
     * 冲正数据标志，0未冲正 ,1 已冲正（包括冲正数据和被冲正数据）
     */
    private Integer correctionFlag;
    /**
     * 被冲正的源支出编码
     */
    private String sourceCostCode;
    /**
     * 源记账生成类型;1 事件、2系统冲正,3补发
     */
    private Integer sourceGenerateType;
    /**
     * 冲正类型
     */
    private Integer correctionType;
    /**
     * 冲正操作员
     */
    private String  correctionUser;
    /**
     * 冲正操作类型 0 自动冲正，1 人工冲正
     */
    private Integer  correctionOpType;
    /**
     * 冲正操作时间
     */
    private Date  correctionTime;
    /**
     * 冲正说明
     */
    private String correctionRemark;

    /**********确认信息***************/
    /**
     * 确认状态;0未对账1对账中2已完成对账
     */
    private Integer confirmStatus;
    /**
     * 确认发放时间
     */
    private Date confirmGrantTime;
    /**
     * 确认操作员
     */
    private String confirmUser;
    /**
     * 确认完成时间
     */
    private Date confirmTime;

    /**********被保人信息***************/
    /**
     * 被保人编码
     */
    private String insuredCode;
    /**
     * 被保人年龄
     */
    private Integer insuredPolicyAge;

    /**
     * 被保人姓名
     */
    private String insuredName;

    /**
     * 被保人手机号码
     */
    private String insuredMobile;

    /**
     * 被保人证件号码
     */
    private String insuredIdCard;

    /**
     * 投保人出生日期
     */
    private Integer insuredGender;

    /**
     * 投保人出生日期
     */
    private Date insuredBirthday;

    /**********险种信息***************/
    /**
     * 险种编码
     */
    private String productCode;
    /**
     * 险种名称
     */
    private String productName;
    /**
     * 保单险种状态
     */
    private String productStatus;

    /**
     * 是否续投,0否，1是 续保单和续期都传1
     */
    private Integer insuranceType;
    /**
     * 协议产品编码
     */
    private String protocolProductCode;
    /**
     * 协议产品名称
     */
    private String protocolProductName;
    /**
     * 计划编码
     */
    private String planCode;
    /**
     * 计划名称
     */
    private String planName;
    /**
     * 主险长短险标记 0短险1长险
     */
    private Integer mainLongShortFlag;
    /**
     * 长短险标记 0短险1长险
     */
    private Integer longShortFlag;
    /**
     * 险种大类
     */
    private String productGroup;

    /**
     * 二级分类编码
     */
    private String level2Code;
    /**
     * 三级分类编码
     */
    private String level3Code;

    /**
     * 险种类型
     */
    private String productType;
    /**
     * 是否主险
     */
    private Integer mainInsurance;
    /**
     * 附加险类型;0-其他类型附加险 1-附加投保人豁免
     */
    private Integer additionalRisksType;
    /**
     * 险种生效日期
     */
    private Date effectiveDate;
    /**
     * 险种截止时间
     */
    private String endDate;
    /**
     * 续期年期
     */
    private Integer renewalYear;
    /**
     * 续期期数
     */
    private Integer renewalPeriod;
    /**
     * 应缴时间
     */
    private Date payableTime;

    /**
     * 实缴时间
     */
    private Date realityTime;
    /**
     * 保额
     */
    private BigDecimal coverage;
    /**
     * 保额单位;保额单位，0：元，1：份，2：元/天
     */
    private Integer coverageUnit;
    /**
     * 保额单位名称
     */
    private String coverageUnitName;
    /**
     * 保障期间类型
     */
    private String insuredPeriodType;
    /**
     * 保障时长
     */
    private Integer insuredPeriod;
    /**
     * 缴费方式;年交/半年交/季交/月交/趸交/不定期交/短险一次交清
     */
    private String periodType;
    /**
     * 缴费期间类型
     */
    private String paymentPeriodType;
    /**
     * 缴费时长
     */
    private Integer paymentPeriod;
    /**
     * 年金领取年龄
     */
    private String drawAge;

    /**
     * 险种总保费
     */
    private BigDecimal productPremiumTotal;
    /**
     * 险种保费
     */
    private BigDecimal productPremium;
    /**
     * 份数
     */
    private Integer copies;
    /**********保全信息***************/
    /**
     * 保全申请编号
     */
    private String preservationCode;
    /**
     * 批单号
     */
    private String endorsementNo;
    /**
     * 保全生效时间
     */
    private Date preservationEffectTime;
    /**
     * 批改时间
     */
    private Date endorsementTime;
    /**
     * 保全类型
     */
    private String preservationType;
    /**
     * 保全项目
     */
    private String preservationProject;

    /**
     * 保全期数
     */
    private Integer preservationPeriod;
    /**
     * 退保时间
     */
    private Date surrenderTime;
    /**
     * 退保金额
     */
    private BigDecimal surrenderAmount;
    /**
     * 是否犹豫期退保;-1无0否1是
     */
    private Integer hesitateSurrender=-1;

    /**
     * 一单一议
     */
    private Integer singleProposeFlag;
    /**
     * 自动结算支出编码
     */
    private String autoCostCode;
    /**
     * 单据编号
     */
    private String documentCode;

    private String costSettlementCycle;
    /**
     *退保所在期数
     */
    private Integer surrenderTermPeriod;
    /**
     *附加险解约的险种编码串，以,隔开
     */
    private String terminationProductCode;

    /**
     * 业务保费（承保时与product_premium一直，退保时与surrender_amount一直，冲正时在原值上取反）
     */
    private BigDecimal businessPremium;
    /**
     * 农机险标识
     */
    private Integer agriculturalMachineryFlag;
    /**
     * 业务记账时间 新契约/续保，退保_退保时间
     */
    private Date businessAccountTime;

    /**
     * 车船税
     */
    private BigDecimal vehicleVesselTax;
    /**
     * 车船税率
     */
    private BigDecimal vehicleVesselTaxRate;

    /********结算保单表中数据*******/
    private String applicantName;
    private String companyCode;
    private String companyName;
    /**
     * 投保时间
     */
    private Date applicantTime;
    /**
     * 承保时间
     */
    private Date approvedTime;
    /**
     * 交单时间
     */
    private Date orderTime;
    /**
     * 生效时间
     */
    private Date enforceTime;

    /**
     * 保单类型 个团车财
     */
    @ApiModelProperty("保单类型 个团车财")
    private String policyProductType;

    /**
     * 业务数据类型
     */
    @ApiModelProperty("业务数据类型：原始业务数据、冲正数据、新业务数据")
    private Integer businessDataType;

    /**
     * 整村推进 0否 1是
     */
    @ApiModelProperty("整村推进 0否 1是")
    private Integer ruralProxyFlag;

    /**
     * 订单类型 0普通订单 1分销订单（是否分销单 0否 1是）
     */
    @ApiModelProperty("订单类型 0普通订单 1分销订单")
    private Integer distributionFlag;
    /**
     * 折算保费
     */
    @ApiModelProperty("折算保费")
    private BigDecimal discountPremium;

}
