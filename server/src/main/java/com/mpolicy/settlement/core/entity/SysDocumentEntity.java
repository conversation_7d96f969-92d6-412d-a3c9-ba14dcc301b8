package com.mpolicy.settlement.core.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件存储
 * 
 * @date 2021-01-25 13:58:51
 */
@TableName("sys_document")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SysDocumentEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 *
	 */
	@TableId
	private Integer id;
	/**
	 * 文件编码
	 */
	private String fileCode;
	/**
	 * 业务系统
	 */
	private String fileSystem;
	/**
	 * 业务模块
	 */
	private String fileModule;
	/**
	 * 文件类型
	 */
	private String fileType;
	/**
	 * 文件名称
	 */
	private String fileName;
	/**
	 * 文件后缀
	 */
	private String fileExt;
	/**
	 * 文件大小
	 */
	private long fileSize;
	/**
	 * oss存储路径
	 */
	private String filePath;
	/**
	 * 外部访问路径
	 */
	private String domainPath;
	/**
	 * 附件表关联外键
	 */
	private String relationCode;
	/**
	 * 业务加载状态 0 未加载 1成功
	 */
	private Integer relationStatus;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 逻辑删除 0:存在;1:删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 修改人
	 */
	private String updateUser;
	/**
	 * 修改时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

	/**
	 * 乐观锁
	 */
	@TableField(fill = FieldFill.INSERT)
	@Version
	private long revision;
}
