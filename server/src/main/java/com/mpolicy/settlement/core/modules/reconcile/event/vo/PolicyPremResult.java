package com.mpolicy.settlement.core.modules.reconcile.event.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class PolicyPremResult implements Serializable {

    private static final long serialVersionUID = -582814018366127886L;
    /**
     * 小鲸险种编码
     */
    private String productCode;
    /**
     * 费率的唯一标识
     */
    private String premCode;
    /**
     * 小鲸险种名称
     */
    private String productName;
    /**
     * 费率
     */
    private BigDecimal yearRate;
    /**
     * 税前保费
     */
    private BigDecimal premium;
    /**
     * 结算方式 按全保费(含税)结算 按净保费(税后)结算
     */
    private String settlementMethod;
    /**
     * 税率
     */
    private BigDecimal taxRate;
    /**
     * 税后保费
     */
    private BigDecimal taxAfterPremium;
    /**
     * 车船税（元）
     */
    private BigDecimal vehicleVesselTax;
    /**
     * 车船税费率
     */
    private BigDecimal vehicleVesselTaxRate;
}
