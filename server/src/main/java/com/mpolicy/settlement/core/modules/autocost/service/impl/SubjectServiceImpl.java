package com.mpolicy.settlement.core.modules.autocost.service.impl;

import com.alibaba.fastjson.JSON;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectDetail;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectRecord;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostSubjectInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostSubjectRecordEntity;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostSubjectInfoService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostSubjectRecordService;
import com.mpolicy.settlement.core.modules.autocost.service.SubjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 【自动结算】科目服务接口实现
 *
 * <AUTHOR>
 * @since 2023-10-22 13:15
 */
@Service
@Slf4j
public class SubjectServiceImpl implements SubjectService {

    @Autowired
    private SettlementCostSubjectInfoService settlementCostSubjectInfoService;

    @Autowired
    private SettlementCostSubjectRecordService settlementCostSubjectRecordService;
    @Override
    public CostSubjectInfo queryCostSubjectInfo(String subjectCode) {
        SettlementCostSubjectInfoEntity subject = Optional.ofNullable(settlementCostSubjectInfoService.lambdaQuery()
                .eq(SettlementCostSubjectInfoEntity::getSubjectCode, subjectCode).one())
                .orElseThrow(() -> new RuntimeException("科目信息不存在"));
        // 构建返回科目vo
        CostSubjectInfo result = new CostSubjectInfo();
        BeanUtils.copyProperties(subject, result);
        return result;
    }

    @Override
    public List<CostSubjectInfo> queryCostSubjectInfo(List<String> subjectCodeList) {
        // 1 获取集合信息
        List<SettlementCostSubjectInfoEntity> list = settlementCostSubjectInfoService.lambdaQuery().in(SettlementCostSubjectInfoEntity::getSubjectCode, subjectCodeList).list();
        // 2 遍历获取科目集合信息
        List<CostSubjectInfo>  result = list.stream().map(x ->{
            CostSubjectInfo bean = new CostSubjectInfo();
            BeanUtils.copyProperties(x, bean);
            return bean;
        }).collect(Collectors.toList());
        log.debug("根据科目编码查询科目信息成功，返回结果：{}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public CostSubjectDetail queryCostSubjectDetail(String subjectCode) {

        CostSubjectDetail result = new CostSubjectDetail();
        // 1 获取科目基本信息
        CostSubjectInfo costSubjectInfo = queryCostSubjectInfo(subjectCode);

        // TODO: 2023/11/2 科目详细信息
        return result;
    }

    @Override
    public void saveOrUpdateCostSubjectRecord(CostSubjectRecord record) {
        // 1 判断是否存在科目数据记录，存在返回，不存在new一个
        SettlementCostSubjectRecordEntity subjectRecord = Optional.ofNullable(settlementCostSubjectRecordService.lambdaQuery()
                .eq(SettlementCostSubjectRecordEntity::getCostSettlementCycle, record.getCostSettlementCycle())
                .eq(SettlementCostSubjectRecordEntity::getSubjectCode, record.getSubjectCode())
                .last(" limit 1")
                .one()).orElse(new SettlementCostSubjectRecordEntity());

        // 2 赋值
        BeanUtils.copyProperties(record, subjectRecord);
        // 3 更新
        settlementCostSubjectRecordService.saveOrUpdate(subjectRecord);
    }
}