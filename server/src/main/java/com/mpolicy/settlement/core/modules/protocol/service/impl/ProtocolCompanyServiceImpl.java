package com.mpolicy.settlement.core.modules.protocol.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.mpolicy.settlement.core.modules.protocol.dao.ProtocolCompanyDao;
import com.mpolicy.settlement.core.modules.protocol.entity.ProtocolCompanyEntity;
import com.mpolicy.settlement.core.modules.protocol.service.ProtocolCompanyService;

/**
 * 保司协议保险公司信息表
 *
 * <AUTHOR>
 * @date 2023-05-20 20:31:56
 */
@Slf4j
@Service("protocolCompanyService")
public class ProtocolCompanyServiceImpl extends ServiceImpl<ProtocolCompanyDao, ProtocolCompanyEntity> implements ProtocolCompanyService {

}
