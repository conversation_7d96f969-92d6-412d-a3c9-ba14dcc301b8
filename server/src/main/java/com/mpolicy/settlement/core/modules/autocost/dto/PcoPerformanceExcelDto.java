package com.mpolicy.settlement.core.modules.autocost.dto;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.modules.autocost.enums.SettlementInstitutionEnum;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = true)
@ApiModel("pco暂发excel导入文档")
public class PcoPerformanceExcelDto extends BaseRowModel implements Serializable {
    @ApiModelProperty("统计日期")
    @ExcelProperty(value="统计日期", index = 0)
    Date businessAccountTime;

    @ApiModelProperty("区域名称")
    @ExcelProperty(value="区域名称", index = 1)
    String regionName;

    @ApiModelProperty("发放对象机构名称")
    @ExcelProperty(value="分支名称", index = 2)
    String objectOrgName;

    @ApiModelProperty(value = "发放对象机构编码",hidden = true)
    String objectOrgCode;

    @ApiModelProperty("发放对象编码")
    @ExcelProperty(value="工号", index = 3)
    String sendObjectCode;

    @ApiModelProperty("发放对象名称")
    @ExcelProperty(value="姓名", index = 4)
    String sendObjectName;

    @ApiModelProperty("岗位名称")
    @ExcelProperty(value="岗位名称", index = 5)
    String postName;

    @ApiModelProperty("pco等级")
    @ExcelProperty(value="PCO等级", index = 6)
    String pcoLevel;

    @ApiModelProperty("津贴")
    @ExcelProperty(value="津贴", index = 7)
    BigDecimal allowance;

    @ApiModelProperty("自动结算金额")
    @ExcelProperty(value="绩效奖励", index = 8)
    BigDecimal commissionAmount;

    @ApiModelProperty("是否涉及长期续期率")
    @ExcelProperty(value="是否涉及长期续期率", index = 9)
    String renewalFlag;

    @ApiModelProperty("结算机构名称")
    @ExcelProperty(value="结算机构", index = 10)
    String settlementInstitutionName;

    @ApiModelProperty(value = "结算机构编码" ,hidden = true)
    String settlementInstitution;


    public String getSettlementInstitution() {
        return SettlementInstitutionEnum.getByName(settlementInstitutionName).getCode();
    }

    /**
     * 涉及续期率则发80%
     * @return
     */
    public BigDecimal getGrantRate(){
        if(Objects.equals("是",renewalFlag)){
            return new BigDecimal("80");
        }else{
            return new BigDecimal("100");
        }
    }

    /**
     * 发放金额%
     * @return
     */
    public BigDecimal getGrantAmount(){
        return PolicySettlementUtils.calcGrantAmount(commissionAmount,getGrantRate());
    }

    public static void validate(PcoPerformanceExcelDto dto){
        if(StringUtils.isBlank(dto.getSendObjectCode())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("工号为空"));
        }
        if(StringUtils.isBlank(dto.getSendObjectName())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("工号为{}的姓名为空",dto.getSendObjectCode())));
        }

        if(StringUtils.isBlank(dto.getObjectOrgName())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("工号为{}的机构为空",dto.getSendObjectCode())));
        }

        if(dto.getCommissionAmount()==null){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("工号为{}的绩效金额为空",dto.getSendObjectCode())));
        }

        if(StringUtils.isBlank(dto.getSettlementInstitutionName())){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("工号为{}的结算机构为空",dto.getSendObjectCode())));
        }
    }

}
