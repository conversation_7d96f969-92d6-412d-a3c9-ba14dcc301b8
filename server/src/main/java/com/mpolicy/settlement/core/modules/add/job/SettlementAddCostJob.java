package com.mpolicy.settlement.core.modules.add.job;

import com.mpolicy.settlement.core.modules.add.entity.SettlementCommissionSyncRecordEntity;
import com.mpolicy.settlement.core.modules.add.enums.SyncRecordStatusEnum;
import com.mpolicy.settlement.core.modules.add.service.SettlementAddCostManageService;
import com.mpolicy.settlement.core.modules.add.service.SettlementCommissionSyncRecordService;
import com.netflix.discovery.converters.Auto;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Component
@Slf4j
public class SettlementAddCostJob {

    @Autowired
    private SettlementCommissionSyncRecordService settlementCommissionSyncRecordService;
    @Autowired
    private SettlementAddCostManageService settlementAddCostManageService;
    @XxlJob("commissionSyncRecordHandler")
    public void commissionSyncRecordHandler() {
        XxlJobHelper.log("开始处理加佣批量同步记录");
        List<SettlementCommissionSyncRecordEntity> lst =  settlementCommissionSyncRecordService.lambdaQuery()
                .in(SettlementCommissionSyncRecordEntity::getStatus, Arrays.asList(SyncRecordStatusEnum.UN_DO.getCode(),SyncRecordStatusEnum.DOING.getCode(),SyncRecordStatusEnum.FAIL.getCode()))
                .list();
        if(CollectionUtils.isEmpty(lst)){
            log.info("无处理加佣批量同步记录");
            return;
        }
         lst.stream().forEach(l->{
             settlementAddCostManageService.batchSyncNotifyHandler(l);
         });
        XxlJobHelper.log("加佣批量同步记录处理完成");
    }

    @XxlJob("syncConfirmStatusToMarketingHandler")
    public void syncConfirmStatusToMarketingHandler() {
        XxlJobHelper.log("开始同步确认状态给营销系统");
        settlementAddCostManageService.syncConfirmStatusToMarketing();
        XxlJobHelper.log("完成同步确认状态给营销系统");

    }
}
