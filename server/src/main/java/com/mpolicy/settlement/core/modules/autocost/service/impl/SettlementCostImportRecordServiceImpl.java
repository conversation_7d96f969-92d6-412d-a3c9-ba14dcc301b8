package com.mpolicy.settlement.core.modules.autocost.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.common.autocost.SettlementCostImportOperationRecordQuery;
import com.mpolicy.settlement.core.common.autocost.SettlementCostImportRecordDelete;
import com.mpolicy.settlement.core.common.autocost.SettlementCostImportRecordQuery;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.dao.SettlementCostDynamicSubjectErrorDataDao;
import com.mpolicy.settlement.core.modules.autocost.dao.SettlementCostImportRecordDao;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.SettlementCostInfoDto;
import com.mpolicy.settlement.core.modules.autocost.dto.base.SettlementInstitutionInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.data.CostSubjectDataRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.dynamic.DynamicReadData;
import com.mpolicy.settlement.core.modules.autocost.dto.dynamic.SubjectDataDynamicBase;
import com.mpolicy.settlement.core.modules.autocost.dto.dynamic.SubjectDataDynamicRule;
import com.mpolicy.settlement.core.modules.autocost.entity.*;
import com.mpolicy.settlement.core.modules.autocost.enums.DynamicSubjectDataRuleEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.ImportRecordOpTypeEnum;
import com.mpolicy.settlement.core.modules.autocost.helper.SettlementInstitutionHelper;
import com.mpolicy.settlement.core.modules.autocost.listener.DynamicSubjectDataApportionListener;
import com.mpolicy.settlement.core.modules.autocost.listener.DynamicSubjectDataInfoListener;
import com.mpolicy.settlement.core.modules.autocost.listener.DynamicSubjectDataRuleListener;
import com.mpolicy.settlement.core.modules.autocost.service.*;
import com.mpolicy.settlement.core.modules.autocost.utils.AutoCostUtils;
import com.mpolicy.settlement.core.modules.autocost.utils.SettlementCostExcelUtil;
import com.mpolicy.settlement.core.service.SysDocumentService;
import com.mpolicy.settlement.core.service.common.SettlementPolicyBaseClient;
import com.mpolicy.web.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 结算文件导入文件记录接口
 *
 * <AUTHOR>
 * @since 2023-11-27 20:24
 */
@Slf4j
@Service
public class SettlementCostImportRecordServiceImpl extends ServiceImpl<SettlementCostImportRecordDao, SettlementCostImportRecordEntity> implements SettlementCostImportRecordService {

    @Autowired
    private SettlementCostImportOperationRecordService settlementCostImportOperationRecordService;


    public IPage<SettlementCostImportRecordEntity> pageSettlementCostImportRecordEntity(SettlementCostImportRecordQuery query){
        return this.page(new Page<SettlementCostImportRecordEntity>(query.getPage(),query.getLimit()),
                new LambdaQueryWrapper<SettlementCostImportRecordEntity>()
                        .eq(StringUtils.isNotBlank(query.getFileType()),
                                SettlementCostImportRecordEntity::getFileType, query.getFileType())
                        .eq(StringUtils.isNotBlank(query.getCostSettlementCycle()),
                                SettlementCostImportRecordEntity::getCostSettlementCycle,query.getCostSettlementCycle())
                        .orderByDesc(SettlementCostImportRecordEntity::getUpdateTime));
    }

    public IPage<SettlementCostImportOperationRecordEntity> pageSettlementCostImportOperationRecordEntity(SettlementCostImportOperationRecordQuery query){
        return settlementCostImportOperationRecordService.page(new Page<SettlementCostImportOperationRecordEntity>(query.getPage(),query.getLimit()),
                new LambdaQueryWrapper<SettlementCostImportOperationRecordEntity>()
                        .eq(StringUtils.isNotBlank(query.getFileType()),
                                SettlementCostImportOperationRecordEntity::getFileType, query.getFileType())
                        .eq(StringUtils.isNotBlank(query.getCostSettlementCycle()),
                                SettlementCostImportOperationRecordEntity::getCostSettlementCycle,query.getCostSettlementCycle())
                        .ge(query.getStartTime()!=null,SettlementCostImportOperationRecordEntity::getCreateTime,query.getStartTime())
                        .le(query.getEndTime()!=null,SettlementCostImportOperationRecordEntity::getCreateTime,query.getEndTime())
                        .orderByDesc(SettlementCostImportOperationRecordEntity::getCreateTime));
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addImportRecord(SettlementCostSubjectDataDynamicApplyEntity apply){
        SettlementCostImportRecordEntity importRecord = this.lambdaQuery()
                .eq(SettlementCostImportRecordEntity::getFileType,apply.getFileType())
                .eq(SettlementCostImportRecordEntity::getCostSettlementCycle,apply.getCostSettlementCycle())
                .one();
        if(Objects.isNull(importRecord)){
            SettlementCostImportRecordEntity entity = new SettlementCostImportRecordEntity();
            BeanUtils.copyProperties(apply,entity);
            entity.setId(null);
            entity.setCurrentApplyCode(apply.getApplyCode());
            this.getBaseMapper().insert(entity);

            SettlementCostImportOperationRecordEntity operation = new SettlementCostImportOperationRecordEntity();
            BeanUtils.copyProperties(entity,operation);
            operation.setOpType(ImportRecordOpTypeEnum.INSERT.getCode());
            settlementCostImportOperationRecordService.getBaseMapper().insert(operation);

        }else{
            this.lambdaUpdate().set(SettlementCostImportRecordEntity::getCurrentApplyCode,apply.getApplyCode())
                    .set(SettlementCostImportRecordEntity::getFileName,apply.getFileName())
                    .set(SettlementCostImportRecordEntity::getDomainPath,apply.getDomainPath())
                    .set(SettlementCostImportRecordEntity::getUpdateUser,apply.getCreateUser())
                    .set(SettlementCostImportRecordEntity::getUpdateTime,apply.getCreateTime())
                    .eq(SettlementCostImportRecordEntity::getId,importRecord.getId())
                    .update();

            SettlementCostImportOperationRecordEntity operation = new SettlementCostImportOperationRecordEntity();
            BeanUtils.copyProperties(apply,operation);
            operation.setId(null);
            operation.setOpType(ImportRecordOpTypeEnum.UPDATE.getCode());
            operation.setCurrentApplyCode(apply.getApplyCode());
            operation.setBeforeApplyCode(importRecord.getCurrentApplyCode());
            operation.setBeforeFileName(importRecord.getFileName());
            operation.setBeforeDomainPath(importRecord.getDomainPath());
            settlementCostImportOperationRecordService.getBaseMapper().insert(operation);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteImportRecord(SettlementCostImportRecordDelete recordDelete){
        SettlementCostImportRecordEntity importRecord = this.lambdaQuery()
                .eq(SettlementCostImportRecordEntity::getId,recordDelete.getId())
                .one();
        if(!Objects.equals(importRecord.getFileType(),recordDelete.getFileType())){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("删除记录的文件类型与传入参数的文件类型不一致"));
        }
        if(!Objects.equals(importRecord.getCostSettlementCycle(),recordDelete.getCostSettlementCycle())){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg("删除记录的周期与传入参数的周期不一致"));
        }
        this.lambdaUpdate().set(SettlementCostImportRecordEntity::getDeleted,
                        -1)
                .set(SettlementCostImportRecordEntity::getUpdateUser,recordDelete.getUserName())
                .set(SettlementCostImportRecordEntity::getUpdateTime,new Date())
                .eq(SettlementCostImportRecordEntity::getId,importRecord.getId())
                .update();
        SettlementCostImportOperationRecordEntity operation = new SettlementCostImportOperationRecordEntity();
        BeanUtils.copyProperties(importRecord,operation);
        operation.setCurrentApplyCode(importRecord.getCurrentApplyCode());
        operation.setOpType(ImportRecordOpTypeEnum.DELETE.getCode());
        //operation.setCreateTime(new Date());
        operation.setCreateUser(recordDelete.getUserName());
        settlementCostImportOperationRecordService.getBaseMapper().insert(operation);

    }
}