package com.mpolicy.settlement.core.modules.reconcile.event.project.policy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.enums.ReconcileTypeEnum;
import com.mpolicy.settlement.core.enums.SettlementExceptionEnum;
import com.mpolicy.settlement.core.enums.SettlementPolicyHandlerEnum;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.SettlementPolicyFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementPolicyHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.project.common.AbsPolicyCommonEvent;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventCheckResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventData;
import com.mpolicy.settlement.core.modules.reconcile.vo.BasisSettlementPolicyInfoDomain;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 结算事件-【保单中心】保全-保单重新缴费
 */
@Service
@Slf4j
public class PolicyRepayEvent extends AbsPolicyCommonEvent {
    @Override
    public HandleEventCheckResult handleIncomeEventCheck(SettlementEventJobEntity eventJob, Integer reconcileType) {
        // 2 获取保全的批改单号，判断是否存在结算明细纪录
        JSONObject eventData = JSONObject.parseObject(eventJob.getEventRequest());
        String preservationCode = eventData.getString("preservationCode");
        if (StrUtil.isBlank(preservationCode)) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg(
                    StrUtil.format("保全申请编码不存在,数据结构异常,不做处理 PushEventCode={}", eventJob.getPushEventCode())).build();
        }
        // 3 根据保全操作唯一编号判断是否纪录
        Integer count = settlementPolicyInfoService.lambdaQuery()
                .eq(SettlementPolicyInfoEntity::getPolicyNo, eventJob.getEventBusinessCode())
                .eq(SettlementPolicyInfoEntity::getReconcileType, reconcileType)
                .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode())
                .eq(SettlementPolicyInfoEntity::getPreservationCode, preservationCode)
                .count();
        // 如果存在纪录
        if (count > 0) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg(StrUtil.format("该保全【保单续费】纪录存在结算明细数据，保单号={} 保全申请单号={},reconcileType={}", eventJob.getEventBusinessCode(), preservationCode, reconcileType)).build();
        }
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleIncomeEvent(SettlementEventJobEntity eventJob, HandleEventData handleEventData) {
        try {
            // 从请求报文里获取期数属性
            JSONObject eventRequest = JSONObject.parseObject(eventJob.getEventRequest());
            String preservationCode = eventRequest.getString("preservationCode");
            if (StrUtil.isBlank(preservationCode)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                        StrUtil.format("保单退费 保单号={}, 缺少保全编码", eventJob.getEventBusinessCode())));
            }
            ReconcileTypeEnum reconcileTypeEnum = ReconcileTypeEnum.matchSearchCode(handleEventData.getReconcileType());
            BasisSettlementPolicyInfoDomain domain = new BasisSettlementPolicyInfoDomain();
            domain.setContractInfo(handleEventData.getPolicyInfo());
            domain.setSettlementEventTypeEnum(SettlementEventTypeEnum.POLICY_REPAY);
            domain.setReconcileTypeEnum(reconcileTypeEnum);
            domain.setPreservationCode(preservationCode);
            domain.setEventSourceCode(eventJob.getPushEventCode());
            // 获取处理事件
            SettlementPolicyHandler handler =
                    SettlementPolicyFactory.getInvoke(SettlementPolicyHandlerEnum.PRESERVATION);
            // 构建结算明细基础信息
            List<SettlementPolicyInfoEntity> settlementPolicyInfoList = handler.buildBasisSettlementPolicyInfo(domain);
            if (CollUtil.isNotEmpty(settlementPolicyInfoList)) {
                //匹配产品信息
                handler.matchInsuranceProduct(reconcileTypeEnum, settlementPolicyInfoList);
                // 构建手续费和折标保费金额
                List<SettlementPolicyInfoEntity> resultList =
                        handler.buildSettlementSubjectAmount(settlementPolicyInfoList);
                if (!resultList.isEmpty()) {
                    settlementPolicyInfoService.saveBatch(resultList);
                }
            } else {
                String msg = StrUtil.format("没有生成结算明细，保单号={}, 事件编码={}", eventJob.getEventBusinessCode(),
                        eventJob.getPushEventCode());
                log.info(msg);
                return msg;
            }
        } catch (GlobalException e) {
            throw e;
        } catch (Exception e) {
            String msg = StrUtil.format("生成结算明细异常，保单号={}, 事件编码={}", eventJob.getEventBusinessCode(),
                    eventJob.getPushEventCode());
            log.warn(msg, e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(msg));
        }
        return "success";
    }

    @Override
    public HandleEventCheckResult handleCostEventCheck(SettlementEventJobEntity eventJob) {
        JSONObject eventData = JSONObject.parseObject(eventJob.getEventRequest());
        //退费后重新缴费变更是内部事件，与保司无关，所以不需要根据保司保全号判断记录是否存在
        // 1 根据保全操作唯一编号判断是否存在支出明细记录
        Integer count = settlementCostInfoService.lambdaQuery()
                .eq(SettlementCostInfoEntity::getCorrectionFlag, 0)
                .eq(SettlementCostInfoEntity::getPreservationCode, eventData.getString("preservationCode"))
                .count();
        // 如果存在纪录
        if (count > 0) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg(StrUtil.format("支出端-该退费后重新缴费变更保全已经支出结算明细数据，保单号={} 保全申请单号={}", eventJob.getEventBusinessCode(), eventJob.getEventBusinessCode())).build();
        }


        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleCostEvent(SettlementEventJobEntity eventJob, HandleEventData eventData) {
        EpContractInfoVo policyInfo = eventData.getPolicyInfo();
        settlementCostProcessService.validParam(eventJob, null);
        // 2 获取保单类型
        PolicyProductTypeEnum policyProductTypeEnum =
                PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        if(!Objects.equals(policyProductTypeEnum.getCode(),policyProductTypeEnum.PERSONAL.getCode())){
            throw new GlobalException(SettlementExceptionEnum.C_B_POLICY_REPAY_ONLY_SUPPORT_PERSONAL.getException(StrUtil.format("支出端-保单退费后续缴保全支持个险")));
        }
        //2、获取所有小鲸险种集合
        Map<String, ProductBase> productMap =
                settlementCostProcessService.getProductBaseMap(policyInfo.getProductInfoList());
        //3、支出端-创建保单基础信息
        SettlementCostPolicyInfoEntity costPolicy =
                settlementCostPolicyInfoService.getCostPolicyInfoEntityByContractCode(policyInfo.getContractCode());
        if (Objects.isNull(costPolicy)) {
            costPolicy = settlementCostProcessService.buildSettlementCostPolicyInfo(policyInfo);
        }

        //验证新契约是否存在
        List<SettlementCostInfoEntity> oldList = settlementCostInfoService.lambdaQuery()
                .eq(SettlementCostInfoEntity::getCorrectionFlag, 0)
                .in(SettlementCostInfoEntity::getInitialEventCode, Arrays.asList(SettlementEventTypeEnum.PERSONAL_NEW_POLICY.getEventCode(), SettlementEventTypeEnum.RENEWAL_POLICY.getEventCode()))
                .eq(SettlementCostInfoEntity::getContractCode, policyInfo.getContractCode())
                .list();
        if (CollectionUtils.isNotEmpty(oldList)) {
            throw new GlobalException(SettlementExceptionEnum.C_B_POLICY_REFUND_NOT_OLD_RECORD.getException(StrUtil.format("支出端-保单退费后续缴保全已存在，单号={} 保全申请单号={}", eventJob.getEventBusinessCode(), eventJob.getEventBusinessCode())));
        }

        //4、生成佣金记录

        //4、生成佣金记录
        List<SettlementCostInfoEntity> costInfoList = null;
        if (CollectionUtils.isNotEmpty(policyInfo.getInsuredInfoList())) {
            costInfoList = settlementCostProcessService.builderSettlementCostInfoByInsuredList(eventJob,
                    CostSubjectEnum.FIRST_BASIC_COMM, SettlementEventTypeEnum.RENEWAL_POLICY, productMap, policyInfo,
                    policyInfo.getInsuredInfoList());
        } else {
            //中和农信渠道的没有被保人明细抛出异常
            if (Objects.equals(policyInfo.getChannelInfo().getChannelCode(), Constant.ZHNX_CHANNEL_CODE)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                        StrUtil.format("保单号={}, 缺少被保人明细", eventJob.getEventBusinessCode())));
            } else {
                costInfoList = settlementCostProcessService.builderSettlementCostInfoByProductList(eventJob,
                        CostSubjectEnum.FIRST_BASIC_COMM, SettlementEventTypeEnum.RENEWAL_POLICY, productMap, policyInfo);
            }
        }

        costInfoList.stream().forEach(c -> {
            c.setSettlementEventCode(handlerEventType().getEventCode());
            c.setSettlementEventDesc(handlerEventType().getEventDesc());
        });
        //处理农保员工在职状态
        settlementCostOwnerService.handlerOwnerThirdStatus(costPolicy, costInfoList);
        //5、保存记录信息
        settlementCostProcessService.saveCostCommissionRecord(eventJob, costPolicy, costInfoList);
        /*
        List<SettlementCostInfoEntity> costInfoList = null;
        if(!Objects.equals(policyProductTypeEnum,PolicyProductTypeEnum.GROUP)){
            if (CollectionUtils.isNotEmpty(policyInfo.getInsuredInfoList())) {
                costInfoList = settlementCostProcessService.builderSettlementCostInfoByInsuredList(eventJob,
                        CostSubjectEnum.FIRST_BASIC_COMM, SettlementEventTypeEnum.PERSONAL_NEW_POLICY, productMap, policyInfo,
                        policyInfo.getInsuredInfoList());
                log.info("生成的记录为:{}", costInfoList);
            } else {

                costInfoList = settlementCostProcessService.builderSettlementCostInfoByProductList(eventJob,
                        CostSubjectEnum.FIRST_BASIC_COMM, SettlementEventTypeEnum.PERSONAL_NEW_POLICY, productMap, policyInfo);

            }
        }else{
            if (Objects.equals(policyInfo.getChannelInfo().getChannelCode(), Constant.ZHNX_CHANNEL_CODE)) {
                List<GroupPreservationInsuredVo> insuredInfoVos =
                        policyCenterBaseClient.listGroupNewInsured(policyInfo.getContractCode());
                if (CollectionUtils.isEmpty(insuredInfoVos)) {

                    throw new GlobalException(SettlementExceptionEnum.C_B_GROUP_INSURED_DETAIL_NOT_EXIST.getException(StrUtil.format("支出端-基础佣金-团险新契约-该保单缺少被保人明细，保单号={}", eventJob.getEventBusinessCode())));
                } else {
                    costInfoList = settlementCostGroupProcessService.builderGroupSettlementCostInfoByInsuredList(eventJob,
                            CostSubjectEnum.FIRST_BASIC_COMM, SettlementEventTypeEnum.GROUP_NEW_POLICY, productMap, policyInfo,
                            null, insuredInfoVos);
                }
            } else {
                costInfoList = settlementCostGroupProcessService.builderGroupSettlementCostInfoByProductList(eventJob,
                        CostSubjectEnum.FIRST_BASIC_COMM, SettlementEventTypeEnum.GROUP_NEW_POLICY, productMap, policyInfo,
                        null);
            }
        }
        costInfoList.stream().forEach(c->{
            c.setSettlementEventCode(handlerEventType().getEventCode());
            c.setSettlementEventDesc(handlerEventType().getEventDesc());
        });


        //处理农保员工在职状态
        settlementCostOwnerService.handlerOwnerThirdStatus(costPolicy,costInfoList);

        //5、保存记录信息
        settlementCostProcessService.saveCostCommissionRecord(eventJob, costPolicy, costInfoList);
        */
        return "success";
    }

    @Override
    public SettlementEventTypeEnum handlerEventType() {
        return SettlementEventTypeEnum.POLICY_REPAY;
    }
}
