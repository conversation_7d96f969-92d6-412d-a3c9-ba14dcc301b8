package com.mpolicy.settlement.core.modules.autocost.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 结算周期方式类型枚举
 *
 * <AUTHOR>
 * @version 2023/11/02
 */
public enum SettlementPeriodEnum {

    /**
     * 结算周期方式类型枚举
     */
    MANUAL("manual", "手动计算"),
    AUTO("auto", "自动计算");

    @Getter
    private final String code;

    @Getter
    private final String name;


    SettlementPeriodEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取结算周期方式类型枚举
     *
     * @param code 类型编码
     * <AUTHOR>
     * @since 2021/11/02
     */
    public static SettlementPeriodEnum deCode(String code) {
        return Arrays.stream(SettlementPeriodEnum.values()).filter(x -> x.code.equals(code)).findFirst().orElse(null);
    }
}
