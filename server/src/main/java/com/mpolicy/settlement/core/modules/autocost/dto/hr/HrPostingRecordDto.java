package com.mpolicy.settlement.core.modules.autocost.dto.hr;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(value = "hr职位信息记录", description = "hr职位信息记录")
public class HrPostingRecordDto {
    /**
     * 区域编码。
     */
    private String regionCode;

    /**
     * 区域名称。
     */
    private String regionName;

    /**
     * 机构编码。
     */
    private String bchCode;

    /**
     * 机构名称。
     */
    private String bchName;

    /**
     * 负责人工号。
     */
    private String employeeCode;

    /**
     * 负责人姓名。
     */
    private String employeeName;

    /**
     * 任职开始日期，关联左闭右闭。
     */
    private String postStartDate;

    /**
     * 任职结束日期（结束当日有效），关联左闭右闭。
     */
    private String postEndDate;
    /**
     * 岗位id。
     */
    private Integer jobId;

    private String jobCode;

    private String postTypeCode;

    /**
     * 入职日期。
     */
    private String joinDate;

    /**
     * 离职日期(最后工作日)。
     */
    private String leaveDate;

}
