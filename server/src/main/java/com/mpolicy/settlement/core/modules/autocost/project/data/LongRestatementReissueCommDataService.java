package com.mpolicy.settlement.core.modules.autocost.project.data;

import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataDynamic;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataRequest;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataRuleInfo;
import com.mpolicy.settlement.core.modules.autocost.project.data.common.DynamicDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 【科目14】长险复效补发数据生成服务
 *
 * <AUTHOR>
 * @since 2023-10-22 17:58
 */
@Service
@Slf4j
public class LongRestatementReissueCommDataService extends DynamicDataService<SubjectDataRequest<String>, SubjectDataDynamic> {

    @Override
    public CostSubjectEnum getSubjectDataEnum() {
        return CostSubjectEnum.LONG_RESTATEMENT_REISSUE_COMM;
    }

    /**
     * 长险复效补发生成数据明细
     */
    @Override
    public int builderCostSubjectData(String costSettlementCycle, CostSubjectInfo subjectInfo, String batchCode, List<SubjectDataRuleInfo> subjectDataRuleList) {
        log.info("【长险复效补发】科目数据计算 构建开始......");
        log.info("【长险复效补发】属于xls动态导入方式，无需处理builder科目数据......");
        log.info("【长险复效补发】科目数据计算 构建完成......");
        return 0;
    }

    @Override
    public int resetSubjectData(String costSettlementCycle) {
        log.info("【农机险绩效】无需操作，直接重置完成了......");
        return 0;
    }

    @Override
    public SubjectDataDynamic subjectData(SubjectDataRequest<String> request) {
        return builderSubjectDataDynamic(request.getCostSettlementCycle(), CostSubjectEnum.AGRICULTURAL_MACHINERY_PERFORMANCE, CostSubjectEnum.LONG_RESTATEMENT_REISSUE_COMM.getCode());
    }
}