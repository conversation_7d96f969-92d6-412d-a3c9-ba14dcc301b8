package com.mpolicy.settlement.core.modules.add.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 外部佣金结算同步记录表
 * 
 * <AUTHOR>
 * @since 2024-04-26 01:15:12
 */
@TableName("settlement_commission_sync_record")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCommissionSyncRecordEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 *  交易流水号
	 */
	private String requestId;
	/**
	 * 佣金类型：add 加佣
	 */
	private String type;
	/**
	 * 同步类型 batch批量、single 单个
	 */
	private String syncType;
	/**
	 * 营销活动编码
	 */
	private Integer activityId;
	/**
	 * 营销活动批次号
	 */
	private String batchNo;
	/**
	 * 营销系统结算状态：0核算中，3作废
	 */
	private Integer settlementState;
	/**
	 * 
	 */
	private Integer status;
	/**
	 * 同步记录数
	 */
	private Integer syncCount;
	/**
	 * 当前已同步成功记录中最大的营销记录id，用于同步过程中被中断的场景且前提是按id排序
	 */
	private Integer successMaxId;
	/**
	 * 业务记账时间 新契约/续保，退保_退保时间
	 */
	private String syncContent;

	private String errorCode;

	private String errorMsg;

	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
@TableField(fill = FieldFill.INSERT)
	private Integer revision;

}
