package com.mpolicy.settlement.core.modules.autocost.service;

import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectDetail;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectRecord;

import java.util.List;

/**
 * 【自动结算】科目服务接口
 *
 * <AUTHOR>
 * @since 2023-10-22 13:09
 */
public interface SubjectService {

    /**
     * 根据科目编码查询科目信息
     *
     * @param subjectCode 科目编码
     * @return 科目基本信息
     * <AUTHOR>
     * @since 2023/11/1 17:51
     */
    CostSubjectInfo queryCostSubjectInfo(String subjectCode);

    /**
     * 根据科目编码集合获取科目信息
     *
     * @param subjectCodeList 科目编码集合
     * @return 科目基本信息集合
     * <AUTHOR>
     * @since 2023/11/2 14:18
     */
    List<CostSubjectInfo> queryCostSubjectInfo(List<String> subjectCodeList);

    /**
     * 根据科目编码查询科目详细信息
     *
     * @param subjectCode 科目编码
     * @return 科目详细信息
     * <AUTHOR>
     * @since 2023/11/2 13:59
     */
    CostSubjectDetail queryCostSubjectDetail(String subjectCode);

    /**
     * 保存或者更新科目数据及结佣运算数据记录
     *`
     * @param record 科目数据及结佣运算数据记录 `
     * <AUTHOR>
     * @since 2023/11/13 09:26
     */
    void saveOrUpdateCostSubjectRecord(CostSubjectRecord record);
}