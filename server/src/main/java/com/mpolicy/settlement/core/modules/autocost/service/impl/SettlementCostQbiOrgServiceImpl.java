package com.mpolicy.settlement.core.modules.autocost.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.autocost.dao.SettlementCostQbiOrgDao;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostQbiOrgEntity;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostQbiOrgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 支出结算机构分支指标维度表
 *
 * <AUTHOR>
 * @since 2023-11-18 13:34:32
 */
@Slf4j
@Service
public class SettlementCostQbiOrgServiceImpl extends ServiceImpl<SettlementCostQbiOrgDao, SettlementCostQbiOrgEntity> implements SettlementCostQbiOrgService {

}
