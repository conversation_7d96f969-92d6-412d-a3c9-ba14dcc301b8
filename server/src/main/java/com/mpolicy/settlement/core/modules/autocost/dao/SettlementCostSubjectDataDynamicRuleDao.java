package com.mpolicy.settlement.core.modules.autocost.dao;

import com.mpolicy.service.common.mapper.ImsBaseMapper;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostSubjectDataDynamicRuleEntity;

/**
 * 科目范围(动态科目)规则
 * 
 * <AUTHOR>
 * @since 2023-11-27 20:42:05
 */
public interface SettlementCostSubjectDataDynamicRuleDao extends ImsBaseMapper<SettlementCostSubjectDataDynamicRuleEntity> {
	
}
