package com.mpolicy.settlement.core.modules.monitor.dto;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Data
public class CostEventRecordYiDaDTO {
    /**
     * 保单号
     */
    private String textField_m8erj5dc;
    /**
     * 批单号
     */
    private String textField_m8erj5dd;
    /**
     * 合同编号
     */
    private String textField_m8erj5dh;
    /**
     * 计算事件
     */
    private String textField_m8erj5df;
    /**
     * 保单中心请求报文
     */
    private String textareaField_mcde6udf;

    /**
     * 异常描述Id
     */
    private String selectField_mcddg3i9;
    /**
     * 异常描述
     */
    private String textareaField_m8erj5dx;

    /**
     * 创建时间
     */
    private String textField_m8erj5dv;


    public static String ofStr(JSONObject dto) {
        if (dto == null) {
            return null;
        }
        CostEventRecordYiDaDTO yidaDTO = new CostEventRecordYiDaDTO();

        yidaDTO.setTextField_m8erj5dc(StringUtils.isNotBlank(dto.getString("policyNo")) ? dto.getString("policyNo") : null);
        yidaDTO.setTextField_m8erj5dd(StringUtils.isNotBlank(dto.getString("endorsementNo")) ? dto.getString("endorsementNo") : null);
        yidaDTO.setTextField_m8erj5dh(StringUtils.isNotBlank(dto.getString("contractCode")) ? dto.getString("contractCode") : null);
        yidaDTO.setTextField_m8erj5df(StringUtils.isNotBlank(dto.getString("eventDesc")) ? dto.getString("eventDesc") : null);
        yidaDTO.setSelectField_mcddg3i9(StringUtils.isNotBlank(dto.getString("costErrorCode")) ? dto.getString("costErrorCode") : null);
        yidaDTO.setTextareaField_m8erj5dx(StringUtils.isNotBlank(dto.getString("costEventMessage")) ? dto.getString("costEventMessage") : null);
        yidaDTO.setTextField_m8erj5dv(dto.get("createTime")!=null? DateUtil.formatDateTime(dto.getDate("createTime")):null);
        yidaDTO.setTextareaField_mcde6udf(StringUtils.isNotBlank(dto.getString("eventRequest")) ? dto.getString("eventRequest") : null);
        return JSON.toJSONString(yidaDTO);
    }
}
