package com.mpolicy.settlement.core.modules.autocost.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@TableName("settlement_cost_dynamic_subject_error_data")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostDynamicSubjectErrorDataEntity implements Serializable {
    /**
     * id
     */
    @TableId
    private Integer id;
    /**
     * 动态科目申请编号
     */
    @ApiModelProperty(value = "动态科目申请编号", example = "KMH")
    private String applyCode;

    /**
     * 所属周期
     */
    @ApiModelProperty(value = "所属周期", example = "202311")
    private String costSettlementCycle;


    @ApiModelProperty(value = "行数", hidden = true)
    private Integer rowNum;
    @ApiModelProperty(value = "sheet下标", hidden = true)
    private Integer sheetIndex;
    @ApiModelProperty(value = "sheet名字", hidden = true)
    private String sheetName;
    @ApiModelProperty(value = "原始记录的序号", hidden = true)
    private Integer serialNumber;

    @ApiModelProperty(value = "错误描述", hidden = true)
    private String errorMsg;

    @TableLogic
    private Integer deleted;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @Version
    @TableField(fill = FieldFill.INSERT)
    private Integer revision;
}
