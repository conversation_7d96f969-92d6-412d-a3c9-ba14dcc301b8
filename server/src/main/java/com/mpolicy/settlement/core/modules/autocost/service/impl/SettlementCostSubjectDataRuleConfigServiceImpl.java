package com.mpolicy.settlement.core.modules.autocost.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.autocost.dao.SettlementCostSubjectDataRuleConfigDao;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostSubjectDataRuleConfigEntity;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostSubjectDataRuleConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 支出结算科目数据规则表
 *
 * <AUTHOR>
 * @since 2023-10-31 20:03:47
 */
@Slf4j
@Service
public class SettlementCostSubjectDataRuleConfigServiceImpl extends ServiceImpl<SettlementCostSubjectDataRuleConfigDao, SettlementCostSubjectDataRuleConfigEntity> implements SettlementCostSubjectDataRuleConfigService {

}
