package com.mpolicy.settlement.core.modules.common;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.oss.vo.OssBaseOut;
import com.mpolicy.common.utils.CommonUtils;

import com.mpolicy.settlement.core.entity.SysDocumentEntity;
import com.mpolicy.settlement.core.service.SysDocumentService;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;

/**
 * 导出基础类服务
 *
 * <AUTHOR>
 * @date 2022-01-28 13:29
 */
public class ExportBasicService {

    /***
     * oss文件操作
     */
    @Autowired
    protected StorageService storageService;

    /***
     * 文件存储service
     */
    @Autowired
    protected SysDocumentService sysDocumentService;

    /**
     * <p>
     * 上传文件到oss + 保存到document
     * </p>
     *
     * @param file file
     * @param fileModelEnum 文件类型
     * @param modelCode 文件模块
     * @return void
     * <AUTHOR>
     * @since 2022/1/28
     */
    protected OssBaseOut uploadExportReport(File file, String modelCode,long millis) {
        OssBaseOut ossResult = storageService.uploadFileInputSteam("settlement/reconcile/".concat(FileUtil.getName(file)), file);
        saveFileDocument(file, modelCode, DateUtil.today(), ossResult,millis);
        return ossResult;
    }

    private void saveFileDocument(File file, String modelCode, String relationCode, OssBaseOut ossResult,long millis) {
        SysDocumentEntity bean = new SysDocumentEntity();
        // 文件大小
        bean.setFileSize(file.length());
        bean.setFileName(FileUtil.getName(file));
        bean.setFileType(FileUtil.getType(file));
        bean.setFileExt(FileUtil.getSuffix(file));
        // 文件编码
        bean.setFileCode(CommonUtils.createCode("oss"));
        // oss访问
        bean.setFilePath(ossResult.getFilePath());
        bean.setDomainPath(ossResult.getAccessDomainPath());
        bean.setFileSystem("settlement");
        bean.setFileModule(modelCode);
        bean.setRelationCode(relationCode);
        bean.setRemark(String.valueOf(millis));
        sysDocumentService.save(bean);
    }
}
