package com.mpolicy.settlement.core.modules.protocol.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.mpolicy.settlement.core.modules.protocol.dao.ProtocolBasicInfoDao;
import com.mpolicy.settlement.core.modules.protocol.entity.ProtocolBasicInfoEntity;
import com.mpolicy.settlement.core.modules.protocol.service.ProtocolBasicInfoService;

/**
 * 保司协议签署信息主表
 *
 * <AUTHOR>
 * @date 2023-05-20 20:31:56
 */
@Slf4j
@Service("protocolBasicInfoService")
public class ProtocolBasicInfoServiceImpl extends ServiceImpl<ProtocolBasicInfoDao, ProtocolBasicInfoEntity> implements ProtocolBasicInfoService {

}
