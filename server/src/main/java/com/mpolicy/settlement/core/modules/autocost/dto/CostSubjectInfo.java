package com.mpolicy.settlement.core.modules.autocost.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 自动结算科目基本信息
 *
 * <AUTHOR>
 * @since  2023-11-01 19:50
 */
@Data
@ApiModel(value = "自动结算科目基本信息", description = "自动结算科目基本信息")
public class CostSubjectInfo implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 科目编码
     */
    @ApiModelProperty(value = "科目编码", example = "PS2020202020202020")
    private String subjectCode;

    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称", example = "PCO津贴")
    private String subjectName;

    /**
     * 科目类型;科目类型（固定科目/自定义科目）
     */
    @ApiModelProperty(value = "科目名称", example = "PCO津贴")
    private String subjectType;

    /**
     * 依赖科目编码集合
     */
    @ApiModelProperty(value = "依赖科目编码集合", example = "PS2020202020202020,PS2020202020202022")
    private String dependentSubjectCodeList;

    /**
     * 科目发放对象
     */
    @ApiModelProperty(value = "科目发放对象", example = "PCO、主任")
    private String subjectObject;

    /**
     * 科目计算周期
     */
    @ApiModelProperty(value = "科目计算周期", example = "月结")
    private String subjectCycle;

    /**
     * 科目计算方式
     */
    @ApiModelProperty(value = "科目计算方式", example = "手动")
    private String subjectCalcWay;

    /**
     * 科目描述
     */
    @ApiModelProperty(value = "科目描述", example = "科目描述")
    private String subjectDesc;

    /**
     * 科目状态;0待生效-1生效
     */
    @ApiModelProperty(value = "科目状态;0待生效-1生效", example = "1")
    private Integer subjectStatus;
}
