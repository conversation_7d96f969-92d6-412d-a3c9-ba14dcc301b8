package com.mpolicy.settlement.core.modules.autocost.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

/**
 * 支出结算科目记录表
 * 
 * <AUTHOR>
 * @since 2023-10-31 20:03:47
 */
@TableName("settlement_cost_subject_record")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostSubjectRecordEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 所属周期
	 */
	private String costSettlementCycle;
	/**
	 * 科目编号
	 */
	private String subjectCode;
	/**
	 * 批次号
	 */
	private String batchCode;
	/**
	 * 科目数据总数量
	 */
	private Integer subjectDataSize;
	/**
	 * 科目数据完成时间
	 */
	private Date subjectDataFinishTime;
	/**
	 * 科目结佣总数量
	 */
	private Integer subjectCostSize;
	/**
	 * 科目结佣总金额
	 */
	private BigDecimal subjectCostCash;
	/**
	 * 科目结佣支出完成时间
	 */
	private Date subjectCostFinishTime;
	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
