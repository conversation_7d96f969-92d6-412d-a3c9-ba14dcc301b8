package com.mpolicy.settlement.core.modules.reconcile.dto.cache;

import com.mpolicy.settlement.core.modules.reconcile.dto.employee.EmployeePostRecord;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * 员工任职记录缓存
 *
 * <AUTHOR>
 * @since 2023-11-28 12:21
 */
@Data
@ApiModel(value = "员工任职记录缓存", description = "员工任职记录缓存")
public class EmployeePostRecordCache extends EmployeePostRecord implements Serializable {
    /**
     * 缓存开始时间 日期
     */
    private String cacheDate;
}
