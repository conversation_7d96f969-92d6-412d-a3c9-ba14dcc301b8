package com.mpolicy.settlement.core.modules.autocost.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 支出结算机构分支指标维度表
 * 
 * <AUTHOR>
 * @since 2023-11-18 13:34:32
 */
@TableName("settlement_cost_qbi_org")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostQbiOrgEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 分支编码
	 */
	private String orgCode;
	/**
	 * 分支名称
	 */
	private String orgName;
	/**
	 * 继续率
	 */
	private BigDecimal renewalRate;
	/**
	 * 长线推广费
	 */
	private BigDecimal longPromotion;
	/**
	 * 长线结算保费
	 */
	private BigDecimal longSettlementPremium;
	/**
	 * 短线推广费
	 */
	private BigDecimal shortPromotion;
	/**
	 * 短线结算保费
	 */
	private BigDecimal shortSettlementPremium;
	/**
	 * 农机险推广费
	 */
	private BigDecimal agriculturalPromotion;
	/**
	 * 农机险结算佣金费
	 */
	private BigDecimal agriculturalSettlementPremium;
	/**
	 * 整村推进推广费
	 */
	private BigDecimal ruralProxyPromotion;
	/**
	 * 整村推进算佣金费
	 */
	private BigDecimal ruralProxyPremium;
	/**
	 * 短期险折标保费
	 */
	private BigDecimal shortAssessConvertInsurancePremium;


	/**
	 * 长期险折标保费
	 */
	private BigDecimal longAssessConvertInsurancePremium;
	/**
	 * pco等级
	 */
	private String pcoLevel;
	/**
	 * 注册客户数
	 */
	private Integer registerUserCount;

	/**
	 * 人均注册客户数
	 */
	private BigDecimal avgRegisterUserCount;

	/**
	 * 应缴金额
	 */
	private BigDecimal payAmt;
	/**
	 * 实缴金额
	 */
	private BigDecimal actPayAmt;
	/**
	 *
	 */
	private String pcoEmployeeCode;
	/**
	 *
	 */
	private String pcoEmployeeName;

	/**
	 * 生服库存盘点率
	 */
	private BigDecimal smCheckSignTaskRadio;
	/**
	 * 酒水个人仓建仓率
	 */
	private BigDecimal personalWarehouseCreateRadio;
	/**
	 * 当月酒水个人仓使用率
	 */
	private BigDecimal smPersonalWarehouseUseRadio;
	/**
	 * 多次驳回率
	 */
	private BigDecimal smRejectManyRate;
	/**
	 * pco培训会次数
	 */
	private Integer smPcoTraining;

	/**
	 * 当月酒水营收
	 */
	private BigDecimal smWineRevenueAmt;


	/**
	 * 快照分区
	 */
	private Integer pt;
	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
