package com.mpolicy.settlement.core.modules.govern.dto.proejct;

import com.mpolicy.settlement.core.modules.govern.dto.base.PolicyDataReviseBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 【业务7】续期实收时间变更
 *
 * <AUTHOR>
 * @date 2024-01-25 14:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "续期实收时间变更数据对象", description = "续期实收时间变更数据对象")
public class ChangeRenewalRealityTimeData extends PolicyDataReviseBase implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号", required = true, example = "A000001")
    @NotBlank(message = "保单号为空")
    private String policyCode;

    /**
     * 调整期次
     */
    @ApiModelProperty(value = "指定期次", required = true, example = "1")
    private Integer period;

    /**
     * 调整续期实收时间
     */
    @ApiModelProperty(value = "调整续期实收时间", required = true)
    @NotBlank(message = "调整续期实收时间为空")
    private Date paymentTime;
}