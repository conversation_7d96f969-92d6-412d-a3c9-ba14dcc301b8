package com.mpolicy.settlement.core.modules.autocost.common;

import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.dto.data.CostSubjectDataRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataRuleInfo;
import com.mpolicy.settlement.core.modules.autocost.service.SubjectDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 科目基础数据服务接口
 *
 * <AUTHOR>
 * @since 2023-10-22 13:01
 */
@Slf4j
public class SubjectDataBasicService extends SubjectBasicService {

    /**
     * 科目数据服务
     */
    @Autowired
    protected SubjectDataService subjectDataService;

    /**
     * 根据科目类型获取科目规则信息
     *
     * @param costSubject 科目枚举类型
     * @return 科目数据规则集合
     * <AUTHOR>
     * @since 2023/11/1 17:53
     */
    protected List<SubjectDataRuleInfo> getSubjectDataRuleInfo(CostSubjectEnum costSubject) {
        return subjectDataService.getSubjectDataRuleInfo(costSubject.getCode());
    }

    /**
     * 重置结算周期+科目进行设置重置
     *
     * @param costSettlementCycle 结算周期
     * @param subjectCode         科目编码
     * <AUTHOR>
     * @since 2023/11/7 21:57
     */
    protected void resetSubjectDataRecord(String costSettlementCycle, String subjectCode) {
        subjectDataService.resetSubjectDataRecord(costSettlementCycle, subjectCode);
    }

    /**
     * 包括科目数据记录信息
     *
     * @param record 科目数据记录信息
     * <AUTHOR>
     * @since 2023/11/2 21:57
     */
    protected void saveSubjectDataRecord(CostSubjectDataRecord record) {
        subjectDataService.saveSubjectDataRecord(record);
    }

    /**
     * 根据科目编码集合获取科目信息
     *
     * @param costSettlementCycle 结算周期
     * @param subjectCodeList     科目编码集合
     * @return 科目信息
     * <AUTHOR>
     * @since 2023/11/2 21:57
     */
    protected List<CostSubjectDataRecord> queryCostSubjectDataRecordList(String costSettlementCycle, List<String> subjectCodeList) {
        return subjectDataService.queryCostSubjectDataRecordList(costSettlementCycle, subjectCodeList);
    }



    /**
     * 科目构建记录信息
     * 温馨提示：如果有成功的返回对象，没有成功的返回null
     *
     * @param costSettlementCycle 结算周期
     * @param subjectCode         科目编码
     * @return 科目构建记录信息
     * <AUTHOR>
     * @since 2023/11/6 20:41
     */
    protected CostSubjectDataRecord checkSubjectDataSuccess(String costSettlementCycle, String subjectCode) {
        // 获取周期+科目的所有记录信息
        List<CostSubjectDataRecord> subjectDataRecord = subjectDataService.querySubjectDataRecord(costSettlementCycle, subjectCode);
        return subjectDataRecord.stream().filter(x -> x.getSubjectDataStatus() == 1).findFirst().orElse(null);
    }
}