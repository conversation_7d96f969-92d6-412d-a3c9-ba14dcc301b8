package com.mpolicy.settlement.core.modules.autocost.project.calculate;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.dto.CostAutoRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.calculate.CostSubjectCalculateRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataPolicyGroup;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.SupervisorProductQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.enums.AutoCostAmountTypeEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.EmployeeRoleEnum;
import com.mpolicy.settlement.core.modules.autocost.project.calculate.common.ProductCalculateNewService;
import com.mpolicy.settlement.core.utils.LambdaUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 【科目4】【督导绩效暂发】结佣计算
 * @date 2023/11/6 1:11 上午
 * @Version 1.0
 */
@Service
@Slf4j
public class    SupervisorPerformanceCalculateService extends ProductCalculateNewService<SubjectDataPolicyGroup,SupervisorProductQbiInfo> {
    @Override
    public CostSubjectEnum getSubjectDataEnum() {
        return CostSubjectEnum.SUPERVISOR_PERFORMANCE;
    }

    /**
     * 根据源数据初始化 佣金记录信息
     * @param sourceDataList
     * @return
     */
    @Override
    protected List<CostAutoRecord> initCostAutoRecordBySource(String documentCode,CostSubjectCalculateRecord record,List<SubjectDataPolicyGroup> sourceDataList) {
        if(CollectionUtils.isEmpty(sourceDataList)){
            return Collections.EMPTY_LIST;
        }
        List<CostAutoRecord> costAutoRecords = Lists.newArrayList();
        CostAutoRecord costAutoRecord =null;
        for(SubjectDataPolicyGroup group : sourceDataList){
            costAutoRecord = builderCostAutoRecordByPolicyGroup(documentCode,record,EmployeeRoleEnum.SUPERVISOR,AutoCostAmountTypeEnum.PROMOTION,group);
            //costAutoRecord.setRenewalRate(group.getRenewalRate());
            if(group.getLongPromotion()!=null){
                costAutoRecord.setAmount(group.getLongPromotion());
                costAutoRecord.setPremium(group.getLongProductPremium());
            }
            if(group.getShortPromotion()!=null){
                costAutoRecord.setAmount(costAutoRecord.getAmount()!=null?costAutoRecord.getAmount().add(group.getShortPromotion()):group.getShortPromotion());
                costAutoRecord.setPremium(costAutoRecord.getPremium()!=null?costAutoRecord.getPremium().add(group.getShortProductPremium()):group.getShortProductPremium());
            }

            costAutoRecords.add(costAutoRecord);
        }
        return costAutoRecords;
    }

    /**
     * key 注意督导的key不能加入机构编号
     * @param employeeCode
     * @param orgCode
     * @param settlementInstitution
     * @return
     */
    @Override
    public String getItemMapKey(String employeeCode,String orgCode,String settlementInstitution){
        return getSupervisorItemMapKey(employeeCode,settlementInstitution);
    }

    /**
     * 获取分摊数据明细
     * @param costAutoRecordList
     * @param costSettlementCycle
     * @return
     */
    @Override
    public Map<String, List<SupervisorProductQbiInfo>> getPersonalProductQbiInfoMap(List<CostAutoRecord> costAutoRecordList, String costSettlementCycle) {
        List<String> employeeCodes = costAutoRecordList.stream().map(CostAutoRecord::getSendObjectCode).collect(Collectors.toList());
        List<SupervisorProductQbiInfo> dtos = settlementCostQbiService.listSupervisorProductQbi(employeeCodes,costSettlementCycle);
        return LambdaUtils.groupBy(dtos, supervisorProductQbiInfo -> getSupervisorItemMapKey(supervisorProductQbiInfo.getEmployeeCode(),supervisorProductQbiInfo.getSettlementOrgCode()));

    }

    /**
     * 创建规则脚本入参
     * @param costAutoRecord
     * @return
     */
    @Override
    public Map<String, Object> createScriptParams(CostAutoRecord costAutoRecord) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("renewalRate",costAutoRecord.getRenewalRate());
        return params;
    }

    /**
     * 校验规则脚本后的数据
     * @param costAutoRecord
     */
    @Override
    protected void checkCostAutoRecord(CostAutoRecord costAutoRecord) {

    }

    /**
     * 计算总额
     * @param costAutoRecord
     */
    @Override
    public void calcCostAutoRecordTotal(CostAutoRecord costAutoRecord) {
        costAutoRecord.setCommissionAmount(calcCommissionAmt(costAutoRecord.getAmount(),costAutoRecord.getCommissionRate()));
        costAutoRecord.setGrantAmount(calcGrantAmt(costAutoRecord.getCommissionAmount(),costAutoRecord.getGrantRate()));
    }

    /**
     * 计算明细
     * @param costAutoRecord
     * @param oldCost
     */
    @Override
    public void calcCostAutoRecordItem(CostAutoRecord costAutoRecord, List<SupervisorProductQbiInfo> oldCost) {
        //todo 是否需要过滤推广费为负数数的
        autoCostProcessService.calcSupervisorProductQbiShare(costAutoRecord,oldCost);
//        if(CollectionUtils.isNotEmpty(costAutoRecord.getAddItemList())){
//            costAutoRecord.setHasTemporary(Constant.NUMBER_YES);
//        }
    }



}
