package com.mpolicy.settlement.core.modules.reconcile.event.project.policy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostCorrectionDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.project.common.AbsPolicyCommonEvent;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventCheckResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.SYSTEM_CORRECTION_USER;
import static com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum.POLICY_RURAL_PROXY_CHANGE;

/**
 * <AUTHOR>
 * @Date 2024/8/22 15:53
 * @Version 1.0
 */
//@Service
@Slf4j
@Service
public class PolicyRuralProxyChangeEvent extends AbsPolicyCommonEvent {
    @Override
    public HandleEventCheckResult handleIncomeEventCheck(SettlementEventJobEntity eventJob, Integer reconcileType) {
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleIncomeEvent(SettlementEventJobEntity eventJob, HandleEventData eventData) {
        return "整村推进变更-success";
    }


    @Override
    public HandleEventCheckResult handleCostEventCheck(SettlementEventJobEntity eventJob) {
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleCostEvent(SettlementEventJobEntity eventJob, HandleEventData handleEventData) {
        JSONObject eventData = JSONObject.parseObject(eventJob.getEventRequest());
        Integer changeType = eventData.getInteger("changeType");

        String policyNo = eventJob.getEventBusinessCode();
        String contractCode = eventJob.getContractCode();

        // 0=四级分销单变成普通单
        // 1=普通单变成四级分销单

        //冲正
        if (StrUtil.isEmpty(policyNo)) {
            return StrUtil.format("保单号-{}不存在-success", policyNo);
        }

        List<SettlementCostInfoEntity> costInfoEntityList = settlementCostInfoService.lambdaQuery()
                .eq(SettlementCostInfoEntity::getContractCode, contractCode)
                .eq(SettlementCostInfoEntity::getCorrectionFlag, 0)
                .list();

        if (CollectionUtil.isEmpty(costInfoEntityList)) {
            return StrUtil.format("保单号/合同号-{}/{}不存在-success", policyNo, contractCode);
        }


        SettlementCostPolicyInfoEntity costPolicy = settlementCostPolicyInfoService.getCostPolicyInfoEntityByContractCode(contractCode);
        if (Objects.isNull(costPolicy)) {
            return "结算保单维度信息缺失-success";
        }

        settlementCostProcessService.validParam(eventJob, null);


        //冲正
        CostCorrectionDto costCorrectionDto = new CostCorrectionDto();

        List<SettlementCostInfoEntity> newCostList = costInfoEntityList.stream().map(
                x -> {
                    //生成一条对冲信息
                    SettlementCostInfoEntity costInfoEntity = settlementCostProcessService.builderOffsetCostInfo(
                            eventJob
                            , handlerEventType()
                            , x, eventData.getDate("createTime")
                            , SYSTEM_CORRECTION_USER
                            , "整村推进变更冲正", Boolean.TRUE
                    );

                    SettlementCostInfoEntity newCostInfoEntity = settlementCostProcessService.builderOriginalChangeNewCostInfo(
                            eventJob
                            , handlerEventType()
                            , x, eventData.getDate("createTime")
                    );

                    if (Objects.equals(changeType, 1)) {
                        newCostInfoEntity.setRuralProxyFlag(1);
                    } else {
                        newCostInfoEntity.setRuralProxyFlag(0);
                    }

                    return Lists.newArrayList(costInfoEntity, newCostInfoEntity) ;
                }
        ).flatMap(Collection::stream).collect(Collectors.toList());

        costCorrectionDto.setNewCostList(newCostList);
        costCorrectionDto.setCorrectionOldIds(costInfoEntityList.stream().map(SettlementCostInfoEntity::getId).collect(Collectors.toList()));
        costCorrectionDto.setPolicy(costPolicy);

        settlementCostCorrectionService.saveCostCommissionRecord(eventJob, costCorrectionDto);

        return "success";




    }

    @Override
    public SettlementEventTypeEnum handlerEventType() {
        return POLICY_RURAL_PROXY_CHANGE;
    }
}
