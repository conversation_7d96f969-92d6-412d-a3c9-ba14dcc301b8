package com.mpolicy.settlement.core.modules.autocost.project.calculate;

import com.google.common.collect.Lists;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.abs.AbsSubjectCalculate;
import com.mpolicy.settlement.core.modules.autocost.dto.CostAutoPolicy;
import com.mpolicy.settlement.core.modules.autocost.dto.CostAutoRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectCalculateRuleConfig;
import com.mpolicy.settlement.core.modules.autocost.dto.SettlementCostInfoDto;
import com.mpolicy.settlement.core.modules.autocost.dto.calculate.CostSubjectCalculateRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataPolicyComm;
import com.mpolicy.settlement.core.modules.autocost.enums.AutoCostAmountTypeEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.CostDataTypeEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.EmployeeRoleEnum;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.utils.LambdaUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 【车险车船税推广费】结佣计算
 * @date 2023/11/6 1:11 上午
 * @Version 1.0
 */
@Service
@Slf4j
public class VehicleVesselTaxCalculateServiceBack extends AbsSubjectCalculate {

    @Override
    public CostSubjectEnum getSubjectDataEnum() {
        return CostSubjectEnum.NULL;
    }


    @Override
    public String handleCostCalculate(CostSubjectCalculateRecord record) {
        log.info("【科目】{}科目数据计算 构建开始......", getSubjectDataEnum().getName());
        /******1、获取科目源数据******/
        List<SubjectDataPolicyComm> sourceDataList = getCalculateSourceData(record.getCostSettlementCycle());
        if(org.apache.commons.collections.CollectionUtils.isEmpty(sourceDataList)){
            log.info("【科目】{}科目数据计算 ......",getSubjectDataEnum().getName());
            return "";
        }



        List<String> costCodes = sourceDataList.stream().map(SubjectDataPolicyComm::getDocumentCode).collect(Collectors.toList());
        List<SettlementCostInfoDto> dtos = settlementBaseCommService.listByCostCode(costCodes);
        Map<String,List<SettlementCostInfoDto>> costInfoMap = LambdaUtils.groupBy(dtos,o->getItemMapKey(o.getOwnerThirdCode(),o.getOwnerOrgCode(),o.getSettlementInstitution()));
        Map<String,List<SubjectDataPolicyComm>> policyCommMap = LambdaUtils.groupBy(sourceDataList,o->getItemMapKey(o.getEmployeeCode(),o.getOrgCode(),o.getSettlementInstitution()));

        List<CostAutoRecord> costAutoRecords = Lists.newArrayList();
        String documentCode = record.getDocumentCode();//generateDocumentCode();

        Map<String, CostSubjectCalculateRuleConfig> ruleConfigMap = costSubjectCalculateRuleService.mapRuleConfig(getSubjectDataEnum().getCode());

        for(String key : costInfoMap.keySet()){
            List<SettlementCostInfoDto> costInfoDtoList = costInfoMap.get(key);
            CostAutoRecord costAutoRecord = builderCostAutoRecord(documentCode,record,EmployeeRoleEnum.ZHNX,
                    AutoCostAmountTypeEnum.VEHICLE_VESSEL_TAX,policyCommMap.get(key).get(0));
            //根据规则获取相关数据
            execScript(ruleConfigMap, Collections.EMPTY_MAP,costAutoRecord);
            //计算分摊和汇总数据
            calcEmployeePolicyShare(costAutoRecord,costInfoDtoList);
            costAutoRecords.add(costAutoRecord);
        }
        return "success";
    }

    /**
     * 督导险种保费分摊计算
     * @param costAutoRecord
     * @param costInfoDtoList (人、机构、结算机构分支必须是同一一个)
     */
    public void calcEmployeePolicyShare(CostAutoRecord costAutoRecord, List<SettlementCostInfoDto> costInfoDtoList){
        List<CostAutoPolicy> costAutoPolicies = Lists.newArrayList();
        Map<String,List<SettlementCostInfoDto>> costMap = LambdaUtils.groupBy(costInfoDtoList,o->o.getPolicyNo()+"_"+o.getProductCode());
        BigDecimal amount = BigDecimal.ZERO,commissionAmount=BigDecimal.ZERO,grantAmount = BigDecimal.ZERO;

        for(String key : costMap.keySet()){
            CostAutoPolicy policy = builderCostAutoPolicy(costAutoRecord,costMap.get(key));
            amount = amount.add(policy.getAmount());
            commissionAmount= amount.add(policy.getCommissionAmount());
            grantAmount = amount.add(policy.getGrantAmount());
            costAutoPolicies.add(policy);
        }
        costAutoRecord.setAmount(amount);
        costAutoRecord.setCommissionAmount(commissionAmount);
        costAutoRecord.setGrantAmount(grantAmount);
        costAutoRecord.setCommissionRate(costAutoPolicies.get(0).getCommissionRate());
        costAutoRecord.setAddItemList(costAutoPolicies);
    }

    /**
     * 创建保单维度分摊信息
     * @param costAutoRecord
     * @param costInfos
     * @return
     */
    private CostAutoPolicy builderCostAutoPolicy(CostAutoRecord costAutoRecord,List<SettlementCostInfoDto> costInfos){
        BigDecimal vehicleVesselTax = costInfos.stream().map(SettlementCostInfoDto::getVehicleVesselTax).reduce(BigDecimal.ZERO,BigDecimal::add);

        CostAutoPolicy policy = new CostAutoPolicy();
        BeanUtils.copyProperties(costAutoRecord,policy);
        policy.setCostPolicyCode(PolicySettlementUtils.createCodeLastNumber(Constant.AUTO_COST_POLICY));
        SettlementCostInfoDto first = costInfos.get(0);
        BigDecimal rate = first.getVehicleVesselTaxRate();

        policy.setProductCode(first.getProductCode());
        policy.setProductName(first.getProductName());
        policy.setProductType(first.getProductType());
        policy.setProductGroup(first.getProductGroup());
        policy.setLevel2Code(first.getLevel2Code());
        //policy.setLevel2Name(first.getLevel2Name());
        policy.setLevel3Code(first.getLevel3Code());
        //policy.setLevel3Name(first.getLevel3Name());
        policy.setLongShortFlag(first.getLongShortFlag());
        policy.setAmount(vehicleVesselTax);
        policy.setCommissionRate(rate);
        policy.setCommissionAmount(calcCommissionAmt(vehicleVesselTax,rate));
        policy.setGrantAmount(calcGrantAmt(costAutoRecord.getCommissionAmount(),costAutoRecord.getGrantRate()));
        return policy;
    }

    private CostAutoRecord builderCostAutoRecord(String documentCode,
                                                 CostSubjectCalculateRecord record,
                                                 EmployeeRoleEnum roleEnum,
                                                 AutoCostAmountTypeEnum amountTypeEnum,
                                                 SubjectDataPolicyComm policyComm){
        CostAutoRecord costAutoRecord = initCostAutoRecord(record.getProgrammeCode(),documentCode,record.getCostSettlementCycle());
        costAutoRecord.setCostSettlementCycle(record.getCostSettlementCycle());
        costAutoRecord.setSendObjectType(roleEnum.getCode());
        costAutoRecord.setSendObjectCode(policyComm.getEmployeeCode());
        costAutoRecord.setSendObjectName(policyComm.getEmployeeName());
        costAutoRecord.setObjectOrgCode(policyComm.getOrgCode());
        costAutoRecord.setObjectOrgName(policyComm.getOrgName());
        costAutoRecord.setSettlementInstitution(policyComm.getSettlementInstitution());
        costAutoRecord.setSettlementInstitutionName(policyComm.getSettlementInstitutionName());

        costAutoRecord.setAmountType(amountTypeEnum.getCode());
        costAutoRecord.setCostDataType(CostDataTypeEnum.POLICY.getCode());
        return costAutoRecord;
    }


    private List<SubjectDataPolicyComm> getCalculateSourceData(String costSettlementCycle){
        return getPolicyCommSourceData(costSettlementCycle);
    }

    @Override
    public Integer resetSubjectCalculate(String costSettlementCycle,String documentCode) {
        return null;
    }
}