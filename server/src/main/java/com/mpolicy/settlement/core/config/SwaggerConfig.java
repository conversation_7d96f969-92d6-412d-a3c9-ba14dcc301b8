package com.mpolicy.settlement.core.config;

import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * Swagger配置
 *
 * <AUTHOR>
 * @date 2019-02-17 23:30
 */
@Configuration
@EnableSwagger2
public class SwaggerConfig {

    @Value("${spring.application.name}")
    private String applicationName;

    /**
     * 是否开启swagger，正式环境一般是需要关闭的，可根据springboot的多环境配置进行设置
     */
    @Value(value = "${swagger.enabled:true}")
    Boolean swaggerEnabled;

    @Bean
    public Docket createRestApi() {
        Docket docket=new Docket(DocumentationType.SWAGGER_2);
        docket=docket.apiInfo(apiInfo())
                // 是否开启
                .enable(swaggerEnabled).select()
                // 扫描注解
                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .paths(PathSelectors.any())
                .build();
        return docket;
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("结算核心服务["+applicationName+"]接口文档")
                .description("结算核心服务接口文档")
                .contact(new Contact("小鲸向海", "https://api-test.xiaowhale.com/doc.html", "<EMAIL>"))
                .version("1.0")
                .build();
    }

}
