package com.mpolicy.settlement.core.modules.autocost.dao;

import com.mpolicy.service.common.mapper.ImsBaseMapper;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostHrGrantPersonInfoEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 结算Hr发放人员表
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface SettlementCostHrGrantPersonInfoDao extends ImsBaseMapper<SettlementCostHrGrantPersonInfoEntity> {

    /**
     * 根据结算周期物理删除Hr发放人员信息(慎用)
     *
     * @param costSettlementCycle 结算周期
     * @return 删除记录数
     */
    @Update("DELETE FROM settlement_cost_hr_grant_person_info WHERE cost_settlement_cycle = #{costSettlementCycle}")
    int deletePhysicalByCostSettlementCycle(@Param("costSettlementCycle") String costSettlementCycle);

    /**
     * 批量更新Hr发放人员信息
     *
     * @param list Hr发放人员信息列表
     * @return 更新记录数
     */
    int batchUpdateHrGrantPersonInfo(@Param("list") List<SettlementCostHrGrantPersonInfoEntity> list);
}
