package com.mpolicy.settlement.core.modules.autocost.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.mpolicy.settlement.core.modules.autocost.dto.base.SettlementCostAccountBatchDto;
import com.mpolicy.settlement.core.modules.autocost.dto.base.SettlementCostAccountInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.base.SettlementCostAccountLog;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.OrgQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.SettlementCostAutoQbiDfp;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostAccountInfoService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostAccountLogService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostAccountManageService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostAutoQbiDfpService;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.utils.LambdaUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.COST_ACCOUNT_LOG;

@Slf4j
@Service
public class SettlementCostAccountManageServiceImpl implements SettlementCostAccountManageService {
    @Autowired
    private SettlementCostAutoQbiDfpService settlementCostAutoQbiDfpService;
    @Autowired
    private SettlementCostAccountInfoService settlementCostAccountInfoService;
    @Autowired
    private SettlementCostAccountLogService settlementCostAccountLogService;

    public SettlementCostAccountBatchDto calcLastMonthNegativeAmount(Date calcTime){

        String pt = DateUtil.format(calcTime,"yyyyMMdd");
        String costSettlementCycle = DateUtil.format(calcTime,"yyyyMM");
        List<SettlementCostAutoQbiDfp> qbiList = settlementCostAutoQbiDfpService.listByCostSettlementCycle(pt,costSettlementCycle);
        if(CollectionUtils.isEmpty(qbiList)){
            return null;
        }
        List<String> sendObjectCodes = qbiList.stream().map(SettlementCostAutoQbiDfp::getSendObjectCode).collect(Collectors.toList());

        List<SettlementCostAccountInfo> accounts = settlementCostAccountInfoService.listBySendObjectCodes(sendObjectCodes);
        List<SettlementCostAccountInfo> updates = Lists.newArrayList();
        List<SettlementCostAccountInfo> adds = Lists.newArrayList();
        Map<String,SettlementCostAccountInfo> accountInfoMap = LambdaUtils.safeToMap(accounts, item-> accountMapKey(item.getObjectOrgCode(),item.getSendObjectCode()));
        List<SettlementCostAccountLog> logs = Lists.newArrayList();
        SettlementCostAccountLog log = null;
        SettlementCostAccountInfo after = null;
        for(SettlementCostAutoQbiDfp qbi : qbiList){
            SettlementCostAccountInfo beforeAccount = accountInfoMap.get(accountMapKey(qbi.getDepartmentCode(),qbi.getSendObjectCode()));
            if(beforeAccount==null){
                after = createAccountInfoByQbi(qbi);
                adds.add(after);
            }else{
                after = new SettlementCostAccountInfo();
                BeanUtils.copyProperties(beforeAccount,after);
                after.setUpdateTime(new Date());
                after.setAmount(beforeAccount.getAmount().add(qbi.getTotalAmt()));
                BigDecimal lastMonthNegativeAmount = beforeAccount.getLastMonthNegativeAmount().add(qbi.getTotalAmt());
                if(lastMonthNegativeAmount.compareTo(BigDecimal.ZERO)>0){
                    lastMonthNegativeAmount = BigDecimal.ZERO;
                }
                after.setLastMonthNegativeAmount(lastMonthNegativeAmount);
                updates.add(after);
            }
            log = createAccountEnterLog(beforeAccount,after);
            logs.add(log);
        }
        SettlementCostAccountBatchDto dto =  new SettlementCostAccountBatchDto();
        dto.setAdds(adds);
        dto.setUpdates(updates);
        dto.setLogs(logs);
        return dto;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void batchSaveCostAccount(SettlementCostAccountBatchDto dto){
        if(Objects.isNull(dto)){
            return ;
        }
        if(CollectionUtils.isNotEmpty(dto.getAdds())){
            settlementCostAccountInfoService.saveList(dto.getAdds());
        }
        if(CollectionUtils.isNotEmpty(dto.getUpdates())){
            settlementCostAccountInfoService.updateCostAccountInfoList(dto.getUpdates());
        }
        if(CollectionUtils.isNotEmpty(dto.getLogs())){
            settlementCostAccountLogService.saveList(dto.getLogs());
        }
    }


    public SettlementCostAccountInfo createAccountInfoByQbi(SettlementCostAutoQbiDfp qbi){
        SettlementCostAccountInfo accountInfo = new SettlementCostAccountInfo();
        accountInfo.setObjectOrgCode(qbi.getDepartmentCode());
        accountInfo.setObjectOrgName(qbi.getDepartmentName());
        accountInfo.setSendObjectCode(qbi.getSendObjectCode());
        accountInfo.setSendObjectName(qbi.getSendObjectName());
        accountInfo.setAccountStatus(1);
        accountInfo.setAmount(qbi.getTotalAmt());
        accountInfo.setCreateTime(new Date());
        accountInfo.setUpdateTime(new Date());
        if(BigDecimal.ZERO.compareTo(qbi.getTotalAmt())>=0){
            accountInfo.setLastMonthNegativeAmount(qbi.getTotalAmt());
        }else{
            accountInfo.setLastMonthNegativeAmount(BigDecimal.ZERO);
        }
        return accountInfo;
    }

    /**
     * 根据结算数据记录入账流水
     * @param before
     * @param after
     * @return
     */
    public SettlementCostAccountLog createAccountEnterLog(SettlementCostAccountInfo before,SettlementCostAccountInfo after){
        SettlementCostAccountLog log =  new SettlementCostAccountLog();
        log.setTransCode(PolicySettlementUtils.createCodeLastNumber(COST_ACCOUNT_LOG+after.getSendObjectCode()));
        log.setTransTime(new Date());
        log.setTransMonth(DateUtil.format(log.getTransTime(),"yyyyMM"));
        //保险目前只有入账，
        log.setTransType(1);
        log.setObjectOrgCode(after.getObjectOrgCode());
        log.setObjectOrgName(after.getObjectOrgName());
        log.setSendObjectCode(after.getSendObjectCode());
        log.setSendObjectName(after.getSendObjectName());
        BigDecimal beforeAmount = before!=null?before.getAmount():BigDecimal.ZERO;
        log.setBeforeAmount(beforeAmount);
        log.setBeforeLastMonthNegativeAmount(before!=null?before.getLastMonthNegativeAmount():BigDecimal.ZERO);
        log.setAddAmount(after.getAmount().subtract(beforeAmount));
        log.setCreateTime(new Date());
        log.setUpdateTime(log.getCreateTime());
        return log;
    }



    private String accountMapKey(String objectOrgCode,String sendObjectCode){
        return StrUtil.format("{}_{}",sendObjectCode,objectOrgCode);
    }
}
