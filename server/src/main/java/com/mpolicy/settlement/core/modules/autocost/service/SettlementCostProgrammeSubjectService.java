package com.mpolicy.settlement.core.modules.autocost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostProgrammeSubjectEntity;

import java.util.List;

/**
 * 支出结算方案科目关系表
 *
 * <AUTHOR>
 * @since 2023-10-31 20:03:47
 */
public interface SettlementCostProgrammeSubjectService extends IService<SettlementCostProgrammeSubjectEntity> {

    void opSubjectSwitch(List<Integer> subjectIds, Integer opSwitch);
}

