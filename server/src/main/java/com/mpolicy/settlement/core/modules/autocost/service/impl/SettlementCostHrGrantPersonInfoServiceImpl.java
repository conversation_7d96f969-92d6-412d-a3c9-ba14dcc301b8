package com.mpolicy.settlement.core.modules.autocost.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.autocost.dao.SettlementCostHrGrantPersonInfoDao;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostHrGrantPersonInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostHrGrantPersonInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.mpolicy.settlement.core.common.Constant.MAX_BATCH_INSERT_NUM;

/**
 * 结算Hr发放人员表服务实现
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Service("settlementCostHrGrantPersonInfoService")
public class SettlementCostHrGrantPersonInfoServiceImpl extends ServiceImpl<SettlementCostHrGrantPersonInfoDao, SettlementCostHrGrantPersonInfoEntity> implements SettlementCostHrGrantPersonInfoService {

    @Override
    public int saveList(List<SettlementCostHrGrantPersonInfoEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return batchSave(list);
    }

    @Override
    public SettlementCostHrGrantPersonInfoEntity getBySendObjectCodeAndCycle(String sendObjectCode, String costSettlementCycle) {
        if (StringUtils.isBlank(sendObjectCode) || StringUtils.isBlank(costSettlementCycle)) {
            return null;
        }

        LambdaQueryWrapper<SettlementCostHrGrantPersonInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SettlementCostHrGrantPersonInfoEntity::getSendObjectCode, sendObjectCode);
        queryWrapper.eq(SettlementCostHrGrantPersonInfoEntity::getCostSettlementCycle, costSettlementCycle);
        queryWrapper.eq(SettlementCostHrGrantPersonInfoEntity::getDeleted, 0);

        return this.getOne(queryWrapper);
    }

    @Override
    public List<SettlementCostHrGrantPersonInfoEntity> listBySendObjectCode(String sendObjectCode) {
        if (StringUtils.isBlank(sendObjectCode)) {
            return null;
        }

        LambdaQueryWrapper<SettlementCostHrGrantPersonInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SettlementCostHrGrantPersonInfoEntity::getSendObjectCode, sendObjectCode);
        queryWrapper.eq(SettlementCostHrGrantPersonInfoEntity::getDeleted, 0);

        return this.list(queryWrapper);
    }

    @Override
    public boolean updateHrGrantFlag(String sendObjectCode, String costSettlementCycle, Integer hrGrantFlag) {
        if (StringUtils.isBlank(sendObjectCode) || StringUtils.isBlank(costSettlementCycle) || hrGrantFlag == null) {
            return false;
        }

        LambdaUpdateWrapper<SettlementCostHrGrantPersonInfoEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SettlementCostHrGrantPersonInfoEntity::getSendObjectCode, sendObjectCode);
        updateWrapper.eq(SettlementCostHrGrantPersonInfoEntity::getCostSettlementCycle, costSettlementCycle);
        updateWrapper.eq(SettlementCostHrGrantPersonInfoEntity::getDeleted, 0);
        updateWrapper.set(SettlementCostHrGrantPersonInfoEntity::getHrGrantFlag, hrGrantFlag);

        return this.update(updateWrapper);
    }

    @Override
    public List<SettlementCostHrGrantPersonInfoEntity> listByCostSettlementCycle(String costSettlementCycle) {
        if (StringUtils.isBlank(costSettlementCycle)) {
            return null;
        }

        LambdaQueryWrapper<SettlementCostHrGrantPersonInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SettlementCostHrGrantPersonInfoEntity::getCostSettlementCycle, costSettlementCycle);
        queryWrapper.eq(SettlementCostHrGrantPersonInfoEntity::getDeleted, 0);

        return this.list(queryWrapper);
    }

    @Override
    public int batchUpdateList(List<SettlementCostHrGrantPersonInfoEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return baseMapper.batchUpdateHrGrantPersonInfo(list);
    }

    @Override
    public int deletePhysicalByCostSettlementCycle(String costSettlementCycle) {
        if (StringUtils.isBlank(costSettlementCycle)) {
            return 0;
        }
        return baseMapper.deletePhysicalByCostSettlementCycle(costSettlementCycle);
    }

    /**
     * 批量保存
     *
     * @param list 数据列表
     * @return 保存成功的记录数
     */
    private int batchSave(List<SettlementCostHrGrantPersonInfoEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        
        int result = 0;
        // 批量写入
        if (list.size() > MAX_BATCH_INSERT_NUM) {
            List<List<SettlementCostHrGrantPersonInfoEntity>> partition = ListUtils.partition(list, MAX_BATCH_INSERT_NUM);
            for (List<SettlementCostHrGrantPersonInfoEntity> x : partition) {
                result = result + baseMapper.insertBatchSomeColumn(x);
            }
        } else {
            result = baseMapper.insertBatchSomeColumn(list);
        }
        return result;
    }
}
