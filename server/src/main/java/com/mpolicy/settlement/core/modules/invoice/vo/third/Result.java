package com.mpolicy.settlement.core.modules.invoice.vo.third;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/9/6 16:17
 * @Version 1.0
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class Result<T> implements Serializable {

    private static final long serialVersionUID = 1331134667810352183L;
    private boolean success;
    private String code;
    /**
     * 错误描述，使用易理解的文字表示错误的原因
     */
    private String msg;
    /**
     * 具体错误原因
     */
    private String issue;
    private T data;

    private static final String success_code = "0";

    private static final String fail_code = "500";

    public static <T> Result<T> ok() {
        return result(success_code, null, null, null);
    }

    public static <T> Result<T> error(String msg) {
        return result(fail_code, msg, msg, null);
    }

    public static <T> Result<T> result(String code, String msg, String issue, T data) {
        Result<T> result = new Result<>();
        return result.setCode(code)
                .setSuccess("0".equals(code))
                .setMsg(msg)
                .setIssue(issue)
                .setData(data);
    }


}
