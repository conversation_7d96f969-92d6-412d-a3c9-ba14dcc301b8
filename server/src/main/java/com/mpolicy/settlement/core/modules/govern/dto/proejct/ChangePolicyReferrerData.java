package com.mpolicy.settlement.core.modules.govern.dto.proejct;

import com.mpolicy.settlement.core.modules.govern.dto.base.PolicyDataReviseBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 【业务10】保单推荐人变更
 *
 * <AUTHOR>
 * @date 2024-01-25 14:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "保单推荐人变更数据对象", description = "保单推荐人变更数据对象")
public class ChangePolicyReferrerData extends PolicyDataReviseBase implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 渠道推荐人编码
     */
    @ApiModelProperty(value = "源渠道推荐人信息")
    EpChannelManagerVo sourceReferrer;

    /**
     * 渠道推荐人编码
     */
    @ApiModelProperty(value = "新渠道推荐人编码", example = "R20220120154038eFuEIz")
    private String newReferrerCode;

    /**
     * 渠道推荐人工号
     */
    @ApiModelProperty(value = "新渠道推荐人工号", required = true, example = "ZHNX0202")
    @NotBlank(message = "渠道推荐人工号为空")
    private String newReferrerWno;

    /**
     * 新渠道推荐人信息
     */
    @ApiModelProperty(value = "新渠道推荐人信息")
    private EpChannelManagerVo newReferrer;

    @ApiModelProperty(value = "渠道")
    private String channelCode;
}
