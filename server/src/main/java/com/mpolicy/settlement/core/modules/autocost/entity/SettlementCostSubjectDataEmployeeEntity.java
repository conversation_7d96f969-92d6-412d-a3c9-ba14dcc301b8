package com.mpolicy.settlement.core.modules.autocost.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 科目范围(员工)数据
 *
 * <AUTHOR>
 * @since 2023-11-06 15:19:48
 */
@TableName("settlement_cost_subject_data_employee")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostSubjectDataEmployeeEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private Integer id;
    /**
     * 所属周期
     */
    private String costSettlementCycle;

    /**
     * 批次号
     */
    private String batchCode;

    /**
     * 科目编码
     */
    private String subjectCode;

    /**
     * 员工id
     */
    private Integer employeeId;
    /**
     * 员工编号
     */
    private String employeeCode;
    /**
     * 员工类型
     */
    private Integer employeeType;
    /**
     * 员工名称
     */
    private String employeeName;
    /**
     * 岗位id
     */
    private String jobId;
    /**
     * 岗位名称
     */
    private String jobName;

    /**
     * 职务类型：0—主职/1—兼职
     */
    private Integer serviceType;

    /**
     * 组织性质
     */
    private Integer orgNature;

    /**
     * 任职状态：0-任职中/1-任职结束/2-草稿/3待生效/4作废/5审批中
     */
    private Integer postingStatus;

    /**
     * 分支编码
     */
    private String orgCode;
    /**
     * 分支名称
     */
    private String orgName;
    /**
     * 片区编码
     */
    private String zoneCode;
    /**
     * 片区名称
     */
    private String zoneName;
    /**
     * 区域编码
     */
    private String regionCode;
    /**
     * 区域名称
     */
    private String regionName;
    /**
     * 责任督导工号
     */
    private String supervisorCode;
    /**
     * 直线上级工号
     */
    private String managerCode;
    /**
     * pco等级
     */
    private String pcoLevel;
    /**
     * 人均单量（用excel导入pco信息）
     */
    private BigDecimal perCapitaCount;
    /**
     * 主任等级
     */
    private String directorLevel;
    /**
     * 继续率
     */
    private BigDecimal renewalRate;
    /**
     * 短险推广费
     */
    private BigDecimal shortPromotion;
    /**
     * 长险推广费
     */
    private BigDecimal longPromotion;
    /**
     * 短险保费
     */
    private BigDecimal shortProductPremium;
    /**
     * 长险保费
     */
    private BigDecimal longProductPremium;
    /**
     * 短期险折标保费
     */
    private BigDecimal shortAssessConvertInsurancePremium;


    /**
     * 长期险折标保费
     */
    private BigDecimal longAssessConvertInsurancePremium;
    /**
     * 注册客户数
     */
    private Integer registerUserCount;
    /**
     * 人均注册客户数
     */
    private BigDecimal avgRegisterUserCount;


    /**
     * 生服业务相关知识（是否合格）
     */
    private String businessKnowledgeQualifyFlag;
    /**
     * pco 上月审核通过培训次数
     */
    private Integer smPcoTraining ;
    /**
     * 酒水/家清破损理赔提交及时性（是否合格）
     */
    private String claimTimelinessQualifyFlag;
    /**
     * 上月理赔多次驳回率
     */
    private BigDecimal smRejectManyRate;
    /**
     * 生服库存盘点率
     */
    private BigDecimal smCheckSignTaskRadio;
    /**
     * 酒水个人仓建仓率
     */
    private BigDecimal personalWarehouseCreateRadio;
    /**
     * 当月酒水个人仓使用率
     */
    private BigDecimal smPersonalWarehouseUseRadio;

    /**
     * 当月酒水营收
     */
    private BigDecimal smWineRevenueAmt;
    /**
     * 是否删除;0有效-1删除
     */
    @TableLogic
    private Integer deleted;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @Version
    @TableField(fill = FieldFill.INSERT)
    private Integer revision;
}
