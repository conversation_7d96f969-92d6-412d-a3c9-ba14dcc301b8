package com.mpolicy.settlement.core.modules.protocol.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.protocol.dao.OrgInfoDao;
import com.mpolicy.settlement.core.modules.protocol.entity.OrgInfoEntity;
import com.mpolicy.settlement.core.modules.protocol.service.OrgInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 保司协议产品险种费率表
 *
 * <AUTHOR>
 * @date 2023-05-20 20:31:56
 */
@Slf4j
@Service("orgInfoService")
public class OrgInfoServiceImpl extends ServiceImpl<OrgInfoDao, OrgInfoEntity> implements OrgInfoService {

}
