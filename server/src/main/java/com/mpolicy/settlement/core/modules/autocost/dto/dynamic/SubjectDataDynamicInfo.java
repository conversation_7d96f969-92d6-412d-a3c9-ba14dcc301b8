package com.mpolicy.settlement.core.modules.autocost.dto.dynamic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 动态科目名单信息
 *
 * <AUTHOR>
 * @since 2023-11-27 21:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "动态科目名单信息", description = "动态科目名单信息")
public class SubjectDataDynamicInfo extends SubjectDataDynamicBase implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号", example = "1")
    private Integer serialNumber;

    /**
     * 动态科目编号
     */
    @ApiModelProperty(value = "动态科目编号", example = "DTKM0001")
    private String dynamicSubjectCode;

    /**
     * 动态科目名称
     */
    @ApiModelProperty(value = "动态科目名称", example = "开门红")
    private String dynamicSubjectName;

    /**
     * 动态科目总金额
     */
    @ApiModelProperty(value = "动态科目总金额", example = "123.12")
    private BigDecimal dynamicSubjectCash;

    /**
     * 动态科目备注
     */
    @ApiModelProperty(value = "动态科目备注", example = "备注信息")
    private String dynamicSubjectDesc;

    /**
     * 结算机构编码
     */
    @ApiModelProperty(value = "结算机构编码", example = "A000001")
    private String settlementInstitution;

    /**
     * 结算机构名称
     */
    @ApiModelProperty(value = "结算机构名称", example = "小鲸向海")
    private String settlementInstitutionName;

    /**
     * 员工编码
     */
    @ApiModelProperty(value = "员工编码", example = "ZHNX12345")
    private String employeeCode;

    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称", example = "张三")
    private String employeeName;

    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码", example = "HNQY")
    private String regionCode;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称", example = "湖南区域")
    private String regionName;

    /**
     * 分支机构编码
     */
    @ApiModelProperty(value = "分支机构编码", example = "HNCS")
    private String orgCode;

    /**
     * 分支机构名称
     */
    @ApiModelProperty(value = "分支机构名称", example = "湖南长沙")
    private String orgName;
}