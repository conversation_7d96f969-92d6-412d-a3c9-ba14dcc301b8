package com.mpolicy.settlement.core.modules.protocol.dao;

import com.mpolicy.settlement.core.modules.protocol.dto.ProtocolInsuranceProductInfoOut;
import com.mpolicy.settlement.core.modules.protocol.entity.ProtocolInsuranceProductProductEntity;
import com.mpolicy.service.common.mapper.ImsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 保司协议险种名称和小鲸险种对应关系
 *
 * <AUTHOR>
 * @date 2023-05-20 20:31:56
 */
public interface ProtocolInsuranceProductProductDao extends ImsBaseMapper<ProtocolInsuranceProductProductEntity> {
    /**
     * 获取险种对应的合约.协议产品信息
     *
     * @param productCodeList 险种集合
     * @param reconcileType   类型
     * @return
     */
    List<ProtocolInsuranceProductInfoOut> findProtocolInsuranceProductList(@Param("productCodeList") List<String> productCodeList,
                                                                           @Param("reconcileType") Integer reconcileType);

    /**
     * 获取险种对应的合约.协议产品信息
     *
     * @param productCode   险种编码
     * @param reconcileType 类型
     * @return
     */
    ProtocolInsuranceProductInfoOut findProtocolInsuranceProductInfo(@Param("productCode") String productCode,
                                                                     @Param("reconcileType") Integer reconcileType);
}
