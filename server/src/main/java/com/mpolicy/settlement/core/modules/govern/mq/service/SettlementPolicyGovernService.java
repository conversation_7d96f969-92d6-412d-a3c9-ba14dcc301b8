package com.mpolicy.settlement.core.modules.govern.mq.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.settlement.core.modules.govern.dto.proejct.*;
import com.mpolicy.settlement.core.service.common.PolicyCenterBaseClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 结算服务-保单中心业务刷数事件处理
 *
 * <AUTHOR>
 * @since 2024/02/05
 */
@Service
@Slf4j(topic = "reconcileMq")
public class SettlementPolicyGovernService {

    @Autowired
    private PolicyCenterBaseClient policyCenterBaseClient;

    /**
     * 申请业务编号回执完成
     *
     * @param governApplyCode 申请业务编号
     * <AUTHOR>
     * @since 2024/2/5
     */
    private void callBackGovern(String governApplyCode){
        log.info("【业务1】保单号变更 ,申请业务编号={} 回执完成", governApplyCode);
        // 曙尘
        // policyCenterBaseClient.governApplyConfirm(governApplyCode, "settlement_server", "收入端");
        // 张健
        // policyCenterBaseClient.governApplyConfirm(governApplyCode, "settlement_server", "支出端");
    }

    /**
     * 【业务1】保单号变更
     *
     * @param governApplyCode 业务刷数凭证号
     * @param data            data
     * <AUTHOR>
     * @since 2024/2/5
     */
    public void changePolicyCode(String governApplyCode, JSONObject data) {
        // 业务刷数报文
        ChangePolicyCodeData dataRevise = JSON.parseObject(data.toJSONString(), ChangePolicyCodeData.class);

        // TODO: 2024/2/5 刘曙尘 + 张健


        // 回执确认
        callBackGovern(governApplyCode);
        log.info("【业务1】保单号变更 ,申请业务编号={} OA工单号={} 处理完成={}", governApplyCode, dataRevise.getOaCode(), JSON.toJSONString(dataRevise));
    }

    /**
     * 【业务2】批单号变更
     *
     * @param governApplyCode 业务刷数凭证号
     * @param data            data
     * <AUTHOR>
     * @since 2024/2/5
     */
    public void changeEndorsementNo(String governApplyCode, JSONObject data) {
        ChangeEndorsementNoData dataRevise = JSON.parseObject(data.toJSONString(), ChangeEndorsementNoData.class);

        // TODO: 2024/2/5 刘曙尘 + 张健

        // 回执确认
        callBackGovern(governApplyCode);
        log.info("【业务2】批单号变更,申请业务编号={} OA工单号={} 处理完成={}", governApplyCode, dataRevise.getOaCode(), JSON.toJSONString(dataRevise));
    }

    /**
     * 【业务3】删除保单
     *
     * @param governApplyCode 业务刷数凭证号
     * @param data            data
     * <AUTHOR>
     * @since 2024/2/5
     */
    public void removePolicy(String governApplyCode, JSONObject data) {
        RemovePolicyData dataRevise = JSON.parseObject(data.toJSONString(), RemovePolicyData.class);

        // TODO: 2024/2/5 刘曙尘 + 张健

        // 回执确认
        callBackGovern(governApplyCode);
        log.info("【业务3】删除保单,申请业务编号={} OA工单号={} 处理完成={}", governApplyCode, dataRevise.getOaCode(), JSON.toJSONString(dataRevise));
    }

    /**
     * 【业务4】删除批单
     *
     * @param governApplyCode 业务刷数凭证号
     * @param data            data
     * <AUTHOR>
     * @since 2024/2/5
     */
    public void removeEndorsementNo(String governApplyCode, JSONObject data) {
        RemoveEndorsementData dataRevise = JSON.parseObject(data.toJSONString(), RemoveEndorsementData.class);

        // TODO: 2024/2/5 刘曙尘 + 张健


        // 回执确认
        callBackGovern(governApplyCode);
        log.info("【业务4】删除批单,申请业务编号={} OA工单号={} 处理完成={}", governApplyCode, dataRevise.getOaCode(), JSON.toJSONString(dataRevise));
    }

    /**
     * 【业务5】保单保费变更
     *
     * @param governApplyCode 业务刷数凭证号
     * @param data            data
     * <AUTHOR>
     * @since 2024/2/5
     */
    public void changePolicyPremium(String governApplyCode, JSONObject data) {
        ChangePolicyPremiumData dataRevise = JSON.parseObject(data.toJSONString(), ChangePolicyPremiumData.class);

        // TODO: 2024/2/5 刘曙尘 + 张健


        // 回执确认
        callBackGovern(governApplyCode);
        log.info("【业务5】保单保费变更,申请业务编号={} OA工单号={} 处理完成={}", governApplyCode, dataRevise.getOaCode(), JSON.toJSONString(dataRevise));
    }

    /**
     * 【业务6】批改保费变更新
     *
     * @param governApplyCode 业务刷数凭证号
     * @param data            data
     * <AUTHOR>
     * @since 2024/2/5
     */
    public void changeEndorsementPremium(String governApplyCode, JSONObject data) {
        ChangeEndorsementPremiumData dataRevise = JSON.parseObject(data.toJSONString(), ChangeEndorsementPremiumData.class);

        // TODO: 2024/2/5 刘曙尘 + 张健


        // 回执确认
        callBackGovern(governApplyCode);
        log.info("【业务6】批改保费变更新,申请业务编号={} OA工单号={} 处理完成={}", governApplyCode, dataRevise.getOaCode(), JSON.toJSONString(dataRevise));
    }

    /**
     * 【业务7】续期实收时间变更
     *
     * @param governApplyCode 业务刷数凭证号
     * @param data            data
     * <AUTHOR>
     * @since 2024/2/5
     */
    public void changeRenewalRealityTime(String governApplyCode, JSONObject data) {
        ChangeRenewalRealityTimeData dataRevise = JSON.parseObject(data.toJSONString(), ChangeRenewalRealityTimeData.class);

        // TODO: 2024/2/5 刘曙尘 + 张健


        // 回执确认
        callBackGovern(governApplyCode);
        log.info("【业务7】续期实收时间变更,申请业务编号={} OA工单号={} 处理完成={}", governApplyCode, dataRevise.getOaCode(), JSON.toJSONString(dataRevise));
    }

    /**
     * 【业务8】续期保费变变更
     *
     * @param governApplyCode 业务刷数凭证号
     * @param data            data
     * <AUTHOR>
     * @since 2024/2/5
     */
    public void changeRenewalRealityPremium(String governApplyCode, JSONObject data) {
        ChangeRenewalRealityPremiumData dataRevise = JSON.parseObject(data.toJSONString(), ChangeRenewalRealityPremiumData.class);

        // TODO: 2024/2/5 刘曙尘 + 张健

        callBackGovern(governApplyCode);
        log.info("【业务8】续期保费变变更,申请业务编号={} OA工单号={} 处理完成={}", governApplyCode, dataRevise.getOaCode(), JSON.toJSONString(dataRevise));
    }

    /**
     * 【业务9】续期回退
     *
     * @param governApplyCode 业务刷数凭证号
     * @param data            data
     * <AUTHOR>
     * @since 2024/2/5
     */
    public void changeRenewalFallback(String governApplyCode, JSONObject data) {
        ChangeRenewalFallbackData dataRevise = JSON.parseObject(data.toJSONString(), ChangeRenewalFallbackData.class);

        // TODO: 2024/2/5 刘曙尘 + 张健

        callBackGovern(governApplyCode);
        log.info("【业务9】续期回退,申请业务编号={} OA工单号={} 处理完成={}", governApplyCode, dataRevise.getOaCode(), JSON.toJSONString(dataRevise));
    }

    /**
     * 【业务10】保单推荐人变更
     *
     * @param governApplyCode 业务刷数凭证号
     * @param data            data
     * <AUTHOR>
     * @since 2024/2/5
     */
    public void changePolicyReferrer(String governApplyCode, JSONObject data) {
        ChangePolicyReferrerData dataRevise = JSON.parseObject(data.toJSONString(), ChangePolicyReferrerData.class);

        // TODO: 2024/2/5 刘曙尘 + 张健

        callBackGovern(governApplyCode);
        log.info("【业务10】保单推荐人变更,申请业务编号={} OA工单号={} 处理完成={}", governApplyCode, dataRevise.getOaCode(), JSON.toJSONString(dataRevise));
    }

    /**
     * 【业务11】保单代理人变更
     *
     * @param governApplyCode 业务刷数凭证号
     * @param data            data
     * <AUTHOR>
     * @since 2024/2/5
     */
    public void changePolicyAgent(String governApplyCode, JSONObject data) {
        ChangePolicyAgentData dataRevise = JSON.parseObject(data.toJSONString(), ChangePolicyAgentData.class);

        // TODO: 2024/2/5 刘曙尘 + 张健


        // 回执确认
        callBackGovern(governApplyCode);
        log.info("【业务11】保单代理人变更,申请业务编号={} OA工单号={} 处理完成={}", governApplyCode, dataRevise.getOaCode(), JSON.toJSONString(dataRevise));
    }
}