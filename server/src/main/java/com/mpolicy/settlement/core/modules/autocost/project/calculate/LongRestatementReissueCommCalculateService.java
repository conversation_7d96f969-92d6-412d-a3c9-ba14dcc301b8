package com.mpolicy.settlement.core.modules.autocost.project.calculate;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.abs.AbsSubjectCalculate;
import com.mpolicy.settlement.core.modules.autocost.dto.CostAutoRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.SettlementCostInfoDto;
import com.mpolicy.settlement.core.modules.autocost.dto.calculate.CostSubjectCalculateRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataDynamic;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataPolicyComm;
import com.mpolicy.settlement.core.modules.autocost.dto.employee.EmployeeInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.EmployeeQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.enums.AutoCostAmountTypeEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.CostDataTypeEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.DynamicSubjectDataRuleEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.EmployeeRoleEnum;
import com.mpolicy.settlement.core.modules.autocost.project.calculate.common.DynamicBaseCalculateService;
import com.mpolicy.settlement.core.modules.autocost.project.calculate.common.PolicyProductCalculateService;
import com.mpolicy.settlement.core.utils.LambdaUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/7 4:30 下午
 * @Version 1.0
 */
@Service
@Slf4j
public class LongRestatementReissueCommCalculateService extends DynamicBaseCalculateService<SubjectDataDynamic> {


    @Override
    public CostSubjectEnum getSubjectDataEnum() {
        return CostSubjectEnum.LONG_RESTATEMENT_REISSUE_COMM;
    }


    @Override
    protected Map<String, List<CostAutoRecord>> initCostAutoRecordBySource(String documentCode, CostSubjectCalculateRecord record, SubjectDataDynamic sourceData) {
        if(Objects.isNull(sourceData)){
            return null;
        }
        List<CostAutoRecord> costAutoRecords = builderCostAutoRecordList(documentCode,record,sourceData);
        Map<String,List<CostAutoRecord>> costMap = Maps.newHashMapWithExpectedSize(1);
        costMap.put(getSubjectDataEnum().getCode(),costAutoRecords);
        return costMap;
    }

    @Override
    protected Map<String, SubjectDataDynamic> getShareDetail(SubjectDataDynamic sourceData) {
        Map<String,SubjectDataDynamic> map = Maps.newHashMapWithExpectedSize(1);
        map.put(sourceData.getDynamicSubjectCode(),sourceData);
        return map;
    }

    @Override
    protected Map<String, Object> createScriptParams(CostAutoRecord costAutoRecord) {
        return Collections.EMPTY_MAP;
    }

    @Override
    protected void checkCostAutoRecord(DynamicSubjectDataRuleEnum ruleEnum, CostAutoRecord costAutoRecord) {

    }

    @Override
    protected void calcCostAutoRecordTotal(DynamicSubjectDataRuleEnum ruleEnum, CostAutoRecord costAutoRecord) {

    }
}
