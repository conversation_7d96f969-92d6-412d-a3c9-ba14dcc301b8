package com.mpolicy.settlement.core.modules.autocost.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.autocost.dao.SettlementCostCalcFactorDao;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostCalcFactorEntity;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostCalcFactorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 自动结算计算因子表
 *
 * <AUTHOR>
 * @since 2023-11-04 17:34:05
 */
@Slf4j
@Service
public class SettlementCostCalcFactorServiceImpl extends ServiceImpl<SettlementCostCalcFactorDao, SettlementCostCalcFactorEntity> implements SettlementCostCalcFactorService {

}
