package com.mpolicy.settlement.core.modules.autocost.dao;

import com.mpolicy.service.common.mapper.ImsBaseMapper;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostSubjectDataDynamicApportionEntity;

/**
 * 科目范围(动态科目)分摊明细
 * 
 * <AUTHOR>
 * @since 2023-11-27 20:42:05
 */
public interface SettlementCostSubjectDataDynamicApportionDao extends ImsBaseMapper<SettlementCostSubjectDataDynamicApportionEntity> {
	
}
