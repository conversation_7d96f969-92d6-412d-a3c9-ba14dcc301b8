package com.mpolicy.settlement.core.modules.autocost.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

public enum SubjectTransStatusEnum {

    NORMAL("normal","正常"),
    CORRECT("correct","冲正");
    @Getter
    private final String code;

    @Getter
    private final String name;



    SubjectTransStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取科目计算交易流水状态
     *
     * @param code 类型编码
     * <AUTHOR>
     * @since 2021/11/02
     */
    public static SubjectTransStatusEnum deCode(String code) {
        return Arrays.stream(SubjectTransStatusEnum.values()).filter(x -> x.code.equals(code)).findFirst().orElse(null);
    }
}
