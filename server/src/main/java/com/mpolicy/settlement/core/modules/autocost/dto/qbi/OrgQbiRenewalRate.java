package com.mpolicy.settlement.core.modules.autocost.dto.qbi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 分支指标基本信息
 *
 * <AUTHOR>
 * @since  2023-11-05 19:50
 */
@Data
@ApiModel(value = "分支指标基本信息", description = "分支指标基本信息")
public class OrgQbiRenewalRate implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 分支编号
     */
    @ApiModelProperty(value = "分支编号", example = "ZHNX202020")
    private String orgCode;

    /**
     * 分支名称
     */
    @ApiModelProperty(value = "分支名称", example = "ZHNX202020")
    private String orgName;


    /**
     * 结算月份
     */
    @ApiModelProperty(value = "结算月份")
    private String bizMonth;

    /**
     * 续期率
     */
    @ApiModelProperty(value = "R13续期率", example = "0.8")
    private BigDecimal renewalRate;

    /**
     * 上月月末续保率
     */
    @ApiModelProperty(value = "上月月末续保率", example = "0.85")
    private BigDecimal smRenewalRate;

    /**
     * 应缴金额
     */
    private BigDecimal payAmt;
    /**
     * 实缴金额
     */
    private BigDecimal actPayAmt;

    /**
     * 快照分区
     */
    @ApiModelProperty(value = "快照分区", example = "20231212")
    private Integer pt;
}
