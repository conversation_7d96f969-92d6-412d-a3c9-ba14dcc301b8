package com.mpolicy.settlement.core.modules.reconcile.event.project.policy;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.settlement.core.enums.SettlementExceptionEnum;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.PolicyPreservationDetailDto;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostCorrectionDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.project.common.AbsPolicyCommonEvent;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventCheckResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/26 11:28 下午
 * @Version 1.0
 */
@Service
@Slf4j
public class CustomerManagerChangeEvent  extends AbsPolicyCommonEvent {
    @Override
    public SettlementEventTypeEnum handlerEventType() {
        return SettlementEventTypeEnum.CUSTOMER_MANAGER_CHANGE;
    }
    @Override
    public HandleEventCheckResult handleIncomeEventCheck(SettlementEventJobEntity eventJob,Integer reconcileType) {
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleIncomeEvent(SettlementEventJobEntity eventJob, HandleEventData eventData) {

        return "团险整单层推荐人变更业务不处理-success";
    }

    @Override
    public HandleEventCheckResult handleCostEventCheck(SettlementEventJobEntity eventJob) {
        JSONObject eventData = JSONObject.parseObject(eventJob.getEventRequest());
        //初始推荐人变更是内部事件，与保司无关，所以不需要根据保司保全号判断记录是否存在
        // 1 根据保全操作唯一编号判断是否存在支出明细记录
        Integer count = settlementCostInfoService.lambdaQuery()
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                .eq(SettlementCostInfoEntity::getPreservationCode, eventData.getString("preservationCode"))
                .count();
        // 如果存在纪录
        if (count > 0) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg(StrUtil.format("支出端-该初始推荐人变更保全已经支出结算明细数据，保单号={} 保全申请单号={}", eventJob.getEventBusinessCode(), eventJob.getEventBusinessCode())).build();
        }
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleCostEvent(SettlementEventJobEntity eventJob, HandleEventData handleEventData) {
        EpContractInfoVo policyInfo = handleEventData.getPolicyInfo();
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        PolicyPreservationDetailDto preservationDetailDto = builderPolicyPreservationDetail(JSONObject.parseObject(eventJob.getEventRequest()));
        if(Objects.equals(policyProductTypeEnum,PolicyProductTypeEnum.GROUP)){
            if(preservationDetailDto.getAfterCustomerManager().getChangeSubPolicy() == 0) {
                log.info("支出端-整单层初始推荐人变更-团险整单层推荐人变更业务不处理--保单号={},保全编号={}", eventJob.getEventBusinessCode(), preservationDetailDto.getPreservationCode(), preservationDetailDto.getRenewalTermPeriod());
                return "团险整单层推荐人变更业务不处理-success";
            }
        }

        getCustomerManagerChangeDetail(preservationDetailDto);
        //判断事件的创建时间是否早于支出端新契约的计算完成时间，早于就直接pass不计算了，因为计算新契约时候获取到的是变更后的管护经理（保单信息存的是最新信息不是快照）
        settlementCostInfoService.lambdaQuery()
                .eq(SettlementCostInfoEntity::getCorrectionFlag,0)
                .eq(SettlementCostInfoEntity::getContractCode,eventJob.getContractCode()).list();
                //.eq(SettlementCostInfoEntity::getSettlementSubjectCode,)


        //1、查询保单基础信息，保单信息不存在，直接不处理该记录
        //存在场景：新契约没有推荐人信息导致处理失败，事件记录处理失败，而推送了一笔推荐人变更事件过来，该事件直接不处理，因为新契约事件补偿时会获取最新的推荐人信息，
        SettlementCostPolicyInfoEntity costPolicy = settlementCostPolicyInfoService.getCostPolicyInfoEntityByContractCode(preservationDetailDto.getContractCode());
        if(Objects.isNull(costPolicy)) {
            return "没有要变更推荐人的佣金记录-success";
        }

        //2、参数验证，验证不通过抛出异常
        settlementCostProcessService.validParam(eventJob,policyInfo, () -> {
            if(Objects.isNull(preservationDetailDto.getAfterCustomerManager())){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("保单号={}, 保全编号={},整单层初始推荐人变更,变更后的推荐人信息为空", eventJob.getEventBusinessCode(),preservationDetailDto.getPreservationCode())));
            }
        });
        try {
            CostCorrectionDto costCorrectionDto = null;
            //初始推荐人变更同步变更分单记录时走分单推荐人冲正逻辑
            if(preservationDetailDto.getAfterCustomerManager().getChangeSubPolicy()!=null &&
                preservationDetailDto.getAfterCustomerManager().getChangeSubPolicy() == 1){
                costCorrectionDto = settlementCostCorrectionService.builderSplitCustomerManagerChangeCostInfo(eventJob,handlerEventType(),costPolicy,preservationDetailDto);
            }else {
                costCorrectionDto = settlementCostCorrectionService.builderCustomerManagerChangeCostInfo(eventJob, handlerEventType(), costPolicy, preservationDetailDto);

            }
            if (costCorrectionDto == null) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("保单号={}, 保全编号={},该保单没有佣金记录", eventJob.getEventBusinessCode(), preservationDetailDto.getPreservationCode())));
            }
            //处理农保员工在职状态
            settlementCostOwnerService.handlerOwnerThirdStatus(costPolicy,costCorrectionDto.getNewCostList());
            settlementCostCorrectionService.saveCostCommissionRecord(eventJob, costCorrectionDto);
        }catch (GlobalException e){
            log.warn("初始推荐人变更异常信息,{}/{}",e.getCode(),e.getMsg());
            if(Objects.equals(e.getCode(), SettlementExceptionEnum.CORRECTION_AFTER_CUSTOMER_MANAGER_EXIST.getErrorCode())){
                return "变更后的推荐人记录已存在-success";
            }else{
                throw e;
            }
        }
        return "success";
    }




}
