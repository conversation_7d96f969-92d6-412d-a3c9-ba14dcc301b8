package com.mpolicy.settlement.core.modules.autocost.controller;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mpolicy.common.result.Result;
import com.mpolicy.settlement.core.modules.autocost.dto.hr.HrGrantPersonInfoDto;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostHrGrantPersonInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.service.HrGrantPersonSyncService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostHrGrantPersonInfoService;
import com.mpolicy.web.common.annotation.PassToken;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 结算Hr发放人员信息管理
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@RestController
@RequestMapping("/settlement/cost/hr-grant-person")
@Api(tags = "结算Hr发放人员信息管理")
@Slf4j
public class SettlementCostHrGrantPersonInfoController {

    @Autowired
    private SettlementCostHrGrantPersonInfoService hrGrantPersonInfoService;

    @Autowired
    private HrGrantPersonSyncService hrGrantPersonSyncService;

    @ApiOperation(value = "根据工号和结算周期查询Hr发放人员信息", notes = "根据工号和结算周期查询Hr发放人员信息")
    @GetMapping("/getByCodeAndCycle")
    @PassToken
    public Result<HrGrantPersonInfoDto> getByCodeAndCycle(
            @ApiParam(value = "工号", required = true) @RequestParam String sendObjectCode,
            @ApiParam(value = "结算周期", required = true) @RequestParam String costSettlementCycle) {
        
        if (StringUtils.isBlank(sendObjectCode) || StringUtils.isBlank(costSettlementCycle)) {
            return Result.error("工号和结算周期不能为空");
        }
        
        SettlementCostHrGrantPersonInfoEntity entity = hrGrantPersonInfoService.getBySendObjectCodeAndCycle(sendObjectCode, costSettlementCycle);
        if (entity == null) {
            return Result.success(null);
        }
        
        HrGrantPersonInfoDto dto = new HrGrantPersonInfoDto();
        BeanUtils.copyProperties(entity, dto);
        
        return Result.success(dto);
    }

    @ApiOperation(value = "根据工号查询Hr发放人员信息列表", notes = "根据工号查询Hr发放人员信息列表")
    @GetMapping("/listByCode")
    @PassToken
    public Result<List<HrGrantPersonInfoDto>> listByCode(
            @ApiParam(value = "工号", required = true) @RequestParam String sendObjectCode) {
        
        if (StringUtils.isBlank(sendObjectCode)) {
            return Result.error("工号不能为空");
        }
        
        List<SettlementCostHrGrantPersonInfoEntity> entities = hrGrantPersonInfoService.listBySendObjectCode(sendObjectCode);
        List<HrGrantPersonInfoDto> dtos = convertToDto(entities);
        
        return Result.success(dtos);
    }

    @ApiOperation(value = "根据结算周期查询Hr发放人员信息列表", notes = "根据结算周期查询Hr发放人员信息列表")
    @GetMapping("/listByCycle")
    @PassToken
    public Result<List<HrGrantPersonInfoDto>> listByCycle(
            @ApiParam(value = "结算周期", required = true) @RequestParam String costSettlementCycle) {
        
        if (StringUtils.isBlank(costSettlementCycle)) {
            return Result.error("结算周期不能为空");
        }
        
        List<SettlementCostHrGrantPersonInfoEntity> entities = hrGrantPersonInfoService.listByCostSettlementCycle(costSettlementCycle);
        List<HrGrantPersonInfoDto> dtos = convertToDto(entities);
        
        return Result.success(dtos);
    }

    @ApiOperation(value = "更新Hr发放标志位", notes = "更新Hr发放标志位")
    @PostMapping("/updateHrGrantFlag")
    @PassToken
    public Result<Boolean> updateHrGrantFlag(
            @ApiParam(value = "工号", required = true) @RequestParam String sendObjectCode,
            @ApiParam(value = "结算周期", required = true) @RequestParam String costSettlementCycle,
            @ApiParam(value = "Hr发放标志位", required = true) @RequestParam Integer hrGrantFlag) {
        
        if (StringUtils.isBlank(sendObjectCode) || StringUtils.isBlank(costSettlementCycle) || hrGrantFlag == null) {
            return Result.error("参数不能为空");
        }
        
        if (hrGrantFlag != 0 && hrGrantFlag != 1) {
            return Result.error("Hr发放标志位只能为0或1");
        }
        
        boolean success = hrGrantPersonInfoService.updateHrGrantFlag(sendObjectCode, costSettlementCycle, hrGrantFlag);
        
        return Result.success(success);
    }

    @ApiOperation(value = "批量保存Hr发放人员信息", notes = "批量保存Hr发放人员信息")
    @PostMapping("/batchSave")
    @PassToken
    public Result<Integer> batchSave(@RequestBody @Valid List<SettlementCostHrGrantPersonInfoEntity> list) {
        
        if (CollectionUtils.isEmpty(list)) {
            return Result.error("保存数据不能为空");
        }
        
        int count = hrGrantPersonInfoService.saveList(list);
        
        return Result.success(count);
    }

    @ApiOperation(value = "手动同步Hr发放人员信息", notes = "从员工任职记录表同步符合条件的员工数据")
    @PostMapping("/syncFromHr")
    @PassToken
    public Result<Integer> syncFromHr(
            @ApiParam(value = "结算周期", required = true) @RequestParam String settlementCycle,
            @ApiParam(value = "是否强制覆盖", required = false, defaultValue = "false") @RequestParam(defaultValue = "false") boolean forceOverride) {

        if (StringUtils.isBlank(settlementCycle)) {
            return Result.error("结算周期不能为空");
        }

        try {
            int syncCount = hrGrantPersonSyncService.syncHrGrantPersonInfo(settlementCycle, forceOverride);
            return Result.success(syncCount);
        } catch (Exception e) {
            log.error("手动同步Hr发放人员信息失败", e);
            return Result.error("同步失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "清理指定周期的Hr发放人员信息", notes = "删除指定结算周期的Hr发放人员数据")
    @DeleteMapping("/cleanByCycle")
    @PassToken
    public Result<Integer> cleanByCycle(
            @ApiParam(value = "结算周期", required = true) @RequestParam String settlementCycle) {

        if (StringUtils.isBlank(settlementCycle)) {
            return Result.error("结算周期不能为空");
        }

        try {
            int deleteCount = hrGrantPersonSyncService.deleteBySettlementCycle(settlementCycle);
            return Result.success(deleteCount);
        } catch (Exception e) {
            log.error("清理Hr发放人员信息失败", e);
            return Result.error("清理失败：" + e.getMessage());
        }
    }

    /**
     * 转换Entity到DTO
     */
    private List<HrGrantPersonInfoDto> convertToDto(List<SettlementCostHrGrantPersonInfoEntity> entities) {
        List<HrGrantPersonInfoDto> dtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(entities)) {
            for (SettlementCostHrGrantPersonInfoEntity entity : entities) {
                HrGrantPersonInfoDto dto = new HrGrantPersonInfoDto();
                BeanUtils.copyProperties(entity, dto);
                dtos.add(dto);
            }
        }
        return dtos;
    }
}
