package com.mpolicy.settlement.core.modules.autocost.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mpolicy.common.result.Result;
import com.mpolicy.settlement.core.modules.autocost.dto.hr.HrGrantPersonInfoDto;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostHrGrantPersonInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.service.HrGrantPersonSyncService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostHrGrantPersonInfoService;
import com.mpolicy.web.common.annotation.PassToken;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 结算Hr发放人员信息管理
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@RestController
@RequestMapping("/settlement/cost/hr-grant-person")
@Api(tags = "结算Hr发放人员信息管理")
@Slf4j
@RequiredArgsConstructor
public class SettlementCostHrGrantPersonInfoController {

    private final SettlementCostHrGrantPersonInfoService hrGrantPersonInfoService;
    private final HrGrantPersonSyncService hrGrantPersonSyncService;

    @ApiOperation(value = "根据工号和结算周期查询Hr发放人员信息", notes = "根据工号和结算周期查询Hr发放人员信息")
    @GetMapping("/getByCodeAndCycle")
    @PassToken
    public Result<HrGrantPersonInfoDto> getByCodeAndCycle(
            @ApiParam(value = "工号", required = true) @RequestParam String sendObjectCode,
            @ApiParam(value = "结算周期", required = true) @RequestParam String costSettlementCycle) {

        // 参数校验
        if (StrUtil.hasBlank(sendObjectCode, costSettlementCycle)) {
            return Result.error("工号和结算周期不能为空");
        }

        try {
            SettlementCostHrGrantPersonInfoEntity entity = hrGrantPersonInfoService.getBySendObjectCodeAndCycle(sendObjectCode, costSettlementCycle);
            if (entity == null) {
                return Result.success(null);
            }

            HrGrantPersonInfoDto dto = convertToDto(entity);
            return Result.success(dto);

        } catch (Exception e) {
            log.error("查询Hr发放人员信息失败，工号：{}，结算周期：{}", sendObjectCode, costSettlementCycle, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "根据工号查询Hr发放人员信息列表", notes = "根据工号查询Hr发放人员信息列表")
    @GetMapping("/listByCode")
    @PassToken
    public Result<List<HrGrantPersonInfoDto>> listByCode(
            @ApiParam(value = "工号", required = true) @RequestParam String sendObjectCode) {

        if (StrUtil.isBlank(sendObjectCode)) {
            return Result.error("工号不能为空");
        }

        try {
            List<SettlementCostHrGrantPersonInfoEntity> entities = hrGrantPersonInfoService.listBySendObjectCode(sendObjectCode);
            List<HrGrantPersonInfoDto> dtos = convertToDtoList(entities);
            return Result.success(dtos);

        } catch (Exception e) {
            log.error("根据工号查询Hr发放人员信息失败，工号：{}", sendObjectCode, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "根据结算周期查询Hr发放人员信息列表", notes = "根据结算周期查询Hr发放人员信息列表")
    @GetMapping("/listByCycle")
    @PassToken
    public Result<List<HrGrantPersonInfoDto>> listByCycle(
            @ApiParam(value = "结算周期", required = true) @RequestParam String costSettlementCycle) {

        if (StrUtil.isBlank(costSettlementCycle)) {
            return Result.error("结算周期不能为空");
        }

        try {
            List<SettlementCostHrGrantPersonInfoEntity> entities = hrGrantPersonInfoService.listByCostSettlementCycle(costSettlementCycle);
            List<HrGrantPersonInfoDto> dtos = convertToDtoList(entities);
            return Result.success(dtos);

        } catch (Exception e) {
            log.error("根据结算周期查询Hr发放人员信息失败，结算周期：{}", costSettlementCycle, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "更新Hr发放标志位", notes = "更新Hr发放标志位")
    @PostMapping("/updateHrGrantFlag")
    @PassToken
    public Result<Boolean> updateHrGrantFlag(
            @ApiParam(value = "工号", required = true) @RequestParam String sendObjectCode,
            @ApiParam(value = "结算周期", required = true) @RequestParam String costSettlementCycle,
            @ApiParam(value = "Hr发放标志位", required = true) @RequestParam Integer hrGrantFlag) {

        // 参数校验
        if (StrUtil.hasBlank(sendObjectCode, costSettlementCycle) || hrGrantFlag == null) {
            return Result.error("参数不能为空");
        }

        if (!isValidHrGrantFlag(hrGrantFlag)) {
            return Result.error("Hr发放标志位只能为0或1");
        }

        try {
            boolean success = hrGrantPersonInfoService.updateHrGrantFlag(sendObjectCode, costSettlementCycle, hrGrantFlag);
            return Result.success(success);

        } catch (Exception e) {
            log.error("更新Hr发放标志位失败，工号：{}，结算周期：{}，标志位：{}", sendObjectCode, costSettlementCycle, hrGrantFlag, e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "批量保存Hr发放人员信息", notes = "批量保存Hr发放人员信息")
    @PostMapping("/batchSave")
    @PassToken
    public Result<Integer> batchSave(@RequestBody @Valid List<SettlementCostHrGrantPersonInfoEntity> list) {

        if (CollectionUtils.isEmpty(list)) {
            return Result.error("保存数据不能为空");
        }

        // 数据校验
        String validationError = validateBatchSaveData(list);
        if (StrUtil.isNotBlank(validationError)) {
            return Result.error(validationError);
        }

        try {
            int count = hrGrantPersonInfoService.saveList(list);
            log.info("批量保存Hr发放人员信息成功，保存记录数：{}", count);
            return Result.success(count);

        } catch (Exception e) {
            log.error("批量保存Hr发放人员信息失败，数据量：{}", list.size(), e);
            return Result.error("批量保存失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "手动同步Hr发放人员信息", notes = "从员工任职记录表同步符合条件的员工数据")
    @PostMapping("/syncFromHr")
    @PassToken
    public Result<Integer> syncFromHr(
            @ApiParam(value = "结算周期", required = true) @RequestParam String settlementCycle,
            @ApiParam(value = "是否强制覆盖", required = false, defaultValue = "false") @RequestParam(defaultValue = "false") boolean forceOverride) {

        if (StringUtils.isBlank(settlementCycle)) {
            return Result.error("结算周期不能为空");
        }

        try {
            int syncCount = hrGrantPersonSyncService.syncHrGrantPersonInfo(settlementCycle, forceOverride);
            return Result.success(syncCount);
        } catch (Exception e) {
            log.error("手动同步Hr发放人员信息失败", e);
            return Result.error("同步失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "清理指定周期的Hr发放人员信息", notes = "删除指定结算周期的Hr发放人员数据")
    @DeleteMapping("/cleanByCycle")
    @PassToken
    public Result<Integer> cleanByCycle(
            @ApiParam(value = "结算周期", required = true) @RequestParam String settlementCycle) {

        if (StringUtils.isBlank(settlementCycle)) {
            return Result.error("结算周期不能为空");
        }

        try {
            int deleteCount = hrGrantPersonSyncService.deleteBySettlementCycle(settlementCycle);
            return Result.success(deleteCount);
        } catch (Exception e) {
            log.error("清理Hr发放人员信息失败", e);
            return Result.error("清理失败：" + e.getMessage());
        }
    }

    /**
     * 转换单个Entity到DTO
     */
    private HrGrantPersonInfoDto convertToDto(SettlementCostHrGrantPersonInfoEntity entity) {
        if (entity == null) {
            return null;
        }
        HrGrantPersonInfoDto dto = new HrGrantPersonInfoDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 转换Entity列表到DTO列表
     */
    private List<HrGrantPersonInfoDto> convertToDtoList(List<SettlementCostHrGrantPersonInfoEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return new ArrayList<>();
        }

        return entities.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * 参数校验 - Hr发放标志位
     */
    private boolean isValidHrGrantFlag(Integer hrGrantFlag) {
        return hrGrantFlag != null && (hrGrantFlag == 0 || hrGrantFlag == 1);
    }

    /**
     * 批量保存数据校验
     */
    private String validateBatchSaveData(List<SettlementCostHrGrantPersonInfoEntity> list) {
        for (int i = 0; i < list.size(); i++) {
            SettlementCostHrGrantPersonInfoEntity entity = list.get(i);

            if (StrUtil.isBlank(entity.getSendObjectCode())) {
                return String.format("第%d条记录的工号不能为空", i + 1);
            }

            if (StrUtil.isBlank(entity.getCostSettlementCycle())) {
                return String.format("第%d条记录的结算周期不能为空", i + 1);
            }

            if (!isValidHrGrantFlag(entity.getHrGrantFlag())) {
                return String.format("第%d条记录的Hr发放标志位只能为0或1", i + 1);
            }
        }
        return null;
    }
}
