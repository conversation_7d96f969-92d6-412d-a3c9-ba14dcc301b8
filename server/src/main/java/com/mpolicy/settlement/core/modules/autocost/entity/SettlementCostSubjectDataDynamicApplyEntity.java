package com.mpolicy.settlement.core.modules.autocost.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

/**
 * 科目范围(动态科目)数据申请记录
 * 
 * <AUTHOR>
 * @since 2023-11-27 20:42:06
 */
@TableName("settlement_cost_subject_data_dynamic_apply")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostSubjectDataDynamicApplyEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 申请编码
	 */
	private String applyCode;
	/**
	 * 批次号
	 */
	private String batchCode;
	/**
	 * 所属周期
	 */
	private String costSettlementCycle;
	/**
	 * 文件编码
	 */
	private String fileCode;
	/**
	 * 文件名称
	 */
	private String fileName;
	/**
	 * 文件地址
	 */
	private String filePath;

	/**
	 * 文件地址域名
	 */
	private String domainPath;
	/**
	 * 动态科目数量
	 */
	private Integer dynamicSize;
	/**
	 * 动态科目名称集合
	 */
	private String dynamicNameList;
	/**
	 * 动态科目成员数量
	 */
	private Integer dynamicPersonnelSize;
	/**
	 * 动态科目总金额
	 */
	private BigDecimal dynamicTotalCash;
	/**
	 * 申请状态;申请状态0待处理-1失败1成功
	 */
	private Integer applyStatus;
	/**
	 * 是否启用;0带启用1启用
	 */
	private Integer applyEnable;
	/**
	 * 申请备注信息
	 */
	private String applyMessage;
	/**
	 * 文件类型 动态科目 dynamic，
	 */
	private String fileType;

	private Integer errorNum;
	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
