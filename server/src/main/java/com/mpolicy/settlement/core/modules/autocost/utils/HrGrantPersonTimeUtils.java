package com.mpolicy.settlement.core.modules.autocost.utils;

import cn.hutool.core.date.DateUtil;
import lombok.experimental.UtilityClass;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * Hr发放人员时间处理工具类
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@UtilityClass
public class HrGrantPersonTimeUtils {

    private static final DateTimeFormatter CYCLE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 解析结算周期
     *
     * @param settlementCycle 结算周期（YYYYMM格式）
     * @return YearMonth对象
     */
    public static YearMonth parseSettlementCycle(String settlementCycle) {
        return YearMonth.parse(settlementCycle, CYCLE_FORMATTER);
    }

    /**
     * 获取上月第一天的开始时间（00:00:00）
     *
     * @param settlementCycle 结算周期
     * @return Date对象
     */
    public static Date getPrevMonthStartTime(String settlementCycle) {
        YearMonth yearMonth = parseSettlementCycle(settlementCycle);
        YearMonth prevMonth = yearMonth.minusMonths(1);
        LocalDateTime startTime = prevMonth.atDay(1).atStartOfDay();
        return DateUtil.date(startTime.atZone(java.time.ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取上月最后一天的结束时间（23:59:59）
     *
     * @param settlementCycle 结算周期
     * @return Date对象
     */
    public static Date getPrevMonthEndTime(String settlementCycle) {
        YearMonth yearMonth = parseSettlementCycle(settlementCycle);
        YearMonth prevMonth = yearMonth.minusMonths(1);
        LocalDateTime endTime = prevMonth.atEndOfMonth().atTime(23, 59, 59);
        return DateUtil.date(endTime.atZone(java.time.ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取上月最后一天的同步时间（00:00:01）
     *
     * @param settlementCycle 结算周期
     * @return Date对象
     */
    public static Date getPrevMonthSyncTime(String settlementCycle) {
        YearMonth yearMonth = parseSettlementCycle(settlementCycle);
        YearMonth prevMonth = yearMonth.minusMonths(1);
        LocalDateTime syncTime = prevMonth.atEndOfMonth().atTime(0, 0, 1);
        return DateUtil.date(syncTime.atZone(java.time.ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取上月第一天
     *
     * @param settlementCycle 结算周期
     * @return LocalDate对象
     */
    public static LocalDate getPrevMonthFirstDay(String settlementCycle) {
        YearMonth yearMonth = parseSettlementCycle(settlementCycle);
        return yearMonth.minusMonths(1).atDay(1);
    }

    /**
     * 获取上月最后一天
     *
     * @param settlementCycle 结算周期
     * @return LocalDate对象
     */
    public static LocalDate getPrevMonthLastDay(String settlementCycle) {
        YearMonth yearMonth = parseSettlementCycle(settlementCycle);
        return yearMonth.minusMonths(1).atEndOfMonth();
    }

    /**
     * 格式化时间范围说明
     *
     * @param settlementCycle 结算周期
     * @return 时间范围说明
     */
    public static String formatTimeRangeDescription(String settlementCycle) {
        LocalDate firstDay = getPrevMonthFirstDay(settlementCycle);
        LocalDate lastDay = getPrevMonthLastDay(settlementCycle);
        Date syncTime = getPrevMonthSyncTime(settlementCycle);
        
        return String.format("结算周期：%s，查询上月范围：%s 00:00:00 ~ %s 23:59:59，同步时间：%s", 
                settlementCycle, 
                firstDay.toString(), 
                lastDay.toString(),
                DateUtil.format(syncTime, "yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 验证结算周期格式
     *
     * @param settlementCycle 结算周期
     * @return 是否有效
     */
    public static boolean isValidSettlementCycle(String settlementCycle) {
        try {
            parseSettlementCycle(settlementCycle);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
