package com.mpolicy.settlement.core.modules.govern.dto.proejct;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/4 15:05
 * @Version 1.0
 */

@Data
public class ChangePremiumInsuredProduct {

    @ApiModelProperty("是否主险种")
    private String mainInsurance;

    @ApiModelProperty("保费")
    private BigDecimal premium;

    @ApiModelProperty("险种编码")
    private String productCode;

    @ApiModelProperty("险种名称")
    private String productName;

    @ApiModelProperty("原保费")
    private String sourcePremium;

}
