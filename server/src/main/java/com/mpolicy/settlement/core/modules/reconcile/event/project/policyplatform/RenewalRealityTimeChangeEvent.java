package com.mpolicy.settlement.core.modules.reconcile.event.project.policyplatform;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileStatusEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.ConfirmStatusEnum;
import com.mpolicy.settlement.core.modules.govern.dto.proejct.ChangeRenewalRealityTimeData;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostCorrectionDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.project.common.AbsPolicyCommonEvent;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventCheckResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventData;
import com.mpolicy.settlement.core.modules.reconcile.service.impl.SettlementCostProcessServiceImpl;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.SYSTEM_CORRECTION_USER;
import static com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum.RENEWAL_TERM_POLICY;

/**
 * <AUTHOR>
 * @description 续期实收时间变更
 * @date 2024/2/26 12:25 上午
 * @Version 1.0
 */
@Service
@Slf4j
public class RenewalRealityTimeChangeEvent extends AbsPolicyCommonEvent {
    @Override
    public HandleEventCheckResult handleIncomeEventCheck(SettlementEventJobEntity eventJob, Integer reconcileType) {
        ChangeRenewalRealityTimeData changePolicyCodeData =
                JSONObject.parseObject(eventJob.getEventRequest(), ChangeRenewalRealityTimeData.class);

        String policyNo = changePolicyCodeData.getPolicyCode();
        // 2.参数校验
        if (StrUtil.isEmpty(policyNo)) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg("续期实收时间变更保单号参数不存在")
                    .build();
        }

        Integer period = changePolicyCodeData.getPeriod();
        // 2.参数校验
        if (period == null) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg("续期实收时间变更期次参数不存在")
                    .build();
        }
        // 判断事件是否已经生成了明细数据,如果生成了就不在运行了
        boolean checkSettlementPolicyInfo = checkSettlementPolicyInfo(eventJob.getPushEventCode(), reconcileType);
        // 如果存在纪录
        if (checkSettlementPolicyInfo) {
            return HandleEventCheckResult.builder().checkStatus(false)
                    .checkMsg(StrUtil.format("续期实收时间变更事件已经写入明细")).build();
        }
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleIncomeEvent(SettlementEventJobEntity eventJob, HandleEventData eventData) {
        ChangeRenewalRealityTimeData changePolicyCodeData =
                JSONObject.parseObject(eventJob.getEventRequest(), ChangeRenewalRealityTimeData.class);
        String policyNo = changePolicyCodeData.getPolicyCode();
        // 2.参数校验
        if (StrUtil.isEmpty(policyNo)) {
            return "事件报文没有获取到保单号";
        }
        List<SettlementPolicyInfoEntity> settlementPolicyInfoArr =
                settlementPolicyInfoService.lambdaQuery()
                        .eq(SettlementPolicyInfoEntity::getPolicyNo, policyNo)
                        .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode())
                        .eq(SettlementPolicyInfoEntity::getSettlementEventCode, RENEWAL_TERM_POLICY.getEventCode())
                        .eq(SettlementPolicyInfoEntity::getRenewalPeriod, changePolicyCodeData.getPeriod())
                        .list();
        // 判断一下 是否存在已经结算的数据,如果存在,那么提示失败
        boolean anyMatch = settlementPolicyInfoArr.stream()
                .anyMatch(a -> ReconcileStatusEnum.RECONCILE_FINISH.getStatusCode().equals(a.getReconcileStatus()));
        if (anyMatch) {
            return "明细存在已经结算的数据,暂不处理";
        }
        // 3.判断数据是否存在
        List<String> pushEventCodes =
                settlementPolicyInfoArr.stream()
                        .map(SettlementPolicyInfoEntity::getEventSourceCode).collect(Collectors.toList());
        if (pushEventCodes.isEmpty()) {
            return "批单号没有获取到结算任务";
        }

        Map<String, List<SettlementPolicyInfoEntity>> settlementPolicyMap =
                handlerSettlementEventJobRectification(pushEventCodes, eventData.getReconcileType(), true);
        // 开始处理数据
        settlementPolicyMap.forEach((pushEventCode, settlementPolicyInfoList) -> {
            settlementPolicyInfoList.forEach(settlementPolicyInfo -> {
                settlementPolicyInfo.setId(null);
                settlementPolicyInfo.setRealityTime(changePolicyCodeData.getPaymentTime());
                settlementPolicyInfo.setEventSourceCode(pushEventCode);
            });
            if (CollUtil.isNotEmpty(settlementPolicyInfoList)) {
                settlementPolicyInfoService.saveBatch(settlementPolicyInfoList);
            }
        });
        return "success";
    }

    @Override
    public HandleEventCheckResult handleCostEventCheck(SettlementEventJobEntity eventJob) {
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleCostEvent(SettlementEventJobEntity eventJob, HandleEventData eventData) {

        ChangeRenewalRealityTimeData renewalFallback =
                JSONObject.parseObject(eventJob.getEventRequest(), ChangeRenewalRealityTimeData.class);
        String policyNo = renewalFallback.getPolicyCode();
        if (StrUtil.isEmpty(policyNo)) {
            return StrUtil.format("保单号-{}不存在-success", policyNo);
        }

        List<SettlementCostInfoEntity> costInfoEntityList = settlementCostInfoService.lambdaQuery()
                .eq(SettlementCostInfoEntity::getContractCode, renewalFallback.getContractCode())
                .eq(SettlementCostInfoEntity::getRenewalPeriod, renewalFallback.getPeriod())
                .eq(SettlementCostInfoEntity::getCorrectionFlag, 0).list();

        if (CollectionUtils.isEmpty(costInfoEntityList)) {
            return StrUtil.format("保单号/合同号-{}/{}不存在-success", policyNo, renewalFallback.getContractCode());
        }

        SettlementCostPolicyInfoEntity costPolicy =
                settlementCostPolicyInfoService.getCostPolicyInfoEntityByContractCode(eventJob.getContractCode());

        if (Objects.isNull(costPolicy)) {
            return "结算保单信息缺失-success";
        }
        //2、参数验证，验证不通过抛出异常
        settlementCostProcessService.validParam(eventJob, null);

        //冲正
        CostCorrectionDto costCorrectionDto = new CostCorrectionDto();


        List<SettlementCostInfoEntity> newCostList = costInfoEntityList.stream().map(
                x -> {

                    //生成一条新结算信息
                    SettlementCostInfoEntity newSettlementCostInfo = new SettlementCostInfoEntity();
                    BeanUtil.copyProperties(x, newSettlementCostInfo);
                    newSettlementCostInfo.setId(null);
                    newSettlementCostInfo.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
                    //记账时间处理
                    newSettlementCostInfo.setSettlementTime(new Date());
                    newSettlementCostInfo.setSettlementDate(newSettlementCostInfo.getSettlementTime());

                    newSettlementCostInfo.setRealityTime(renewalFallback.getPaymentTime());

                    //事件编号
                    newSettlementCostInfo.setEventSourceCode(eventJob.getPushEventCode());

                    //事件信息
                    newSettlementCostInfo.setSettlementEventCode(handlerEventType().getEventCode());
                    newSettlementCostInfo.setSettlementEventDesc(handlerEventType().getEventDesc());
                    newSettlementCostInfo.setCommissionType(x.getCommissionType());
                    newSettlementCostInfo.setSettlementGenerateType(2);
                    //是否冲正
                    //业务记账时间处理
                    newSettlementCostInfo.setBusinessAccountTime(SettlementCostProcessServiceImpl.getCorrectionBusinessAccountTime(x.getBusinessAccountTime(),eventJob.getCreateTime()));
                    newSettlementCostInfo.setSourceCostCode(x.getCostCode());
                    newSettlementCostInfo.setCorrectionTime(new Date());
                    newSettlementCostInfo.setCorrectionFlag(0);

                    newSettlementCostInfo.setCorrectionOpType(1);
                    if (StringUtil.isNotBlank(renewalFallback.getBusinessDesc())) {
                        newSettlementCostInfo.setCorrectionRemark(renewalFallback.getBusinessDesc());
                    }
                    //清除确认信息
                    newSettlementCostInfo.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                    newSettlementCostInfo.setConfirmUser(null);
                    newSettlementCostInfo.setConfirmTime(null);
                    newSettlementCostInfo.setConfirmGrantTime(null);
                    newSettlementCostInfo.setDocumentCode(null);
                    newSettlementCostInfo.setAutoCostCode(null);
                    newSettlementCostInfo.setCostSettlementCycle(null);


                    //冲正
                    SettlementCostInfoEntity offsetCostInfo = settlementCostProcessService.builderOffsetCostInfo(
                            eventJob,
                            handlerEventType(),
                            x,
                            eventJob.getCreateTime(),
                            SYSTEM_CORRECTION_USER,
                            "续期实收时间变更冲正",
                            Boolean.TRUE
                    );

                    return Lists.newArrayList(newSettlementCostInfo, offsetCostInfo);
                }
        ).flatMap(Collection::stream).collect(Collectors.toList());


        costCorrectionDto.setNewCostList(newCostList);
        costCorrectionDto.setCorrectionOldIds(costInfoEntityList.stream().map(SettlementCostInfoEntity::getId).collect(Collectors.toList()));
        costCorrectionDto.setPolicy(costPolicy);

        settlementCostCorrectionService.saveCostCommissionRecord(eventJob, costCorrectionDto);
        policyCenterBaseClient.governApplyConfirm(eventJob.getEventBusinessCode(), "settlement_server", "支出端");
        return "success";
    }

    @Override
    public SettlementEventTypeEnum handlerEventType() {
        return SettlementEventTypeEnum.CHANGE_RENEWAL_REALITY_TIME;
    }
}
