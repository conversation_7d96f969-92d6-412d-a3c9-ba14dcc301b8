package com.mpolicy.settlement.core.modules.autocost.dto.policy;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 保单所属员工信息
 * @date 2023/11/30 10:33 下午
 * @Version 1.0
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PolicyEmployeeDto {
    /**
     * 结算机构编码
     */
    @ApiModelProperty(value = "结算机构编码", example = "A000001")
    private String settlementInstitution;

    /**
     * 结算机构名称
     */
    @ApiModelProperty(value = "结算机构名称", example = "小鲸向海")
    private String settlementInstitutionName;

    /**
     * 员工编码
     */
    @ApiModelProperty(value = "员工编码", example = "ZHNX12345")
    private String employeeCode;

    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称", example = "张三")
    private String employeeName;

    /**
     * 分支机构编码
     */
    @ApiModelProperty(value = "分支机构编码", example = "HNCS")
    private String orgCode;

    /**
     * 分支机构名称
     */
    @ApiModelProperty(value = "分支机构名称", example = "湖南长沙")
    private String orgName;
}
