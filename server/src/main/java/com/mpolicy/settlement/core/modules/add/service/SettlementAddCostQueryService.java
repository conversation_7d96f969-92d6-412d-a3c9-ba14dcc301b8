package com.mpolicy.settlement.core.modules.add.service;

import com.mpolicy.common.utils.PageUtils;
import com.mpolicy.settlement.core.common.reconcile.policy.ZhSettlementCostInfo;
import com.mpolicy.settlement.core.common.reconcile.policy.ZhSettlementCostInfoQuery;
import com.mpolicy.settlement.core.common.reconcile.policy.ZhSettlementCostSummary;
import com.mpolicy.settlement.core.common.reconcile.policy.ZhSettlementPolicyCostInfo;

import java.util.List;

/**
 * 结算支出端加佣管理
 *
 * <AUTHOR>
 * @since 2024-04-26 01:15:12
 */
public interface SettlementAddCostQueryService {
    /**
     * 获取结算支出端加佣列表
     *
     * @param query 查询条件
     * @return 结算支出端加佣列表
     */
    PageUtils<ZhSettlementCostInfo> pageZhSettlementAddCost(ZhSettlementCostInfoQuery query);

    /**
     *
     * @param query
     * @return
     */
    List<ZhSettlementCostInfo> listZhSettlementAddCost(ZhSettlementCostInfoQuery query);

    /**
     *
     * @param query
     * @return
     */
    PageUtils<ZhSettlementPolicyCostInfo> pageZhSettlementPolicyAddCost(ZhSettlementCostInfoQuery query);

    /**
     *
     * @param query
     * @return
     */
    List<ZhSettlementPolicyCostInfo> listZhSettlementPolicyAddCost(ZhSettlementCostInfoQuery query);

    /**
     *
     * @param query
     * @return
     */
    ZhSettlementCostSummary sumSettlementAddCostSmy(ZhSettlementCostInfoQuery query);
}
