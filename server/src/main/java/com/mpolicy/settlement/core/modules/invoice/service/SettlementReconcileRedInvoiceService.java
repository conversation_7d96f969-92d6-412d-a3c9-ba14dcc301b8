package com.mpolicy.settlement.core.modules.invoice.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.invoice.entity.SettlementReconcileRedInvoice;
import com.mpolicy.settlement.core.modules.invoice.vo.third.FttmInvoiceOrderInfoVo;
import com.mpolicy.settlement.core.modules.invoice.vo.third.Result;

/**
 * <AUTHOR>
 * @Date 2024/9/25 20:21
 * @Version 1.0
 */
public interface SettlementReconcileRedInvoiceService extends IService<SettlementReconcileRedInvoice> {
    void handleRedCallback(FttmInvoiceOrderInfoVo vo);
}
