# Hr发放人员信息同步定时任务说明

## 功能概述

该定时任务用于从员工任职记录表(`hr_posting_record_ldom`)中查询符合条件的员工数据，并将其同步到Hr发放人员表(`settlement_cost_hr_grant_person_info`)中。

## 业务规则

### 结算周期说明
- **格式**: YYYYMM（如：202506表示2025年6月）
- **取值**: 当月为结算月

### 员工筛选条件

1. **时间条件**：
   - 上月月末在职的员工（同步时间为上月月末最后一天的数据，且在职的）
   - 上月离职的员工（同步时间为上月月末最后一天的数据，且离职时间在上月第一天和上月月末最后一天之间）

2. **员工类型条件**：
   - 普通员工 (employ_type = 0)
   - 返聘员工 (employ_type = 3)

3. **其他条件**：
   - enabled_flag = 1 (启用状态)
   - service_type = 0 (主职)

### SQL查询示例

#### 上月月末在职的员工（以202506为例）
```sql
SELECT p.employee_code, l.name as employee_name
FROM hr_posting_record_ldom p
LEFT JOIN hr_employee_ldom l ON p.employee_id = l.id AND l.sync_time = '2025-05-31 00:00:01' AND l.enabled_flag = 1
WHERE p.enabled_flag = 1
AND p.service_type = 0
AND p.posting_status = 0
AND p.posting_end_date >= '2025-05-31'  -- 上月末（5月31日）
AND p.employ_type IN (0, 3)
AND p.sync_time = '2025-05-31 00:00:01'  -- 上月末同步时间
```

#### 上月离职的员工（以202506为例）
```sql
SELECT p.employee_code, l.name as employee_name
FROM hr_posting_record_ldom p
LEFT JOIN hr_employee_ldom l ON p.employee_id = l.id AND l.sync_time = '2025-05-31 00:00:01' AND l.enabled_flag = 1
WHERE p.enabled_flag = 1
AND p.service_type = 0
AND p.posting_status = 1
AND p.last_work_date >= '2025-05-01'  -- 上月第一天
AND p.last_work_date <= '2025-05-31'  -- 上月最后一天
AND p.employ_type IN (0, 3)
AND p.sync_time = '2025-05-31 00:00:01'  -- 上月末同步时间
```

## 定时任务配置

### 1. hrGrantPersonSyncJob - 同步任务

**任务名称**: `hrGrantPersonSyncJob`

**参数说明**:
- 无参数或空字符串：使用当月作为结算周期，正常同步模式
- `"true"`：使用当月作为结算周期，强制覆盖模式
- `"YYYYMM"`：指定结算周期，正常同步模式
- `"YYYYMM|true"`：指定结算周期，强制覆盖模式

**示例**:
```
# 正常同步当月数据
参数: (空)

# 强制覆盖当月数据
参数: true

# 同步指定月份数据
参数: 202506

# 强制覆盖指定月份数据
参数: 202506|true
```

### 2. hrGrantPersonCleanJob - 清理任务

**任务名称**: `hrGrantPersonCleanJob`

**参数说明**:
- `"YYYYMM"`：清理指定结算周期的数据

**示例**:
```
# 清理指定月份的数据
参数: 202506
```

## API接口

### 手动同步接口

**接口**: `POST /settlement/cost/hr-grant-person/syncFromHr`

**参数**:
- `settlementCycle`: 结算周期 (必填，格式：YYYYMM)
- `forceOverride`: 是否强制覆盖 (可选，默认false)

### 手动清理接口

**接口**: `DELETE /settlement/cost/hr-grant-person/cleanByCycle`

**参数**:
- `settlementCycle`: 结算周期 (必填，格式：YYYYMM)

## 数据处理逻辑

1. **去重处理**: 同一员工工号在同一结算周期内只保留一条记录
2. **默认值设置**: 
   - `hr_grant_flag`: 默认设置为1（hr发放）
   - `remark`: 设置为"系统自动同步"
3. **数据覆盖**: 强制覆盖模式下会先删除已存在的同周期数据

## 注意事项

1. **数据一致性**: 确保`hr_posting_record_ldom`表的`sync_time`字段与结算周期匹配
2. **性能考虑**: 大批量数据同步时建议在业务低峰期执行
3. **错误处理**: 任务执行失败时会记录详细的错误日志
4. **事务管理**: 同步和清理操作都在事务中执行，确保数据一致性

## 监控和日志

- 任务执行日志会记录在XXL-JOB平台和应用日志中
- 关键操作节点都有详细的日志记录
- 异常情况会触发任务失败并记录错误信息
