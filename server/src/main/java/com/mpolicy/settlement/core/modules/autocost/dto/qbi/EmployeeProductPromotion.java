package com.mpolicy.settlement.core.modules.autocost.dto.qbi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 员工推广费险种信息
 *
 * <AUTHOR>
 * @since  2023-11-13 19:50
 */
@Data
@ApiModel(value = "员工推广费险种信息", description = "员工推广费险种信息")
public class EmployeeProductPromotion implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 员工编号
     */
    @ApiModelProperty(value = "员工编号", example = "ZHNX202020")
    private String employeeCode;

    /**
     * 长短险标记 0短险1长险
     */
    @ApiModelProperty(value = "长短险标记 0短险1长险")
    private Integer longShortFlag;

    /**
     * 保单合同号
     */
    @ApiModelProperty(value = "保单合同号", example = "C12345678")
    private String contractCode;

    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号", example = "PC12345678")
    private String policyNo;

    /**
     * 险种编码
     */
    @ApiModelProperty(value = "险种编码", example = "XJPC12345678")
    private String productCode;

    /**
     * 险种名称
     */
    @ApiModelProperty(value = "险种名称", example = "众安个人意外")
    private String productName;

    /**
     * 险种保费
     */
    @ApiModelProperty(value = "保单号", example = "12.12")
    private BigDecimal premium;

    /**
     * 分支编号
     */
    @ApiModelProperty(value = "结算机构编码", example = "ZHNX202020")
    private String settlementOrgCode;

    /**
     * 分支名称
     */
    @ApiModelProperty(value = "结算机构名称", example = "ZHNX202020")
    private String settlementOrgName;
}
