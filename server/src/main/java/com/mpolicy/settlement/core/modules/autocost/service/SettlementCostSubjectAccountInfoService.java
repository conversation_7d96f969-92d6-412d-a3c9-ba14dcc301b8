package com.mpolicy.settlement.core.modules.autocost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.autocost.dto.base.SettlementCostSubjectAccountInfo;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostQbiSupervisorEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostSubjectAccountInfoEntity;

public interface SettlementCostSubjectAccountInfoService extends IService<SettlementCostSubjectAccountInfoEntity> {
    /**
     * 根据机构编码、工号、账户类型获取账户信息
     * @param objectOrgCode
     * @param sendObjectCode
     * @param accountType
     * @return
     */
    SettlementCostSubjectAccountInfo getSubjectAccountInfo(String objectOrgCode, String sendObjectCode, String accountType);

    /**
     * 初始化账户
     * @param accountInfo
     * @return
     */
    Integer initSubjectAccountInfo(SettlementCostSubjectAccountInfo accountInfo);

    /**
     * 更新账户余额
     * @param accountInfo
     * @return
     */
    boolean updateSubjectAccountAmt(SettlementCostSubjectAccountInfo accountInfo);
}
