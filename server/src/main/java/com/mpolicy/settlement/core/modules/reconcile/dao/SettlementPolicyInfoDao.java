package com.mpolicy.settlement.core.modules.reconcile.dao;

import com.mpolicy.service.common.mapper.ImsBaseMapper;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 结算保单明细记录表
 * 
 * <AUTHOR>
 * @date 2023-05-21 22:28:39
 */
public interface SettlementPolicyInfoDao extends ImsBaseMapper<SettlementPolicyInfoEntity> {

    /**
     * 设置对账完成
     *
     * @param reconcileCode 保司对账单号
     * @param userName 操作员
     * <AUTHOR>
     * @since 2023/5/24 09:25
     */
    void updateSettlementPolicyFinish(@Param("reconcileCode") String reconcileCode,
        @Param("userName") String userName,@Param("settlementMonth")String settlementMonth);
}
