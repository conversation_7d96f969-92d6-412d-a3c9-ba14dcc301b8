package com.mpolicy.settlement.core.modules.autocost.project.data;

import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataReissue;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataRequest;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataRuleInfo;
import com.mpolicy.settlement.core.modules.autocost.helper.SubjectDataHelper;
import com.mpolicy.settlement.core.modules.autocost.project.data.common.ReissueDataService;
import com.mpolicy.settlement.core.modules.autocost.project.data.rule.PolicyCommDataRuleEnum;
import com.mpolicy.settlement.core.modules.autocost.utils.AutoCostUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 【科目5】督导绩效补发数据生成服务
 *
 * <AUTHOR>
 * @since 2023-11-05 17:58
 */
@Service
@Slf4j
public class SupervisorReissuePerformanceDataService extends ReissueDataService<SubjectDataRequest<String>, List<SubjectDataReissue>> {

    @Override
    public CostSubjectEnum getSubjectDataEnum() {
        return CostSubjectEnum.SUPERVISOR_REISSUE_PERFORMANCE;
    }

    /**
     * 督导绩效补发生成数据明细
     * 1、解析规则
     * 2、获取源【督导绩效暂发】数据
     * 3、解析构建科目目标数据
     * 4、写入科目范围数据
     */
    @Override
    public int builderCostSubjectData(String costSettlementCycle, CostSubjectInfo subjectInfo, String batchCode, List<SubjectDataRuleInfo> subjectDataRuleList) {
        log.info("【督导绩效补发】科目数据计算 构建开始......");
        // 1 解析基础规则(解析前推x个月)
        int reissueMonth = SubjectDataHelper.getReissueCostSettlementCycleMonth(subjectInfo, subjectDataRuleList, PolicyCommDataRuleEnum.SUPERVISOR_REISSUE_PERFORMANCE_REISSUE_MONTH);
        log.info("【督导绩效补发】科目需要前置补发月份为={}", reissueMonth);
        String reissueCostSettlementCycle = AutoCostUtils.getReissueCostSettlementCycle(costSettlementCycle, reissueMonth);
        // 2 构建返回
        List<SubjectDataReissue> data = builderSettlementReissue(costSettlementCycle, subjectInfo, batchCode, CostSubjectEnum.SUPERVISOR_PERFORMANCE, reissueCostSettlementCycle);
        if (data.isEmpty()) {
            return 0;
        }
        // 3 写入科目范围数据
        int saveSize = saveSubjectData(data);
        log.info("【督导绩效补发】科目数据计算 构建完成, 构建数据总数={}", saveSize);
        return saveSize;
    }

    @Override
    public int resetSubjectData(String costSettlementCycle) {
        int result = clearSubjectData(costSettlementCycle, getSubjectDataEnum().getCode());
        log.info("【督导绩效补发】数据生成服务 重置完成了......");
        return result;
    }

    @Override
    public List<SubjectDataReissue> subjectData(SubjectDataRequest<String> request) {
        List<SubjectDataReissue> result = querySubjectData(request.getCostSettlementCycle(), getSubjectDataEnum().getCode());
        log.info("【督导绩效补发】科目数据获取完成, 数据数量={}", result.size());
        return result;
    }
}