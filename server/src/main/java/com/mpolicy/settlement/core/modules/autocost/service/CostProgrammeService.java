package com.mpolicy.settlement.core.modules.autocost.service;

import com.mpolicy.settlement.core.modules.autocost.dto.CostProgrammeDetail;
import com.mpolicy.settlement.core.modules.autocost.dto.CostProgrammeInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.CostProgrammeRecordInfo;

/**
 * 方案服务接口
 *
 * <AUTHOR>
 * @since 2023-11-01 22:17
 */
public interface CostProgrammeService {

    /**
     * 根据方案编码查询方案信息
     *
     * @param programmeCode 方案编码
     * @return 方案基本信息
     * <AUTHOR>
     * @since 2023/11/1 17:51
     */
    CostProgrammeInfo queryCostProgrammeInfo(String programmeCode);

    /**
     * 根据方案编码查询方案详细信息
     *
     * @param programmeCode 科目编码
     * @return 方案详细信息
     * <AUTHOR>
     * @since 2023/11/2 13:59
     */
    CostProgrammeDetail queryCostProgrammeDetail(String programmeCode);

    /**
     * 保存或修改方案记录信息
     * 温馨提示：如果存在方案记录信息，则更新，否则新增
     *
     * @param recordInfo 方案记录信息
     * <AUTHOR>
     * @since 2023/11/7 11:11
     */
    void saveOrUpdateSettlementCostProgrammeRecord(CostProgrammeRecordInfo recordInfo);
}