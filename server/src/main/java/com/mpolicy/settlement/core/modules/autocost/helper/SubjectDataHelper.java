package com.mpolicy.settlement.core.modules.autocost.helper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileMonthEnum;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.base.SettlementCostQuery;
import com.mpolicy.settlement.core.modules.autocost.dto.cache.OrganizationCache;
import com.mpolicy.settlement.core.modules.autocost.dto.data.CostSubjectDataRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataRuleInfo;
import com.mpolicy.settlement.core.modules.autocost.project.data.rule.PolicyCommDataRuleEnum;
import com.mpolicy.settlement.core.modules.autocost.service.SubjectDataService;
import com.mpolicy.settlement.core.modules.autocost.utils.AutoCostUtils;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.service.common.ProductBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 科目方案数据辅助类
 *
 * <AUTHOR>
 * @since 2023-10-22 12:24
 */
@Slf4j
@Component
public class SubjectDataHelper {

    public static SubjectDataHelper subjectDataHelper;

    @Autowired
    IRedisService redisService;

    @Autowired
    ProductBaseService productBaseService;

    /**
     * 科目数据服务
     */
    @Autowired
    protected SubjectDataService subjectDataService;

    @PostConstruct
    public void init() {
        subjectDataHelper = this;
        // redis服务
        subjectDataHelper.redisService = this.redisService;
        subjectDataHelper.productBaseService = this.productBaseService;
        subjectDataHelper.subjectDataService = this.subjectDataService;
    }

    /**
     * 科目构建记录信息
     * 温馨提示：如果有成功的返回对象，没有成功的返回null
     *
     * @param costSettlementCycle 结算周期
     * @param subjectCode         科目编码
     * @return 科目构建记录信息
     * <AUTHOR>
     * @since 2023/11/6 20:41
     */
    public static CostSubjectDataRecord checkSubjectDataSuccess(String costSettlementCycle, String subjectCode) {
        // 获取周期+科目的所有记录信息
        List<CostSubjectDataRecord> subjectDataRecord = subjectDataHelper.subjectDataService.querySubjectDataRecord(costSettlementCycle, subjectCode);
        return subjectDataRecord.stream().filter(x -> x.getSubjectDataStatus() == 1).findFirst().orElse(null);
    }

    /**
     * 根据科目规则配置集合构建查询器条件
     *
     * @param subjectDataRuleList 科目规则配置集合
     * @param query               科目数据查询器
     * <AUTHOR>
     * @since 2023/11/4 11:15
     */
    public static void builderDataRuleInfo(String costSettlementCycle, List<SubjectDataRuleInfo> subjectDataRuleList, SettlementCostQuery query) {
        for (SubjectDataRuleInfo rule : subjectDataRuleList) {
            PolicyCommDataRuleEnum ruleType = PolicyCommDataRuleEnum.deCode(rule.getRuleKey());
            switch (ruleType) {
                case ACTIVATION_NEW_POLICY: {
                    // 新契约启用标识 0否 1是
                    query.setNewPolicyActivation(Integer.parseInt(rule.getRuleValue()));
                    break;
                }
                case ACTIVATION_MAINTAIN: {
                    // 续投启动标识  0否 1是
                    query.setMaintainActivation(Integer.parseInt(rule.getRuleValue()));
                    break;
                }
                case ACTIVATION_RENEWAL: {
                    // 续期启动标识  0否 1是
                    query.setRenewalActivation(Integer.parseInt(rule.getRuleValue()));
                    break;
                }
                case ACTIVATION_PRESERVATION: {
                    // 保全启动标识  0否 1是
                    query.setPreservationActivation(Integer.parseInt(rule.getRuleValue()));
                    break;
                }
                case LONG_SHORT_FLAG: {
                    // 长短线配置：长短险标记 0短险1长险
                    query.setLongShortFlag(Integer.parseInt(rule.getRuleValue()));
                    break;
                }
                case RURAL_PROXY_FLAG: {
                    // 整村推进标识： 0否 1是
                    query.setRuralProxyFlag(Integer.parseInt(rule.getRuleValue()));
                    break;
                }
                case DISTRIBUTION: {
                    // 分销单标识：是否分销单 0否 1是
                    query.setDistributionFlag(Integer.parseInt(rule.getRuleValue()));
                    break;
                }
                case SINGLE_PROPOSE_FLAG: {
                    // 一单一议 0否 1是
                    query.setSingleProposeFlag(Integer.parseInt(rule.getRuleValue()));
                    break;
                }
                case VEHICLE_FLAG: {
                    // 车险类型 包含/忽略
                    if (StringUtils.equals(rule.getRuleValue(), "1")) {
                        query.getPolicyProductType().add(PolicyProductTypeEnum.VEHICLE.getCode());
                    } else {
                        query.getIgnorePolicyProductType().add(PolicyProductTypeEnum.VEHICLE.getCode());
                    }
                    break;
                }
                case REGION_CODE_LIST: {
                    // 区域集合：由于底层数据只有机构，所以需要根据区域获取分支机构集合
                    String ruleData = rule.getRuleValue();
                    if (StringUtils.isNotBlank(ruleData)) {
                        List<OrganizationCache> organizationList = SettlementEmployeeHelper.listOrganizationByRegion(Arrays.asList(ruleData.split(",")));
                        if (organizationList != null && !organizationList.isEmpty()) {
                            query.getOrgCodeList().addAll(organizationList.stream().map(OrganizationCache::getBranchCode).collect(Collectors.toList()));
                        }
                    }
                    break;
                }
                case ORG_CODE_LIST: {
                    // 机构分支集合
                    String ruleData = rule.getRuleValue();
                    if (StringUtils.isNotBlank(ruleData)) {
                        CollUtil.addAll(query.getOrgCodeList(), ruleData.split(","));
                    }
                    break;
                }
                case DYNAMIC_RULE_JSON: {
                    // 动态规则配置
                    String ruleData = rule.getRuleValue();
                    if (StringUtils.isNotBlank(ruleData)) {
                        query.setDynamicRule(JSONObject.parseObject(ruleData));
                    }
                    break;
                }
                case REVISIT_FLAG: {
                    // 续期-回访结果 0未回访 1回访成功
                    query.setRevisitFlag(Integer.parseInt(rule.getRuleValue()));
                    break;
                }
                case BUSINESS_START_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 业务开始时间 : {"type":"ONE_MONTH_AGO","day":10}
                    query.setBusinessAccountStartTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),true));
                    break;
                }
                case BUSINESS_END_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 业务截止时间 : {"type":"ONE_MONTH_AGO","day":10}
                    query.setBusinessAccountEndTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),false));
                    break;
                }
                case REVISIT_END_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 续期-回访截止时间 : {"type":"ONE_MONTH_AGO","day":10}
                    query.setRevisitEndTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),false));
                    break;
                }
                case RECEIPT_END_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 续期-回执截止时间 : {"type":"ONE_MONTH_AGO","day":10}
                    query.setReceiptEndTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),false));
                    break;
                }
                case RENEWAL_REALITY_START_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 续期-实收开始时间 : {"type":"ONE_MONTH_AGO","day":10}
                    query.setRealityStartTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),true));
                    break;
                }
                case RENEWAL_REALITY_END_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 续期-实收截止时间 : {"type":"ONE_MONTH_AGO","day":10}
                    query.setRealityEndTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),false));
                    break;
                }
                case RENEWAL_BUSINESS_START_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 续期-业务记账开始时间 : {"type":"ONE_MONTH_AGO","day":10}
                    query.setRealityBusinessStartTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),true));
                    break;
                }
                case RENEWAL_BUSINESS_END_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 续期-业务记账截止时间 : {"type":"ONE_MONTH_AGO","day":10}
                    query.setRealityBusinessEndTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),false));
                    break;
                }
                case NEW_UNDERWRITE_START_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 新契约-承保开始时间 : {"type":"ONE_MONTH_AGO","day":10}
                    query.setNewPolicyUnderwriteStartTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),true));
                    break;
                }
                case NEW_UNDERWRITE_END_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 新契约-承保截止时间 : {"type":"ONE_MONTH_AGO","day":10}
                    query.setNewPolicyUnderwriteEndTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),false));
                    break;
                }
                case NEW_POLICY_BUSINESS_START_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 新契约-业务记账开始时间 : {"type":"ONE_MONTH_AGO","day":10}
                    query.setNewPolicyBusinessStartTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),true));
                    break;
                }
                case NEW_POLICY_BUSINESS_END_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 新契约-业务记账截止时间
                    query.setNewPolicyBusinessEndTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),false));
                    break;
                }
                case MAINTAIN_UNDERWRITE_START_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 续投-承保开始时间
                    query.setRenewalUnderwriteStartTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),true));
                    break;
                }
                case MAINTAIN_UNDERWRITE_END_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 续投-承保截止时间
                    query.setRenewalUnderwriteEndTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),false));
                    break;
                }
                case MAINTAIN_BUSINESS_START_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 续投-业务记账开始时间 : {"type":"ONE_MONTH_AGO","day":10}
                    query.setRenewalBusinessStartTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),true));
                    break;
                }
                case MAINTAIN_BUSINESS_END_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 续投-业务记账截止时间
                    query.setRenewalBusinessEndTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),false));
                    break;
                }
                case PRESERVATION_EFFECTIVE_START_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 保全生效-开始时间
                    query.setPreservationEffectiveStartTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),true));
                    break;
                }
                case PRESERVATION_EFFECTIVE_END_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 保全生效-截止时间
                    query.setPreservationEffectiveEndTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),false));
                    break;
                }
                case PRESERVATION_START_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 保全批改-开始时间
                    query.setPreservationStartTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),true));
                    break;
                }
                case PRESERVATION_END_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 保全批改-截止时间
                    query.setPreservationEndTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),false));
                    break;
                }
                case PRESERVATION_BUSINESS_START_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 保全-业务记账开始时间 : {"type":"ONE_MONTH_AGO","day":10}
                    query.setPreservationBusinessStartTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),true));
                    break;
                }
                case PRESERVATION_BUSINESS_END_TIME: {
                    JSONObject ruleValue = JSONObject.parseObject(rule.getRuleValue());
                    // 保全-业务记账截止时间
                    query.setPreservationBusinessEndTime(AutoCostUtils.builderDateTime(costSettlementCycle, ReconcileMonthEnum.matchSearchCode(ruleValue.getString("type")), ruleValue.getInteger("day"),false));
                    break;
                }
                case AGRICULTURAL_FLAG: {
                    // 农机险表示 0否 1是
                    query.setAgriculturalFlag(Integer.parseInt(rule.getRuleValue()));
                    break;
                }
                case OWNER_THIRD_STATUS_FLAG: {
                    // 是否根据过滤离职后业绩标志位计算
                    if(StringUtils.isNotBlank(rule.getRuleValue())) {
                        query.setOwnerThirdStatus(Integer.parseInt(rule.getRuleValue()));
                    }
                    break;
                }
                default: {
                }
            }
        }
    }

    /**
     * 获取【补发科目】前置配置月份
     *
     * @param subjectDataRuleList 科目规则信息
     * @param dataRuleEnum        规则枚举
     * @return int
     * <AUTHOR>
     * @since 2023/11/21 09:22
     */
    public static int getReissueCostSettlementCycleMonth(CostSubjectInfo subjectInfo, List<SubjectDataRuleInfo> subjectDataRuleList, PolicyCommDataRuleEnum dataRuleEnum) {
        if (subjectDataRuleList.isEmpty()) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("科目补发科目规则缺失，科目:{}", subjectInfo.getSubjectName())));
        }
        SubjectDataRuleInfo ruleInfo = subjectDataRuleList.stream()
                .filter(x -> StringUtils.equals(x.getRuleKey(), dataRuleEnum.getCode()))
                .findFirst()
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("科目规则配置不存在，code:{}", dataRuleEnum.getCode()))));

        return Integer.parseInt(ruleInfo.getRuleValue());
    }

    public static void builderNotDatabaseQueryCntRule(String costSettlementCycle, List<SubjectDataRuleInfo> subjectDataRuleList, SettlementCostQuery query){
        for (SubjectDataRuleInfo rule : subjectDataRuleList) {
            PolicyCommDataRuleEnum ruleType = PolicyCommDataRuleEnum.deCode(rule.getRuleKey());
            switch (ruleType) {
                case PERFORMANCE_ALLOCATION_FLAG: {
                    // 新契约启用标识 0否 1是
                    query.setPerformanceAllocationFlag(Integer.parseInt(rule.getRuleValue()));
                    log.info("开始查询可分配绩效的险种编码");
                    query.setPerformanceProductCods(subjectDataHelper.productBaseService.listAllPerformanceAllocationProductCode());
                    break;
                }

                default: {
                }
            }
        }
    }


}