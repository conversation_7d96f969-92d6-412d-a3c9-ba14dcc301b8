package com.mpolicy.settlement.core.modules.reconcile.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.mpolicy.settlement.core.modules.reconcile.dto.rule.ReconcileCompanyInfo;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileRuleHelper;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileHelperService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 保险公司对账服务相关定时任务
 *
 * <AUTHOR>
 * @since 2023/05/20
 */
@Component
@Slf4j(topic = "reconcileCore")
public class CreateCompanyReconcileJob {

    @Autowired
    private SettlementReconcileService settlementReconcileService;

    @Autowired
    private SettlementReconcileHelperService settlementReconcileHelperService;

    /**
     * 生成保司对账单账单任务
     *
     * <AUTHOR>
     * @since 2023/05/20
     */
    @XxlJob("createCompanyReconcile")
    public void authCreateReconcile() {
        XxlJobHelper.log("执行生成协议对账单(小鲸保单对账单) date= {}", DateUtil.now());
        // 1 获取当前日期的：日
        int day = DateUtil.thisDayOfMonth();
        int lastDay = DateUtil.thisMonthEnum().getLastDay(DateUtil.isLeapYear(DateUtil.thisYear()));
        if (day == lastDay) {
            log.info("当前day为本月最后一天，执行强制修改为【99】");
            // 99的配置对应协协议对账生成勾选的 最后日
            day = 99;
        }

        // 2 获取【保司配置规则生成为账单日=day的保司对账单】
        List<ReconcileCompanyInfo> reconcileCompanyInfos = ReconcileRuleHelper.queryReconcileCompanyInfoList(day);
        if (CollUtil.isNotEmpty(reconcileCompanyInfos)) {
            log.info("需要生成账单数量为={}", reconcileCompanyInfos.size());
            for (ReconcileCompanyInfo reconcileCompanyInfo : reconcileCompanyInfos) {
                // 执行生成对账单
                try {
                    String reconcileCode = settlementReconcileService.createReconcile(reconcileCompanyInfo);
                    log.info("生成账单的保司完成 对账单号={}", reconcileCode);
                }catch (Exception e) {
                    log.warn("生成账单的保司失败 保险公司={}", reconcileCompanyInfo.getCompanyCode(), e);
                }
            }
        } else {
            log.info("没有需要生成账单的保司");
        }
        XxlJobHelper.handleSuccess("执行生成协议对账单(小鲸保单对账单)任务完成");
    }
}
