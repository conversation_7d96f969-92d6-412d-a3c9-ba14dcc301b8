package com.mpolicy.settlement.core.modules.autocost.dao;

import com.mpolicy.service.common.mapper.ImsBaseMapper;
import com.mpolicy.settlement.core.modules.autocost.entity.DimInsuranceSpvsrManageProtectionDfpEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.DwdBranchDirectorPostRecordDfpEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 主任职位信息
 *
 * <AUTHOR>
 * @since 2024-06-07 3:34:32
 */
public interface DwdBranchDirectorPostRecordDfpDao extends ImsBaseMapper<DwdBranchDirectorPostRecordDfpEntity> {
    @Select("<script>" +
                " select * " +

            " from dwd_branch_director_post_record_dfp " +
            " where department_code in " +
            " <foreach collection='bchCodeList' item='key' open='(' separator=',' close=')'> " +
            "    #{key}" +
            " </foreach> " +
            " and job_id in (194243,100567,100570) "+
            " and pt = #{pt} "+
            " and leave_date is null "+
            " </script>")
    List<DwdBranchDirectorPostRecordDfpEntity> listPostRecordByCycleAndBchCodes(@Param("pt")String pt,@Param("bchCodeList")List<String> bchCodeList);
}
