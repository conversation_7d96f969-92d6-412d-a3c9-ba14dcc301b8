package com.mpolicy.settlement.core.modules.autocost.project.calculate.common.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.mpolicy.policy.common.ep.policy.EpPersonalProductInfoVo;
import com.mpolicy.policy.common.settlement.base.SettlementPolicyInfo;
import com.mpolicy.policy.common.settlement.product.SettlementPolicyProduct;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.modules.autocost.dto.CostAutoPolicy;
import com.mpolicy.settlement.core.modules.autocost.dto.CostAutoProduct;
import com.mpolicy.settlement.core.modules.autocost.dto.CostAutoRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataDynamic;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataDynamicApportion;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataDynamicInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.policy.PolicyEmployeeDto;
import com.mpolicy.settlement.core.modules.autocost.dto.policy.PolicyProductShareDto;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.OrgProductQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.ProductCommonQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.enums.DynamicSubjectDataRuleEnum;
import com.mpolicy.settlement.core.modules.autocost.project.calculate.common.DynamicSubjectShareService;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.service.common.SettlementPolicyBaseClient;
import com.mpolicy.settlement.core.utils.LambdaUtils;
import com.netflix.discovery.converters.Auto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/30 12:46 下午
 * @Version 1.0
 */
@Slf4j
@Service
public class ManualPolicyShareServiceImpl extends DynamicSubjectAbstractService implements DynamicSubjectShareService<PolicyProductShareDto> {

    @Autowired
    private SettlementPolicyBaseClient client;

    @Override
    public Boolean route(DynamicSubjectDataRuleEnum eventEnum) {
        return Objects.equals(eventEnum,getActionType());
    }

    @Override
    public DynamicSubjectDataRuleEnum getActionType() {
        return DynamicSubjectDataRuleEnum.MANUAL_POLICY;
    }

    @Override
    public String getItemMapKey(CostAutoRecord costAutoRecord) {
        return keyItem(costAutoRecord.getObjectOrgCode(),costAutoRecord.getSendObjectCode(),costAutoRecord.getSettlementInstitution());
    }
    @Override
    public Map<String, List<PolicyProductShareDto>> mapShareItemDetail(SubjectDataDynamic subjectDataDynamic,List<CostAutoRecord> costAutoRecordList, String costSettlementCycle) {
        List<SubjectDataDynamicInfo> dynamicInfoList = subjectDataDynamic.getDynamicInfoList();
        List<String> policyNos = dynamicInfoList.stream().flatMap(x -> x.getApportionList().stream()).map(SubjectDataDynamicApportion::getPolicyCode).distinct().collect(Collectors.toList());

        //根据保单号获取保单信息
        Map<String, SettlementPolicyInfo> policyInfoMap = client.mapSettlementPolicy(policyNos,true);
        List<PolicyProductShareDto> shareDtos = Lists.newArrayList();
        for(SubjectDataDynamicInfo dynamicInfo : dynamicInfoList){
            List<SubjectDataDynamicApportion> apportionList = dynamicInfo.getApportionList();
            for(int i=0;i<apportionList.size();i++){
                SubjectDataDynamicApportion apportion = apportionList.get(i);
                SettlementPolicyInfo policyInfo = policyInfoMap.get(apportion.getPolicyCode());
                List<SettlementPolicyProduct> productList = policyInfo.getProductList();
                BigDecimal totalDiscountPremium = BigDecimal.ZERO;
                for(SettlementPolicyProduct product : productList){
                    //todo 暂时不会到险种，后续到险种和续期时需要增加逻辑处理
                    //if(Objects.equals(apportion.getProductCode(),product.getProductCode()))
                    PolicyProductShareDto shareDto = PolicyProductShareDto.builder()
                            .settlementInstitution(dynamicInfo.getSettlementInstitution())
                            .settlementInstitutionName(dynamicInfo.getSettlementInstitutionName())
                            .employeeCode(dynamicInfo.getEmployeeCode())
                            .employeeName(dynamicInfo.getEmployeeName())
                            .orgCode(dynamicInfo.getOrgCode())
                            .orgName(dynamicInfo.getOrgName())
                            .apportionCash(apportion.getApportionCash())
                            .contractCode(policyInfo.getContractCode())
                            .policyNo(apportion.getPolicyCode()).build();


                    BeanUtils.copyProperties(product,shareDto);
                    //todo 折算保费字段缺少，先用源保费计算
                    BigDecimal discountPremium = product.getPremium(); //ReconcileBaseHelper.calcDiscountPremium(apportion.getPolicyCode(),product.getProductCode(),null,null,
                            //product.getLongShortFlag(),product.getPremium(),product.getPeriodType(),null,null);
                    shareDto.setDiscountPremium(discountPremium);
                    totalDiscountPremium = totalDiscountPremium.add(discountPremium);
                    shareDtos.add(shareDto);
                }
            }
        }
        log.info("分摊数据记录数={}",shareDtos.size());
        log.info("分摊数据记录={}",shareDtos);
        if(CollectionUtil.isNotEmpty(shareDtos)){
            return LambdaUtils.groupBy(shareDtos,o->keyItem(o.getOrgCode(),o.getEmployeeCode(),o.getSettlementInstitution()));
        }

        return Collections.EMPTY_MAP;
    }

    @Override
    public void calcCostAutoRecordShareItem(CostAutoRecord costAutoRecord, List<PolicyProductShareDto> itemList) {

        log.info("分摊源数据={}",itemList);
        List<CostAutoPolicy> costAutoPolicies = Lists.newArrayList();
        //按保单号分组
        Map<String,List<PolicyProductShareDto>> policyShareMap = LambdaUtils.groupBy(itemList,t->t.getPolicyNo());
        BigDecimal totalPremium ;
        for(String policyNo : policyShareMap.keySet()){
            List<PolicyProductShareDto> policyProductList = policyShareMap.get(policyNo);
            totalPremium = policyProductList.stream().map(x->{
                BigDecimal d = BigDecimal.ZERO;
                if(x.getDiscountPremium()!=null){
                    d = d.add(x.getDiscountPremium());
                }
                return d;
            }).reduce(BigDecimal.ZERO,BigDecimal::add);


            BigDecimal grantAmt = null,dyGrantAmt = BigDecimal.ZERO;
            BigDecimal premium = null,shareRate=null;
            for(int i=0;i<policyProductList.size();i++){
                PolicyProductShareDto shareDto = policyProductList.get(i);
                CostAutoPolicy policy = new CostAutoPolicy();

                BeanUtils.copyProperties(costAutoRecord,policy);

                builderItemPolicyInfo(shareDto,policy);

                premium = BigDecimal.ZERO;
                if(shareDto.getDiscountPremium()!=null){
                    premium = premium.add(shareDto.getDiscountPremium());
                }
                shareRate = premium.divide(totalPremium,6,BigDecimal.ROUND_HALF_UP);
                grantAmt = PolicySettlementUtils.calcAmt(shareDto.getApportionCash(),shareRate,2);

                if(i==policyProductList.size()-1 || dyGrantAmt.add(grantAmt).compareTo(shareDto.getApportionCash())>0){
                    policy.setGrantAmount(shareDto.getApportionCash().subtract(dyGrantAmt));
                    dyGrantAmt = shareDto.getApportionCash();
                }else{
                    policy.setGrantAmount(grantAmt);
                    dyGrantAmt = dyGrantAmt.add(grantAmt);
                }
                //导入单佣金和需要根据比例反推
                policy.setCommissionAmount(policy.getGrantAmount().multiply(new BigDecimal("100")).divide(costAutoRecord.getGrantRate(),2,BigDecimal.ROUND_HALF_UP));
                policy.setAmount(policy.getCommissionAmount().multiply(new BigDecimal("100")).divide(costAutoRecord.getCommissionRate(),2,BigDecimal.ROUND_HALF_UP));

                costAutoPolicies.add(policy);
            }
        }
        costAutoRecord.setAddItemList(costAutoPolicies);

        return ;
    }

    private void builderItemPolicyInfo(PolicyProductShareDto shareDto, CostAutoPolicy policy){
        policy.setCostPolicyCode(PolicySettlementUtils.createCodeLastNumber(Constant.AUTO_COST_POLICY));
        policy.setContractCode(shareDto.getContractCode());
        policy.setPolicyNo(shareDto.getPolicyNo());
        policy.setProductCode(shareDto.getProductCode());
        policy.setProductName(shareDto.getProductName());
        policy.setProductType(shareDto.getProductType());
        policy.setProductGroup(shareDto.getProductGroup());
        policy.setLevel2Code(shareDto.getLevel2Code());
        policy.setLevel2Name(shareDto.getLevel2Name());
        policy.setLevel3Code(shareDto.getLevel3Code());
        policy.setLevel3Name(shareDto.getLevel3Name());
        policy.setLongShortFlag(shareDto.getLongShortFlag());
        policy.setPremium(shareDto.getDiscountPremium());

    }
}
