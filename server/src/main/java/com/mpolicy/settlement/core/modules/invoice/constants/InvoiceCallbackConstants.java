package com.mpolicy.settlement.core.modules.invoice.constants;

import com.google.common.collect.Sets;

import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/9/30 12:55
 * @Version 1.0
 */
public class InvoiceCallbackConstants {

    public static final Set<String> SUCCESS_SETS = Sets.newHashSet("030000");
    public static final Set<String> RETRY_SETS = Sets.newHashSet("031000", "031999", "032999");
    public static final Set<String> HANDLE_SETS = Sets.newHashSet("032000");


}
