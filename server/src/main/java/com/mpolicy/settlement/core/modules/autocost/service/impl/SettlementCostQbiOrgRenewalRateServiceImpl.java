package com.mpolicy.settlement.core.modules.autocost.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.autocost.dao.SettlementCostQbiOrgRenewalRateDao;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostQbiOrgRenewalRateEntity;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostQbiOrgRenewalRateService;
import org.springframework.stereotype.Service;


@Service("settlementCostQbiOrgRenewalRateService")
public class SettlementCostQbiOrgRenewalRateServiceImpl extends ServiceImpl<SettlementCostQbiOrgRenewalRateDao, SettlementCostQbiOrgRenewalRateEntity> implements SettlementCostQbiOrgRenewalRateService {

}
