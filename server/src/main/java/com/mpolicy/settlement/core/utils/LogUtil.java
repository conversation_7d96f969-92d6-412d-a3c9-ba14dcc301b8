package com.mpolicy.settlement.core.utils;

/**
 * <AUTHOR>
 */
public class LogUtil {


    /**
     * 获取异常信息
     * @param e
     * @return
     */
    public static String printLog(Exception e ) {
        StringBuilder logOut = new StringBuilder();
        logOut.append(e.toString());
        logOut.append("\n");
        StackTraceElement[] errorList = e.getStackTrace();
        for (StackTraceElement stackTraceElement : errorList) {
            logOut.append(stackTraceElement.toString());
            logOut.append("\n");
        }
        return logOut.toString();
    }

}
