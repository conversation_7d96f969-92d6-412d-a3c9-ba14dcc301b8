package com.mpolicy.settlement.core.modules.autocost.dto.calculate;

import com.mpolicy.settlement.core.modules.autocost.dto.CostAutoRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/10 11:18 上午
 * @Version 1.0
 */
@Data
@ApiModel(value = "自动结算科目计算基础响应", description = "自动结算科目计算基础响应")
public class SubjectCalculateResponse<T> implements Serializable {

    private static final long serialVersionUID = 1;

    @ApiModelProperty(value = "请求服务", required = true, example = "A000001")
    @NotBlank(message = "请求服务")
    private String serviceName;

    @ApiModelProperty("响应时间")
    private String responseTime;

    @ApiModelProperty("同步耗时")
    private long costMillis;
    /**
     * 明细类型;policy 保单维度，product 险种维度，policy_product保单险种维度
     */
    private String costDataType;



    @ApiModelProperty("自动计算记录列表")
    private List<T> data;

}
