package com.mpolicy.settlement.core.modules.autocost.factory;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataRequest;
import com.mpolicy.settlement.core.modules.autocost.handler.SubjectDataBuilderHandler;
import com.mpolicy.settlement.core.modules.autocost.handler.SubjectDataQueryHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * 【自动结算】科目数据范围工厂
 * 【科目构建器】 + 【科目数据查询器】
 *
 * <AUTHOR>
 * @serial 2023-10-21 22:57
 */
@Component
@Slf4j
public class SubjectDataFactory {

    public static SubjectDataFactory factory;

    private Map<String, SubjectDataBuilderHandler> subjectBuilderDataHandler = Maps.newHashMap();

    private Map<String, SubjectDataQueryHandler> subjectDataQueryHandler = Maps.newHashMap();

    @Autowired
    Map<String, SubjectDataBuilderHandler> subjectBuilderDataHandlerMap;

    @Autowired
    Map<String, SubjectDataQueryHandler> subjectDataQueryHandlerMap;

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        factory = this;
        for (SubjectDataBuilderHandler solver : subjectBuilderDataHandlerMap.values()) {
            if (subjectBuilderDataHandler.containsKey(solver.getSubjectDataEnum().getCode())) {
                throw new GlobalException(BasicCodeMsg.RESOURCE_ERROR.setMsg(StrUtil.format("支出科目数据工厂初始化出现异常，{} 存在重复", solver.getSubjectDataEnum().getName())));
            }
            subjectBuilderDataHandler.put(solver.getSubjectDataEnum().getCode(), solver);
        }
        factory.subjectBuilderDataHandler = subjectBuilderDataHandler;

        for (SubjectDataQueryHandler solver : subjectDataQueryHandlerMap.values()) {
            subjectDataQueryHandler.put(solver.getSubjectDataEnum().getCode(), solver);
        }
        factory.subjectDataQueryHandler = subjectDataQueryHandler;
    }

    /**
     * 根据科目编码获取科目数据构建处理器
     *
     * @param costSubject 科目类型枚举
     * @return com.mpolicy.settlement.core.modules.autocost.handler.SubjectDataHandler
     * <AUTHOR>
     * @since 2023/10/22 17:50
     */
    public static SubjectDataBuilderHandler getSubjectBuilderDataHandler(CostSubjectEnum costSubject) {
        if (costSubject == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("科目举类型不能为空"));
        }
        if (!factory.subjectBuilderDataHandler.containsKey(costSubject.getCode())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("科目举类型暂不支持，科目编码={}", costSubject.getCode())));
        }
        return factory.subjectBuilderDataHandler.get(costSubject.getCode());
    }

    /**
     * 根据科目编码获取科目数据查询器
     * 说明：T 为查询的请求对象，R为查询结果对象
     *
     * @param costSubject 科目类型枚举
     * @return com.mpolicy.settlement.core.modules.autocost.handler.SubjectDataQueryHandler
     * <AUTHOR>
     * @since 2023/11/1 16:54
     */
    public static <T extends SubjectDataRequest<?>, R> SubjectDataQueryHandler<T, R> getSubjectDataQueryHandler(CostSubjectEnum costSubject) {
        if (costSubject == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("科目举类型不能为空"));
        }
        if (!factory.subjectDataQueryHandler.containsKey(costSubject.getCode())) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("科目举类型暂不支持，科目编码={}", costSubject.getCode())));
        }
        return factory.subjectDataQueryHandler.get(costSubject.getCode());
    }
}