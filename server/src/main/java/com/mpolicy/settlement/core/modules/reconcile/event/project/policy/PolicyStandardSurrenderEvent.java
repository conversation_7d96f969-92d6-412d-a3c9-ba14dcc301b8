package com.mpolicy.settlement.core.modules.reconcile.event.project.policy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.enums.ReconcileTypeEnum;
import com.mpolicy.settlement.core.enums.SettlementPolicyHandlerEnum;
import com.mpolicy.settlement.core.modules.reconcile.dto.policy.PolicyPreservationDetailDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.SettlementPolicyFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementPolicyHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.project.common.AbsPolicyCommonEvent;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventCheckResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventData;
import com.mpolicy.settlement.core.modules.reconcile.vo.BasisSettlementPolicyInfoDomain;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 结算事件-【保单中心】保全-标准退保
 *
 * <AUTHOR>
 * @since 2023-05-20 21:40
 */
@Service
@Slf4j
public class PolicyStandardSurrenderEvent extends AbsPolicyCommonEvent {


    @Override
    public SettlementEventTypeEnum handlerEventType() {
        return SettlementEventTypeEnum.STANDARD_SURRENDER;
    }

    /**
     * 【保单中心】保全-标准退保
     */
    @Override
    public HandleEventCheckResult handleIncomeEventCheck(SettlementEventJobEntity eventJob, Integer reconcileType) {
        // 2 获取保全的批改单号，判断是否存在结算明细纪录
        JSONObject eventData = JSONObject.parseObject(eventJob.getEventRequest());
        String preservationCode = eventData.getString("preservationCode");
        if (StrUtil.isBlank(preservationCode)) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg(
                    StrUtil.format("保全申请编码不存在,数据结构异常,不做处理 PushEventCode={}", eventJob.getPushEventCode())).build();
        }
        // 3 根据保全操作唯一编号判断是否纪录
        Integer count = settlementPolicyInfoService.lambdaQuery()
                .eq(SettlementPolicyInfoEntity::getPolicyNo, eventJob.getEventBusinessCode())
                .eq(SettlementPolicyInfoEntity::getReconcileType, reconcileType)
                .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode())
                .eq(SettlementPolicyInfoEntity::getPreservationCode, preservationCode)
                .count();
        // 如果存在纪录
        if (count > 0) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg(StrUtil.format("该保全【标准退保】纪录存在结算明细数据，保单号={} 保全申请单号={},reconcileType={}", eventJob.getEventBusinessCode(),preservationCode, reconcileType)).build();
        }
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleIncomeEvent(SettlementEventJobEntity eventJob, HandleEventData handleEventData) {
        // 1 获取保单详情
        EpContractInfoVo policyInfo = handleEventData.getPolicyInfo();
        if (policyInfo.getContractBaseInfo().getLongShortFlag() != null && policyInfo.getContractBaseInfo().getLongShortFlag() == 1) {
            return "长险直接通过-success";
        }
        try {
            // 从请求报文里获取期数属性
            JSONObject eventRequest = JSONObject.parseObject(eventJob.getEventRequest());
            String preservationCode = eventRequest.getString("preservationCode");
            if (StrUtil.isBlank(preservationCode)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("附加险解约 保单号={}, 缺少保全编码", eventJob.getEventBusinessCode())));
            }
            ReconcileTypeEnum reconcileTypeEnum = ReconcileTypeEnum.matchSearchCode(handleEventData.getReconcileType());
            BasisSettlementPolicyInfoDomain domain = new BasisSettlementPolicyInfoDomain();
            domain.setContractInfo(handleEventData.getPolicyInfo());
            domain.setSettlementEventTypeEnum(SettlementEventTypeEnum.STANDARD_SURRENDER);
            domain.setReconcileTypeEnum(reconcileTypeEnum);
            domain.setPreservationCode(preservationCode);
            domain.setEventSourceCode(eventJob.getPushEventCode());
            // 获取处理事件
            SettlementPolicyHandler handler =
                SettlementPolicyFactory.getInvoke(SettlementPolicyHandlerEnum.PRESERVATION );
            // 构建结算明细基础信息
            List<SettlementPolicyInfoEntity> settlementPolicyInfoList = handler.buildBasisSettlementPolicyInfo(domain);
            if (CollUtil.isNotEmpty(settlementPolicyInfoList)) {
                //匹配产品信息
                handler.matchInsuranceProduct(reconcileTypeEnum, settlementPolicyInfoList);
                // 构建手续费和折标保费金额
                List<SettlementPolicyInfoEntity> resultList =
                    handler.buildSettlementSubjectAmount(settlementPolicyInfoList);
                if (!resultList.isEmpty()) {
                    settlementPolicyInfoService.saveBatch(resultList);
                }
            } else {
                return StrUtil.format("没有生成结算明细，保单号={}, 事件编码={}", eventJob.getEventBusinessCode(),
                    eventJob.getPushEventCode());
            }
        } catch (GlobalException e) {
            throw e;
        } catch (Exception e) {
            String msg = StrUtil.format("生成结算明细异常，保单号={}, 事件编码={}", eventJob.getEventBusinessCode(),
                eventJob.getPushEventCode());
            log.warn(msg, e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(msg));
        }
        return "success";
    }

    @Override
    public HandleEventCheckResult handleCostEventCheck(SettlementEventJobEntity eventJob) {
        return super.handlerPreservationCostEventCheck(eventJob, SettlementEventTypeEnum.STANDARD_SURRENDER);
    }

    @Override
    public String handleCostEvent(SettlementEventJobEntity eventJob, HandleEventData handleEventData) {
        EpContractInfoVo policyInfo = handleEventData.getPolicyInfo();

        PolicyPreservationDetailDto preservationDetailDto = builderPolicyPreservationDetail(JSONObject.parseObject(eventJob.getEventRequest()));
        PolicyProductTypeEnum policyProductTypeEnum = PolicyProductTypeEnum.getProdTypeEnum(policyInfo.getPolicyProductType());
        //1、参数验证，验证不通过抛出异常
        settlementCostProcessService.validParam(eventJob, policyInfo, () -> {
            if (preservationDetailDto.getSurrenderCash() == null) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("支出端-该标准退保保全获取保单详情，保全保费信息缺失，保单号=" + eventJob.getEventBusinessCode()));
            }
            if (policyProductTypeEnum == null) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("标准退保保全事件失败，无法获取保单类，保单号={}, 保单类型={}", eventJob.getEventBusinessCode(), eventJob.getEventBusinessCode())));
            }
        });
        /*if (!super.checkBusinessAccountTime(SettlementEventTypeEnum.STANDARD_SURRENDER, preservationDetailDto) && !Objects.equals(eventJob.getCostEventStatus(),3)) {
            return "保全生效时间早于约定时间，不计算success";
        }*/
        //todo 需要获取当前的险种信息
        // 主险 如果是保单是长线的话，退保是不需要做结算处理
        ProductBase mainProductBase = productBaseService.getProductInfo(policyInfo.getContractBaseInfo().getMainProductCode());
        //大于第二期的退保还是生成为0的数据，不做直接通过 2024-06-20
        /*if (Objects.equals(policyInfo.getChannelInfo().getChannelCode(), Constant.ZHNX_CHANNEL_CODE)) {
            if (preservationDetailDto.getRenewalTermPeriod() >= 2) {
                if (mainProductBase.getLongShortFlag() != null && mainProductBase.getLongShortFlag() == 1 && Objects.equals(policyInfo.getContractExtendInfo().getRevisitResult(), 1)) {
                    //犹豫期外且回访成功的长险保单单，续期了第二年后退保的保单信息还是需要存储到系统，后续自动结算时需要用到
                    SettlementCostPolicyInfoEntity costPolicy = genCostPolicy(policyInfo);
                    //设置退保所在期数
                    costPolicy.setSurrenderTermPeriod(preservationDetailDto.getRenewalTermPeriod());
                    settlementCostProcessService.updateCostPolicyRecord(costPolicy);
                    return "犹豫期外且回访成功的长险保单单，续期了第二年后退保直接通过-success";
                }
            }
        }*/
//        else{
//            if(mainProductBase.getLongShortFlag() != null && mainProductBase.getLongShortFlag() == 1){
//                return "长险直接通过-success";
//            }
//        }
        //2、获取所有小鲸险种集合
        Map<String, ProductBase> productMap = settlementCostProcessService.getProductBaseMap(policyInfo.getProductInfoList());

        //3、获取退保明细数据和数据录入时间
        //preservationDetailDto.setSurrenderDetail(getSurrenderDetail(preservationDetailDto.getPreservationCode()));
        setSurrenderDetail(preservationDetailDto,true);

        //4、支出端-创建保单基础信息
        SettlementCostPolicyInfoEntity costPolicy = genCostPolicy(policyInfo);
        //设置退保所在期数
        costPolicy.setSurrenderTermPeriod(preservationDetailDto.getRenewalTermPeriod());
        //设置退保所在期数
        costPolicy.setSurrenderTermPeriod(preservationDetailDto.getRenewalTermPeriod());
        //5、生成佣金记录
        List<SettlementCostInfoEntity> costInfoList = settlementCostSurrenderProcessService.builderStandardSurrenderCostInfo(eventJob, CostSubjectEnum.FIRST_BASIC_COMM, SettlementEventTypeEnum.STANDARD_SURRENDER, productMap, policyInfo, preservationDetailDto);
        //处理农保员工在职状态
        settlementCostOwnerService.handlerOwnerThirdStatus(costPolicy,costInfoList);
        //6、保存记录信息
        settlementCostProcessService.saveCostCommissionRecord(eventJob, costPolicy, costInfoList);

        return "success";
    }
}