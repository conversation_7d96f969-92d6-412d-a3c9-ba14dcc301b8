package com.mpolicy.settlement.core.modules.reconcile.enums;

import com.mpolicy.policy.common.enums.PolicyContractStatusEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

public enum ProductStatusEnum {
    /**
     * 犹豫期内退保
     */
    HESITATION_CANCEL("POLICY_STATUS:1", "犹豫期内退保", "犹豫期内退保"),
    /**
     * 退保
     */
    CANCELLATION("POLICY_STATUS:2", "退保", "退保"),
    /**
     * 保单中止
     */
    DISCONTINUE("POLICY_STATUS:3", "保单中止", "保单中止"),
    /**
     * 终止
     */
    SIGNED("POLICY_STATUS:4", "终止", "终止"),
    /**
     * 承保
     */
    TERMINATION("POLICY_STATUS:5", "承保", "承保"),
    /**
     * 生效中
     */
    ACTIVE("POLICY_STATUS:6", "生效中", "生效中"),
    /**
     * 协议解约
     */
    AGREEMENT_TERMINATION("POLICY_STATUS:7", "协议解约", "协议解约"),
    /**
     * 理赔终止
     */
    CLAIMS_TERMINATION("POLICY_STATUS:8", "理赔终止", "理赔终止"),
    /**
     * 预收
     */
    ADVANCE("POLICY:ADVANCE_STATUS:1", "预收", "预收"),
    /**
     * 撤单
     */
    CANCEL_ORDER("POLICY:ADVANCE_STATUS:2", "撤单", "撤单"),
    /**
     * 延期承保
     */
    DEFERRED_UNDERWRITING("POLICY:ADVANCE_STATUS:3", "延期承保", "延期承保"),
    /**
     * 拒保
     */
    REFUSAL("POLICY:ADVANCE_STATUS:4", "拒保", "拒保");

    @Getter
    private final String code;

    @Getter
    private final String statusDesc;
    @Getter
    private final String desc;


    ProductStatusEnum(String code, String statusDesc, String desc) {
        this.code = code;
        this.statusDesc = statusDesc;
        this.desc = desc;
    }

    public static ProductStatusEnum getPolicyContractStatusEnum(String code) {
        return Arrays.stream(ProductStatusEnum.values())
                .filter(x -> x.code.equals(code))
                .findFirst().orElse(null);
    }

    public static ProductStatusEnum getEnumByDesc(String statusDesc) {
        return Arrays.stream(ProductStatusEnum.values())
                .filter(x -> x.statusDesc.equals(statusDesc))
                .findFirst().orElse(null);
    }

    public static String getCodeByDesc(String desc) {
        return Arrays.stream(ProductStatusEnum.values())
                .filter(x -> x.desc.equals(desc))
                .map(ProductStatusEnum::getCode)
                .findAny().orElse(null);
    }

    /**
     * 判断保单是否为不可逆状态
     *
     * @param bean
     * @return
     */
    public static boolean isIrreversibleStatus(ProductStatusEnum bean) {
        return DISCONTINUE.equals(bean)
                || SIGNED.equals(bean)
                || isSurrenderStatus(bean.getCode())
                ;
    }

    /**
     * 判断保单是否为退保状态
     *
     * @param code
     * @return
     */
    public static boolean isSurrenderStatus(String code) {
        return getSurrenderStatusEnumCodes().contains(code);
    }

    /**
     * 判断保单是否为退保状态
     *
     * @param code
     * @return
     */
    public static boolean isEffectiveStatus(String code) {
        return getEffectiveStatusEnums().contains(code);
    }

    /**
     * 获取退保状态的枚举列表
     *
     * @return 退保状态的状态枚举
     */
    public static List<ProductStatusEnum> getSurrenderStatusEnums() {
        return Arrays.asList(HESITATION_CANCEL, CANCELLATION, AGREEMENT_TERMINATION, CLAIMS_TERMINATION);
    }

    public static void main(String[] args){
        String str = "POLICY_STATUS:1";
        System.out.println(isSurrenderStatus(str));
    }

    /**
     * 获取退保状态的枚举列表
     *
     * @return 退保状态的状态枚举
     */
    public static List<String> getEffectiveStatusEnums() {
        return Arrays.asList(DISCONTINUE.getCode(), SIGNED.getCode(), TERMINATION.getCode(), ACTIVE.getCode());
    }

    /**
     * 获取退保状态的枚举列表
     *
     * @return 退保状态的状态枚举
     */
    public static List<String> getSurrenderStatusEnumCodes() {
        return Arrays.asList(HESITATION_CANCEL.getCode(), CANCELLATION.getCode(), AGREEMENT_TERMINATION.getCode(), CLAIMS_TERMINATION.getCode());
    }





}
