package com.mpolicy.settlement.core.modules.reconcile.event.project.policyplatform;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.enums.ReconcileTypeEnum;
import com.mpolicy.settlement.core.enums.SettlementPolicyHandlerEnum;
import com.mpolicy.settlement.core.modules.govern.dto.proejct.ChangeRenewalFallbackData;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostCorrectionDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.SettlementPolicyFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementPolicyHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.project.common.AbsPolicyCommonEvent;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventCheckResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventData;
import com.mpolicy.settlement.core.modules.reconcile.vo.BasisSettlementPolicyInfoDomain;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.SYSTEM_CORRECTION_USER;

/**
 * <AUTHOR>
 * @description 续期回退
 * @date 2024/2/26 12:27 上午
 * @Version 1.0
 */
@Service
@Slf4j
public class RenewalFallbackChangeEvent extends AbsPolicyCommonEvent {
    @Override
    public HandleEventCheckResult handleIncomeEventCheck(SettlementEventJobEntity eventJob, Integer reconcileType) {
        ChangeRenewalFallbackData changeRenewalFallbackData =
                JSONObject.parseObject(eventJob.getEventRequest(), ChangeRenewalFallbackData.class);
        Integer period = changeRenewalFallbackData.getPeriod();
        if (period == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("续期回退 保单号={}, 缺少续期期次", eventJob.getEventBusinessCode())));
        }
        String policyNo = changeRenewalFallbackData.getPolicyCode();
        // 2.参数校验
        if (StrUtil.isEmpty(policyNo)) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg("续期回退事件没有获取到保单号").build();
        }
        // 判断事件是否已经生成了明细数据,如果生成了就不在运行了
        boolean checkSettlementPolicyInfo = checkSettlementPolicyInfo(eventJob.getPushEventCode(), reconcileType);
        // 如果存在纪录
        if (checkSettlementPolicyInfo) {
            return HandleEventCheckResult.builder().checkStatus(false)
                    .checkMsg(StrUtil.format("续期回退事件已经写入明细")).build();
        }
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleIncomeEvent(SettlementEventJobEntity eventJob, HandleEventData handleEventData) {
        // 从请求报文里获取期数属性
        ChangeRenewalFallbackData changeRenewalFallbackData =
                JSONObject.parseObject(eventJob.getEventRequest(), ChangeRenewalFallbackData.class);
        Integer period = changeRenewalFallbackData.getPeriod();
        if (period == null) {
            return StrUtil.format("续期回退 保单号={}, 缺少续期期次", eventJob.getEventBusinessCode());
        }
        String policyNo = changeRenewalFallbackData.getPolicyCode();
        // 2.参数校验
        if (StrUtil.isEmpty(policyNo)) {
            return "续期回退事件没有获取到保单号";
        }
        try {
            ReconcileTypeEnum reconcileTypeEnum = ReconcileTypeEnum.matchSearchCode(handleEventData.getReconcileType());
            BasisSettlementPolicyInfoDomain domain = new BasisSettlementPolicyInfoDomain();
            domain.setContractInfo(handleEventData.getPolicyInfo());
            domain.setSettlementEventTypeEnum(SettlementEventTypeEnum.CHANGE_RENEWAL_FALLBACK);
            domain.setReconcileTypeEnum(reconcileTypeEnum);
            domain.setEventSourceCode(eventJob.getPushEventCode());
            domain.setPeriod(period);
            // 获取处理事件
            SettlementPolicyHandler handler = SettlementPolicyFactory.getInvoke(SettlementPolicyHandlerEnum.RENEWAL_TERM);
            // 构建结算明细基础信息
            List<SettlementPolicyInfoEntity> settlementPolicyInfoList = handler.buildBasisSettlementPolicyInfo(domain);
            if (CollUtil.isNotEmpty(settlementPolicyInfoList)) {
                //匹配产品信息
                handler.matchInsuranceProduct(reconcileTypeEnum, settlementPolicyInfoList);
                // 构建手续费和折标保费金额
                List<SettlementPolicyInfoEntity> resultList =
                        handler.buildSettlementSubjectAmount(settlementPolicyInfoList);
                if (!resultList.isEmpty()) {
                    settlementPolicyInfoService.saveBatch(resultList);
                }
            } else {
                return StrUtil.format("没有生成结算明细，保单号={}, 事件编码={}", eventJob.getEventBusinessCode(),
                        eventJob.getPushEventCode());
            }
        } catch (GlobalException e) {
            throw e;
        } catch (Exception e) {
            String msg = StrUtil.format("生成结算明细异常，保单号={}, 事件编码={}", eventJob.getEventBusinessCode(),
                    eventJob.getPushEventCode());
            log.warn(msg, e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(msg));
        }
        return "success";
    }

    @Override
    public HandleEventCheckResult handleCostEventCheck(SettlementEventJobEntity eventJob) {
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleCostEvent(SettlementEventJobEntity eventJob, HandleEventData eventData) {
        ChangeRenewalFallbackData renewalFallback =
                JSONObject.parseObject(eventJob.getEventRequest(), ChangeRenewalFallbackData.class);
        String policyNo = renewalFallback.getPolicyCode();
        if (StrUtil.isEmpty(policyNo)) {
            return StrUtil.format("保单号-{}不存在-success", policyNo);
        }

        List<SettlementCostInfoEntity> costInfoEntityList = settlementCostInfoService.lambdaQuery()
                .eq(SettlementCostInfoEntity::getContractCode, renewalFallback.getContractCode())
                .eq(SettlementCostInfoEntity::getRenewalPeriod, renewalFallback.getPeriod())
                .eq(SettlementCostInfoEntity::getCorrectionFlag, 0).list();

        if (CollectionUtils.isEmpty(costInfoEntityList)) {
            return StrUtil.format("保单号/合同号-{}/{}不存在-success", policyNo, renewalFallback.getContractCode());
        }

        SettlementCostPolicyInfoEntity costPolicy =
                settlementCostPolicyInfoService.getCostPolicyInfoEntityByContractCode(eventJob.getContractCode());

        if (Objects.isNull(costPolicy)) {
            return "结算保单信息缺失-success";
        }
        //2、参数验证，验证不通过抛出异常
        settlementCostProcessService.validParam(eventJob, null);

        //冲正
        CostCorrectionDto costCorrectionDto = new CostCorrectionDto();

        List<SettlementCostInfoEntity> newCostList = costInfoEntityList.stream().map(x -> {
            //冲正
            return settlementCostProcessService.builderOffsetCostInfo(eventJob, handlerEventType(), x,
                    eventJob.getCreateTime(), SYSTEM_CORRECTION_USER, "续期回退变更冲正", Boolean.TRUE);
        }).collect(Collectors.toList());

        costCorrectionDto.setNewCostList(newCostList);
        costCorrectionDto.setCorrectionOldIds(
                costInfoEntityList.stream().map(SettlementCostInfoEntity::getId).collect(Collectors.toList()));
        costCorrectionDto.setPolicy(costPolicy);

        settlementCostCorrectionService.saveCostCommissionRecord(eventJob, costCorrectionDto);
        policyCenterBaseClient.governApplyConfirm(eventJob.getEventBusinessCode(), "settlement_server", "支出端");
        return "success";
    }

    @Override
    public SettlementEventTypeEnum handlerEventType() {
        return SettlementEventTypeEnum.CHANGE_RENEWAL_FALLBACK;
    }
}
