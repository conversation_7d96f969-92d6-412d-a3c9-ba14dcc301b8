package com.mpolicy.settlement.core.modules.autocost.service.impl;

import com.alibaba.fastjson.JSON;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.dto.data.CostSubjectDataRecord;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostProgrammeSubjectEntity;
import com.mpolicy.settlement.core.modules.autocost.factory.SubjectCalculateFactory;
import com.mpolicy.settlement.core.modules.autocost.factory.SubjectDataFactory;
import com.mpolicy.settlement.core.modules.autocost.handler.SubjectCalculateHandler;
import com.mpolicy.settlement.core.modules.autocost.handler.SubjectDataBuilderHandler;
import com.mpolicy.settlement.core.modules.autocost.service.AutoSettlementManagerService;
import com.mpolicy.settlement.core.modules.autocost.service.AutoSettlementProcessService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostProgrammeSubjectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class AutoSettlementManagerServiceImpl implements AutoSettlementManagerService {

    @Autowired
    private AutoSettlementProcessService autoSettlementProcessService;
    @Autowired
    private TransactionTemplate transactionTemplate;


    /**
     * 异步批量重置科目(抽数+重算)
     * @param subjectCodes
     * @param costSettlementCycle
     */
    @Async
    public void batchReSetBySubjectCodes(List<String> subjectCodes,String costSettlementCycle){
        if(CollectionUtils.isEmpty(subjectCodes)){
            return ;
        }
        List<SettlementCostProgrammeSubjectEntity> subjectEntityList = autoSettlementProcessService.listBySubjectCodes(subjectCodes);
        subjectEntityList.stream().forEach(s->{
            autoSettlementProcessService.singleSubjectHandler(s,costSettlementCycle);
        });
    }
    /**
     * 异步批量抽数科目
     * @param subjectCodes
     * @param costSettlementCycle
     */
    @Async
    public void batchReDataBySubjectCodes(List<String> subjectCodes,String costSettlementCycle){
        if(CollectionUtils.isEmpty(subjectCodes)){
            return ;
        }
        List<SettlementCostProgrammeSubjectEntity> subjectEntityList = autoSettlementProcessService.listBySubjectCodes(subjectCodes);
        subjectEntityList.stream().forEach(s->{
            autoSettlementProcessService.singleSubjectDataHandler(s,costSettlementCycle);
        });
    }
    /**
     * 异步批量重算科目
     * @param subjectCodes
     * @param costSettlementCycle
     */
    @Async
    public void batchReCalcBySubjectCodes(List<String> subjectCodes,String costSettlementCycle){
        log.info("异步批量重算科目,{},{}", JSON.toJSONString(subjectCodes),costSettlementCycle);
        if(CollectionUtils.isEmpty(subjectCodes)){
            return ;
        }
        List<SettlementCostProgrammeSubjectEntity> subjectEntityList = autoSettlementProcessService.listBySubjectCodes(subjectCodes);
        subjectEntityList.stream().forEach(s->{
            autoSettlementProcessService.singleSubjectCalcHandler(s,costSettlementCycle);
        });
    }

}
