package com.mpolicy.settlement.core.utils;

import cn.hutool.core.util.StrUtil;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.modules.protocol.entity.OrgInfoEntity;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class PremUtil {

    public static boolean verifyParam(String source, String desc) {
        if (StrUtil.isBlank(desc)) {
            // 不校验直接返回
            return true;
        }
        return desc.equals(source);
    }

    /**
     * 校验年龄
     *
     * @param age     年龄
     * @param ageRang 年龄区间
     * @return
     */
    public static boolean verifyAge(Integer age, String ageRang) {
        if (StrUtil.isBlank(ageRang)) {
            // 不校验年龄
            return true;
        }
        if (age == null) {
            return false;
        }
        int[] ints = Arrays.stream(ageRang.split("~")).mapToInt(Integer::parseInt).toArray();
        if (ints.length == 1) {
            return age == ints[0];
        } else {
            return (age >= ints[0] && age <= ints[1]);
        }
    }

    /**
     * 获取上一级机构信息,如果他是机构那么就返回当前机构信息
     * 如果没有匹配到就返回全国
     * @param orgCode     组织编码
     * @param orgInfoList 组织列表
     * @return
     */
    public static OrgInfoEntity getOrgCode(String orgCode, List<OrgInfoEntity> orgInfoList) {
        OrgInfoEntity orgInfo =
            orgInfoList.stream().filter(f -> f.getOrgCode().equals(orgCode)).findFirst().orElse(null);
        if (orgInfo == null) {
            orgInfo = new OrgInfoEntity();
            orgInfo.setOrgCode(Constant.NATIONWIDE_ORG_CODE);
            orgInfo.setOrgName("全国");
            return orgInfo;
        }
        if (!StatusEnum.INVALID.getCode().equals(orgInfo.getOrgType())) {
            return getOrgCode(orgInfo.getOrgSuperiorCode(), orgInfoList);
        }
        return orgInfo;
    }
}
