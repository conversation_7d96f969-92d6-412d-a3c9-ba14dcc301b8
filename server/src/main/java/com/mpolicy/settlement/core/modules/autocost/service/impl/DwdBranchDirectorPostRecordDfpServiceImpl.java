package com.mpolicy.settlement.core.modules.autocost.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.autocost.dao.DimInsuranceSpvsrManageProtectionDfpDao;
import com.mpolicy.settlement.core.modules.autocost.dao.DwdBranchDirectorPostRecordDfpDao;
import com.mpolicy.settlement.core.modules.autocost.dto.BranchDirectorPostRecordDto;
import com.mpolicy.settlement.core.modules.autocost.entity.DimInsuranceSpvsrManageProtectionDfpEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.DwdBranchDirectorPostRecordDfpEntity;
import com.mpolicy.settlement.core.modules.autocost.enums.HrJobPostEnum;
import com.mpolicy.settlement.core.modules.autocost.service.DimInsuranceSpvsrManageProtectionDfpService;
import com.mpolicy.settlement.core.modules.autocost.service.DwdBranchDirectorPostRecordDfpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;
import static cn.hutool.core.date.DatePattern.PURE_DATE_PATTERN;
import static com.mpolicy.settlement.core.modules.autocost.enums.HrJobPostEnum.*;

/**
 * 主任职位记录
 *
 * <AUTHOR>
 * @since 2024-06-07 3:34:32
 */
@Slf4j
@Service("dwdBranchDirectorPostRecordDfpService")
public class DwdBranchDirectorPostRecordDfpServiceImpl extends ServiceImpl<DwdBranchDirectorPostRecordDfpDao, DwdBranchDirectorPostRecordDfpEntity> implements DwdBranchDirectorPostRecordDfpService {
    /**
     * 获取机构主任、副主任
     * @param generateCycle
     * @return
     */
    public List<BranchDirectorPostRecordDto> listBranchDirectorPostRecord(String generateCycle){
        List<DwdBranchDirectorPostRecordDfpEntity> postRecordList = this.lambdaQuery()
                .eq(DwdBranchDirectorPostRecordDfpEntity::getPt,generateCycle)
                .in(DwdBranchDirectorPostRecordDfpEntity::getJobId, Arrays.asList(DIRECTOR.getCode(), DEPUTY_DIRECTOR.getCode()))
                .in(DwdBranchDirectorPostRecordDfpEntity::getPostStatusCode,Arrays.asList(0,1))
                .list();
        if(CollectionUtils.isEmpty(postRecordList)){
            return Collections.EMPTY_LIST;
        }
        return postRecordList.stream().map(o->{
            BranchDirectorPostRecordDto dto = new BranchDirectorPostRecordDto();
            BeanUtils.copyProperties(o, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 获取机构领导任职记录（主任、副主任、管理顾问、主任助理）
     * @param generateCycle
     * @return
     */
    public List<BranchDirectorPostRecordDto> listBranchLeaderPostRecord(String generateCycle, Date firstDayOfMonth){
        List<DwdBranchDirectorPostRecordDfpEntity> postRecordList = this.lambdaQuery()
                .eq(DwdBranchDirectorPostRecordDfpEntity::getPt,generateCycle)
                .ge(DwdBranchDirectorPostRecordDfpEntity::getPostEndDate,DateUtil.format(firstDayOfMonth,NORM_DATE_PATTERN))
                .in(DwdBranchDirectorPostRecordDfpEntity::getJobId, Arrays.asList(DIRECTOR.getCode(),
                        DEPUTY_DIRECTOR.getCode(),
                        MANAGEMENT_CONSULTANT.getCode(),
                        DIRECTOR_ASSISTANT.getCode()
                        ))
                .in(DwdBranchDirectorPostRecordDfpEntity::getPostStatusCode,Arrays.asList(0,1))
                .list();
        if(CollectionUtils.isEmpty(postRecordList)){
            return Collections.EMPTY_LIST;
        }
        return postRecordList.stream().map(o->{
            BranchDirectorPostRecordDto dto = new BranchDirectorPostRecordDto();
            BeanUtils.copyProperties(o, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 获取机构生服对接人
     * @param generateCycle
     * @return
     */
    public List<BranchDirectorPostRecordDto> listLifeServicerPostRecord(String generateCycle,Date firstDayOfMonth){
        List<DwdBranchDirectorPostRecordDfpEntity> postRecordList = this.lambdaQuery()
                .eq(DwdBranchDirectorPostRecordDfpEntity::getPt,generateCycle)
                .eq(DwdBranchDirectorPostRecordDfpEntity::getJobId, PCO.getCode())
                .ge(DwdBranchDirectorPostRecordDfpEntity::getPostEndDate, DateUtil.format(firstDayOfMonth,NORM_DATE_PATTERN))
                .in(DwdBranchDirectorPostRecordDfpEntity::getPostStatusCode,Arrays.asList(0,1))
                .list();
        if(CollectionUtils.isEmpty(postRecordList)){
            return Collections.EMPTY_LIST;
        }
        return postRecordList.stream().map(o->{
            BranchDirectorPostRecordDto dto = new BranchDirectorPostRecordDto();
            BeanUtils.copyProperties(o, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    public List<BranchDirectorPostRecordDto> listPostRecordByCycleAndBchCodes(String cycle,List<String> bchCodes){
        List<DwdBranchDirectorPostRecordDfpEntity> postRecordList = this.baseMapper.listPostRecordByCycleAndBchCodes(cycle,bchCodes);
        if(CollectionUtils.isEmpty(postRecordList)){
            return Collections.EMPTY_LIST;
        }
        return postRecordList.stream().map(o->{
            BranchDirectorPostRecordDto dto = new BranchDirectorPostRecordDto();
            BeanUtils.copyProperties(o, dto);
            return dto;
        }).collect(Collectors.toList());
    }
}
