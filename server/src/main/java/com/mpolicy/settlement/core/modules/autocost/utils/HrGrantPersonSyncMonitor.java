package com.mpolicy.settlement.core.modules.autocost.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Hr发放人员同步性能监控工具
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Component
public class HrGrantPersonSyncMonitor {

    private final ConcurrentHashMap<String, Long> startTimes = new ConcurrentHashMap<>();
    private final AtomicLong totalSyncCount = new AtomicLong(0);
    private final AtomicLong totalSyncTime = new AtomicLong(0);

    /**
     * 开始监控
     */
    public void startMonitor(String operationKey) {
        startTimes.put(operationKey, System.currentTimeMillis());
        log.debug("开始监控操作：{}", operationKey);
    }

    /**
     * 结束监控
     */
    public long endMonitor(String operationKey) {
        Long startTime = startTimes.remove(operationKey);
        if (startTime == null) {
            log.warn("未找到操作的开始时间：{}", operationKey);
            return 0;
        }

        long duration = System.currentTimeMillis() - startTime;
        log.info("操作完成：{}，耗时：{} ms", operationKey, duration);

        // 更新统计信息
        totalSyncCount.incrementAndGet();
        totalSyncTime.addAndGet(duration);

        return duration;
    }

    /**
     * 监控数据库查询
     */
    public void monitorQuery(String queryType, int resultCount, long duration) {
        log.info("数据库查询 - 类型：{}，结果数量：{}，耗时：{} ms", queryType, resultCount, duration);
        
        if (duration > 5000) { // 超过5秒的查询记录警告
            log.warn("慢查询警告 - 类型：{}，耗时：{} ms", queryType, duration);
        }
    }

    /**
     * 监控批量操作
     */
    public void monitorBatchOperation(String operationType, int batchSize, long duration) {
        log.info("批量操作 - 类型：{}，批次大小：{}，耗时：{} ms", operationType, batchSize, duration);
        
        if (batchSize > 0) {
            double avgTimePerRecord = (double) duration / batchSize;
            log.debug("平均每条记录处理时间：{} ms", String.format("%.2f", avgTimePerRecord));
        }
    }

    /**
     * 获取平均同步时间
     */
    public double getAverageSyncTime() {
        long count = totalSyncCount.get();
        if (count == 0) {
            return 0;
        }
        return (double) totalSyncTime.get() / count;
    }

    /**
     * 获取总同步次数
     */
    public long getTotalSyncCount() {
        return totalSyncCount.get();
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        totalSyncCount.set(0);
        totalSyncTime.set(0);
        startTimes.clear();
        log.info("性能监控统计信息已重置");
    }

    /**
     * 打印统计报告
     */
    public void printStatisticsReport() {
        long count = getTotalSyncCount();
        double avgTime = getAverageSyncTime();
        
        log.info("=== Hr发放人员同步性能统计报告 ===");
        log.info("总同步次数：{}", count);
        log.info("平均同步时间：{} ms", String.format("%.2f", avgTime));
        log.info("总耗时：{} ms", totalSyncTime.get());
        log.info("================================");
    }
}
