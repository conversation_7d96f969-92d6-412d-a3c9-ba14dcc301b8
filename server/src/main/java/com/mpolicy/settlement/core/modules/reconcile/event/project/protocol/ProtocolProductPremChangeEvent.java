package com.mpolicy.settlement.core.modules.reconcile.event.project.protocol;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.common.AbsSettlementEvent;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventCheckResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventData;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementPolicyInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 协议费率表产品费率变更
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProtocolProductPremChangeEvent extends AbsSettlementEvent {

    @Autowired
    private SettlementPolicyInfoService settlementPolicyInfoService;

    @Override
    public SettlementEventTypeEnum handlerEventType() {
        return SettlementEventTypeEnum.PROTOCOL_PRODUCT_PREM_CHANGE;
    }

    @Override
    public HandleEventCheckResult handleIncomeEventCheck(SettlementEventJobEntity eventJob, Integer reconcileType) {
        if (reconcileType != 0) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg("协议变更不影响合约").build();
        }
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleIncomeEvent(SettlementEventJobEntity eventJob, HandleEventData handleEventData) {
        log.info("协议费率表产品费率变更推送标识={}触发事件参数={}", eventJob.getPushEventCode(),JSONUtil.toJsonStr(eventJob));
        if (StrUtil.isNotBlank(eventJob.getEventRequest()) && JSONUtil.isJson(eventJob.getEventRequest())) {
            JSONObject eventRequest = JSONUtil.parseObj(eventJob.getEventRequest());
            List<String> removePremCodeList = Optional.ofNullable(eventRequest.getJSONArray("removePremCodeList")).orElse(new cn.hutool.json.JSONArray()).toList(String.class);
            List<String> updatePremCodeList = Optional.ofNullable(eventRequest.getJSONArray("updatePremCodeList")).orElse(new cn.hutool.json.JSONArray()).toList(String.class);
            List<String> insertPremCodeList = Optional.ofNullable(eventRequest.getJSONArray("insertPremCodeList")).orElse(new JSONArray()).toList(String.class);
            // 更新未结算保单的费率信息
        }
        return "success";
    }
    @Override
    public HandleEventCheckResult handleCostEventCheck(SettlementEventJobEntity eventJob) {
        return HandleEventCheckResult.builder().checkStatus(false).checkMsg("协议/合约费率表变更不影响支出端").build();
    }

    @Override
    public String handleCostEvent(SettlementEventJobEntity eventJob, HandleEventData handleEventData) {
        // TODO: 2023/8/6
        return "success";
    }
}