package com.mpolicy.settlement.core.modules.autocost.dao;

import com.mpolicy.service.common.mapper.ImsBaseMapper;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostDynamicSubjectErrorDataEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostSubjectDataDynamicInfoEntity;

/**
 * 动态科目导入异常数据
 * 
 * <AUTHOR>
 * @since 2024-11-27 20:42:06
 */
public interface SettlementCostDynamicSubjectErrorDataDao extends ImsBaseMapper<SettlementCostDynamicSubjectErrorDataEntity> {
	
}
