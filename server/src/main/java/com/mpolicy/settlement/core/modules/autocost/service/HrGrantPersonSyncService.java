package com.mpolicy.settlement.core.modules.autocost.service;

/**
 * Hr发放人员同步服务接口
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface HrGrantPersonSyncService {

    /**
     * 同步Hr发放人员信息
     * 根据结算月份从员工任职记录表中获取符合条件的员工数据并入库
     *
     * @param settlementCycle 结算周期（格式：YYYY-MM-DD，如：2025-06-30）
     * @param forceOverride 是否强制覆盖已存在的数据
     * @return 同步成功的记录数
     */
    int syncHrGrantPersonInfo(String settlementCycle, boolean forceOverride);

    /**
     * 根据结算周期删除Hr发放人员信息
     *
     * @param settlementCycle 结算周期
     * @return 删除的记录数
     */
    int deleteBySettlementCycle(String settlementCycle);
}
