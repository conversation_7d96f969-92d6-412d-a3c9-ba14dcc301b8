package com.mpolicy.settlement.core.modules.autocost.service;

/**
 * Hr发放人员同步服务接口
 *
 * 主要功能：
 * 1. 从员工任职记录表(hr_posting_record_ldom)中查询符合条件的员工
 * 2. 将符合条件的员工数据同步到Hr发放人员表(settlement_cost_hr_grant_person_info)
 * 3. 支持强制覆盖和增量同步两种模式
 * 4. 提供数据清理功能
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface HrGrantPersonSyncService {

    /**
     * 同步Hr发放人员信息
     *
     * 业务规则：
     * 1. 查询上月月末仍在职的员工（posting_status=0, posting_end_date>=上月最后一天）
     * 2. 查询当月离职的员工（posting_status=1, last_work_date在当月范围内）
     * 3. 只包含普通员工(employ_type=0)和返聘员工(employ_type=3)
     * 4. 自动去重处理（同一工号在同一结算周期只保留一条记录）
     * 5. 默认设置hr_grant_flag=1（hr发放）
     *
     * @param settlementCycle 结算周期（格式：YYYYMM，如：202506）
     * @param forceOverride 是否强制覆盖已存在的数据
     * @return 同步成功的记录数
     * @throws IllegalArgumentException 当结算周期格式错误时
     * @throws RuntimeException 当同步过程中发生异常时
     */
    int syncHrGrantPersonInfo(String settlementCycle, boolean forceOverride);

    /**
     * 根据结算周期删除Hr发放人员信息
     *
     * 注意：此方法执行逻辑删除，不会物理删除数据
     *
     * @param settlementCycle 结算周期（格式：YYYYMM）
     * @return 删除的记录数
     */
    int deleteBySettlementCycle(String settlementCycle);
}
