package com.mpolicy.settlement.core.modules.autocost.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.service.common.service.DicCacheHelper;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.*;
import com.mpolicy.settlement.core.modules.autocost.entity.*;
import com.mpolicy.settlement.core.modules.autocost.service.*;
import com.mpolicy.settlement.core.modules.autocost.utils.AutoCostUtils;
import com.mpolicy.settlement.core.service.common.ProductBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 中和农信员工服务接口实现
 * https://cfpamf.yuque.com/xg4y3f/voxwu3/fgzub8uka17isqv6
 *
 * <AUTHOR>
 * @since 2023-10-31 22:12
 */
@Slf4j
@Service
public class SettlementCostQbiServiceImpl implements SettlementCostQbiService {


    @Autowired
    private ProductBaseService productBaseService;

    @Autowired
    private SettlementCostQbiEmployeeService settlementCostQbiEmployeeService;

    @Autowired
    private SettlementCostQbiEmployeeProductService settlementCostQbiEmployeeProductService;

    @Autowired
    private SettlementCostQbiSupervisorService settlementCostQbiSupervisorService;

    @Autowired
    private SettlementCostQbiSupervisorProductService settlementCostQbiSupervisorProductService;

    @Autowired
    private SettlementCostQbiOrgService settlementCostQbiOrgService;

    @Autowired
    private SettlementCostQbiOrgProductService settlementCostQbiOrgProductService;

    @Autowired
    private SettlementCostQbiEmployeeRenewalRateService settlementCostQbiEmployeeRenewalRateService;

    @Autowired
    private SettlementCostQbiSupervisorRenewalRateService settlementCostQbiSupervisorRenewalRateService;

    @Autowired
    private SettlementCostQbiOrgRenewalRateService settlementCostQbiOrgRenewalRateService;

    /**
     * 获取快照周期月份
     *
     * @param monthSnapshot 快照周期
     * @return 快照月份 yyyyMMdd
     * <AUTHOR>
     * @since 2023/11/18 11:09
     */
    private Integer getSnapshotMonth(String monthSnapshot) {
        // 特殊处理快照pt
        if(StringUtils.equals("202310",monthSnapshot)){
            return 20231031;
        }
        if(StringUtils.equals("202311",monthSnapshot)){
            return 20231130;
        }
        if(StringUtils.equals("202312",monthSnapshot)){
            return 20231231;
        }
        if(StringUtils.equals("202401",monthSnapshot)){
            return 20240131;
        }
        if(StringUtils.equals("202402",monthSnapshot)){
            return 20240229;
        }
        if(StringUtils.equals("202403",monthSnapshot)){
            return 20240331;
        }
        if(StringUtils.equals("202404",monthSnapshot)){
            return 20240430;
        }
        // 将日期对象格式化为输出格式的字符串
        String result = DateUtil.format(DateUtil.offsetDay(new Date(), -1), "yyyyMMdd");
        // 将结果转换为整数类型
        return Integer.parseInt(result);
    }

    @Override
    public EmployeeQbiInfo queryEmployeeQbi(String employeeCode, String monthSnapshot) {
        // 1 获取员工的指标信息
        SettlementCostQbiEmployeeEntity employeeQbi = Optional.ofNullable(
                settlementCostQbiEmployeeService.lambdaQuery()
                        .eq(SettlementCostQbiEmployeeEntity::getEmployeeCode, employeeCode)
                        .eq(SettlementCostQbiEmployeeEntity::getPt, getSnapshotMonth(monthSnapshot))
                        .last(" limit 1")
                        .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("员工编号={}指标信息不存在", employeeCode))));

        // 2 设置返回信息
        EmployeeQbiInfo result = new EmployeeQbiInfo();
        BeanUtils.copyProperties(employeeQbi, result);
        //续期率为空判断
        result.setRenewalRate(renewalRateIsNull(employeeQbi.getPayAmt())?null:employeeQbi.getRenewalRate());
        return result;
    }

    @Override
    public List<EmployeeQbiInfo> listEmployeeQbi(List<String> employeeCodeList, String monthSnapshot) {
        // 1 获取集合员工指标信息
        List<SettlementCostQbiEmployeeEntity> list = settlementCostQbiEmployeeService.lambdaQuery()
                .in(SettlementCostQbiEmployeeEntity::getEmployeeCode, employeeCodeList)
                .eq(SettlementCostQbiEmployeeEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();

        // 2 构建返回员工指标集合
        return list.stream().map(x -> {
            EmployeeQbiInfo bean = new EmployeeQbiInfo();
            BeanUtils.copyProperties(x, bean);
            bean.setRenewalRate(renewalRateIsNull(x.getPayAmt())?null:x.getRenewalRate());
            return bean;
        }).collect(Collectors.toList());
    }

    /**
     * 注意 ：如果员工在多个机构任职，且机构指标数据不一致的情况下（不可用）
     * @param employeeCodeList 员工编码集合
     * @param monthSnapshot    快照月份[yyyyMM]
     * @return
     */
    @Override
    public Map<String, EmployeeQbiInfo> mapEmployeeQbi(List<String> employeeCodeList, String monthSnapshot) {
        List<EmployeeQbiInfo> employeeQbiList = this.listEmployeeQbi(employeeCodeList, monthSnapshot);
        return employeeQbiList.stream().collect(Collectors.toMap(EmployeeQbiInfo::getEmployeeCode, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public List<EmployeeProductQbiInfo> listEmployeeProductQbi(String employeeCode, String monthSnapshot) {
        // 1 获取集合员工指标信息
        List<SettlementCostQbiEmployeeProductEntity> list = settlementCostQbiEmployeeProductService.lambdaQuery()
                .eq(SettlementCostQbiEmployeeProductEntity::getEmployeeCode, employeeCode)
                .eq(SettlementCostQbiEmployeeProductEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();
        if (list.isEmpty()) {
            return Collections.emptyList();
        }
        // 2 获取险种map 信息
        Map<String, ProductBase> productMap = productBaseService.mapProductBase(list.stream().map(SettlementCostQbiEmployeeProductEntity::getProductCode).distinct().collect(Collectors.toList()));
        // 3 构建返回员工险种指标集合
        return list.stream().map(x -> {
            EmployeeProductQbiInfo bean = new EmployeeProductQbiInfo();
            BeanUtils.copyProperties(x, bean);
            if (productMap.containsKey(bean.getProductCode())) {
                ProductBase productBase = productMap.get(bean.getProductCode());
                bean.setProductGroup(productBase.getProductGroup());
                bean.setLongShortFlag(productBase.getLongShortFlag());
                bean.setProductType(productBase.getProductType());
                bean.setLevel2Code(productBase.getLevel2Code());
                bean.setLevel3Code(productBase.getLevel3Code());
                bean.setLevel2Name(DicCacheHelper.getValue(productBase.getLevel2Code()));
                bean.setLevel3Name(DicCacheHelper.getValue(productBase.getLevel3Code()));
            }
            return bean;
        }).collect(Collectors.toList());
    }

    @Override
    public List<EmployeeProductQbiInfo> listEmployeeProductQbi(List<String> employeeCodeList, String monthSnapshot) {
        // 1 获取集合员工指标信息
        List<SettlementCostQbiEmployeeProductEntity> list = settlementCostQbiEmployeeProductService.lambdaQuery()
                .in(SettlementCostQbiEmployeeProductEntity::getEmployeeCode, employeeCodeList)
                .eq(SettlementCostQbiEmployeeProductEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();

        // 2 获取险种map 信息
        Map<String, ProductBase> productMap = productBaseService.mapProductBase(list.stream().map(SettlementCostQbiEmployeeProductEntity::getProductCode).distinct().collect(Collectors.toList()));

        // 3 构建返回员工险种指标集合
        return list.stream().map(x -> {
            EmployeeProductQbiInfo bean = new EmployeeProductQbiInfo();
            BeanUtils.copyProperties(x, bean);
            if (productMap.containsKey(bean.getProductCode())) {
                ProductBase productBase = productMap.get(bean.getProductCode());
                bean.setProductGroup(productBase.getProductGroup());
                bean.setLongShortFlag(productBase.getLongShortFlag());
                bean.setProductType(productBase.getProductType());
                bean.setLevel2Code(productBase.getLevel2Code());
                bean.setLevel3Code(productBase.getLevel3Code());
                bean.setLevel2Name(DicCacheHelper.getValue(productBase.getLevel2Code()));
                bean.setLevel3Name(DicCacheHelper.getValue(productBase.getLevel3Code()));
            }
            return bean;
        }).collect(Collectors.toList());
    }

    @Override
    public SupervisorQbiInfo querySupervisorQbiInfo(String supervisorEmployeeCode, String monthSnapshot) {
        // 1 获取督导员工的指标信息
        SettlementCostQbiSupervisorEntity employeeSupervisorQbi = Optional.ofNullable(
                settlementCostQbiSupervisorService.lambdaQuery()
                        .eq(SettlementCostQbiSupervisorEntity::getEmployeeCode, supervisorEmployeeCode)
                        .eq(SettlementCostQbiSupervisorEntity::getPt, getSnapshotMonth(monthSnapshot))
                        .last(" limit 1")
                        .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("督导员工编号={}指标信息不存在", supervisorEmployeeCode))));

        // 2 设置返回信息
        SupervisorQbiInfo result = new SupervisorQbiInfo();
        BeanUtils.copyProperties(employeeSupervisorQbi, result);
        result.setRenewalRate(renewalRateIsNull(employeeSupervisorQbi.getPayAmt())?null:employeeSupervisorQbi.getRenewalRate());
        return result;
    }



    @Override
    public List<SupervisorQbiInfo> listSupervisorQbiInfo(List<String> supervisorEmployeeCodeList, String monthSnapshot) {
        // 1 获取集合督导员工指标信息
        List<SettlementCostQbiSupervisorEntity> list = settlementCostQbiSupervisorService.lambdaQuery()
                .in(SettlementCostQbiSupervisorEntity::getEmployeeCode, supervisorEmployeeCodeList)
                .eq(SettlementCostQbiSupervisorEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();

        // 2 构建返回督导员工险种指标集合
        return list.stream().map(x -> {
            SupervisorQbiInfo bean = new SupervisorQbiInfo();
            BeanUtils.copyProperties(x, bean);
            bean.setRenewalRate(renewalRateIsNull(x.getPayAmt())?null:x.getRenewalRate());
            return bean;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<String, SupervisorQbiInfo> mapSupervisorQbiInfo(List<String> supervisorEmployeeCodeList, String monthSnapshot) {
        List<SupervisorQbiInfo> employeeQbiList = this.listSupervisorQbiInfo(supervisorEmployeeCodeList, monthSnapshot);
        return employeeQbiList.stream().collect(Collectors.toMap(SupervisorQbiInfo::getEmployeeCode, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public List<SupervisorProductQbiInfo> listSupervisorProductQbi(String supervisorEmployeeCode, String monthSnapshot) {
        // 1 获取集合员工指标信息
        List<SettlementCostQbiSupervisorProductEntity> list = settlementCostQbiSupervisorProductService.lambdaQuery()
                .eq(SettlementCostQbiSupervisorProductEntity::getEmployeeCode, supervisorEmployeeCode)
                .eq(SettlementCostQbiSupervisorProductEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();

        // 2 获取险种map 信息
        Map<String, ProductBase> productMap = productBaseService.mapProductBase(list.stream().map(SettlementCostQbiSupervisorProductEntity::getProductCode).distinct().collect(Collectors.toList()));

        // 3 构建返回员工险种指标集合
        return list.stream().map(x -> {
            SupervisorProductQbiInfo bean = new SupervisorProductQbiInfo();
            BeanUtils.copyProperties(x, bean);
            if (productMap.containsKey(bean.getProductCode())) {
                ProductBase productBase = productMap.get(bean.getProductCode());
                bean.setProductGroup(productBase.getProductGroup());
                bean.setLongShortFlag(productBase.getLongShortFlag());
                bean.setProductType(productBase.getProductType());
                bean.setLevel2Code(productBase.getLevel2Code());
                bean.setLevel3Code(productBase.getLevel3Code());
                bean.setLevel2Name(DicCacheHelper.getValue(productBase.getLevel2Code()));
                bean.setLevel3Name(DicCacheHelper.getValue(productBase.getLevel3Code()));
            }
            return bean;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SupervisorProductQbiInfo> listSupervisorProductQbi(List<String> supervisorEmployeeCodeList, String monthSnapshot) {
        // 1 获取集合督导员工险种指标信息
        List<SettlementCostQbiSupervisorProductEntity> list = settlementCostQbiSupervisorProductService.lambdaQuery()
                .in(SettlementCostQbiSupervisorProductEntity::getEmployeeCode, supervisorEmployeeCodeList)
                .eq(SettlementCostQbiSupervisorProductEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();

        // 2 获取险种map 信息
        Map<String, ProductBase> productMap = productBaseService.mapProductBase(list.stream().map(SettlementCostQbiSupervisorProductEntity::getProductCode).distinct().collect(Collectors.toList()));
        // 3 构建返回
        return list.stream().map(x -> {
            SupervisorProductQbiInfo bean = new SupervisorProductQbiInfo();
            BeanUtils.copyProperties(x, bean);
            if (productMap.containsKey(bean.getProductCode())) {
                ProductBase productBase = productMap.get(bean.getProductCode());
                bean.setProductGroup(productBase.getProductGroup());
                bean.setLongShortFlag(productBase.getLongShortFlag());
                bean.setProductType(productBase.getProductType());
                bean.setLevel2Code(productBase.getLevel2Code());
                bean.setLevel3Code(productBase.getLevel3Code());
                bean.setLevel2Name(DicCacheHelper.getValue(productBase.getLevel2Code()));
                bean.setLevel3Name(DicCacheHelper.getValue(productBase.getLevel3Code()));
            }
            return bean;
        }).collect(Collectors.toList());
    }


    @Override
    public OrgQbiInfo queryOrgQbi(String orgCode, String monthSnapshot) {
        // 1 获取机构险种指标信息
        SettlementCostQbiOrgEntity rogQbi = Optional.ofNullable(
                settlementCostQbiOrgService.lambdaQuery()
                        .eq(SettlementCostQbiOrgEntity::getOrgCode, orgCode)
                        .eq(SettlementCostQbiOrgEntity::getPt, getSnapshotMonth(monthSnapshot))
                        .last(" limit 1")
                        .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("机构={}指标信息不存在", orgCode))));

        // 2 设置返回信息
        OrgQbiInfo result = new OrgQbiInfo();
        BeanUtils.copyProperties(rogQbi, result);
        result.setRenewalRate(renewalRateIsNull(rogQbi.getPayAmt())?null:rogQbi.getRenewalRate());
        result.setSmRenewalRate(rogQbi.getSmRenewalRate());

        return result;
    }

    @Override
    public List<OrgQbiInfo> listOrgQbi(List<String> orgCodeList, String monthSnapshot) {
        // 1 获取机构险种指标信息
        List<SettlementCostQbiOrgEntity> list = settlementCostQbiOrgService.lambdaQuery()
                .in(SettlementCostQbiOrgEntity::getOrgCode, orgCodeList)
                .eq(SettlementCostQbiOrgEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();

        // 2 设置返回信息
        return list.stream().map(x -> {
            OrgQbiInfo bean = new OrgQbiInfo();
            BeanUtils.copyProperties(x, bean);
            bean.setRenewalRate(renewalRateIsNull(x.getPayAmt())?null:x.getRenewalRate());
            bean.setSmRenewalRate(x.getSmRenewalRate());

            return bean;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<String, OrgQbiInfo> mapOrgQbi(List<String> orgCodeList, String monthSnapshot) {
        if(orgCodeList == null || orgCodeList.isEmpty()){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("分支机构编码集合为空，无法查询机构指标信息"));
        }
        List<OrgQbiInfo> orgQbiList = this.listOrgQbi(orgCodeList, monthSnapshot);
        return orgQbiList.stream().collect(Collectors.toMap(OrgQbiInfo::getOrgCode, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public List<OrgProductQbiInfo> listOrgProductQbi(String orgCode, String monthSnapshot) {
        // 1 获取机构险种指标信息
        List<SettlementCostQbiOrgProductEntity> list = settlementCostQbiOrgProductService.lambdaQuery()
                .eq(SettlementCostQbiOrgProductEntity::getOrgCode, orgCode)
                .eq(SettlementCostQbiOrgProductEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();

        // 2 获取险种map 信息
        Map<String, ProductBase> productMap = productBaseService.mapProductBase(list.stream().map(SettlementCostQbiOrgProductEntity::getProductCode).distinct().collect(Collectors.toList()));

        // 3 设置返回信息
        return list.stream().map(x -> {
            OrgProductQbiInfo bean = new OrgProductQbiInfo();
            BeanUtils.copyProperties(x, bean);
            if (productMap.containsKey(bean.getProductCode())) {
                ProductBase productBase = productMap.get(bean.getProductCode());
                bean.setProductGroup(productBase.getProductGroup());
                bean.setLongShortFlag(productBase.getLongShortFlag());
                bean.setProductType(productBase.getProductType());
                bean.setLevel2Code(productBase.getLevel2Code());
                bean.setLevel3Code(productBase.getLevel3Code());
                bean.setLevel2Name(DicCacheHelper.getValue(productBase.getLevel2Code()));
                bean.setLevel3Name(DicCacheHelper.getValue(productBase.getLevel3Code()));
            }
            return bean;
        }).collect(Collectors.toList());
    }

    /**
     * 获取qbi机构险种维度费用相关字段
     * @param orgCodeList
     * @param monthSnapshot
     * @return
     */
    @Override
    public List<OrgProductQbiInfo> listOrgProductQbiCostField(List<String> orgCodeList, String monthSnapshot){
        // 1 获取机构险种指标信息
        List<SettlementCostQbiOrgProductEntity> list = settlementCostQbiOrgProductService.lambdaQuery()
                .in(SettlementCostQbiOrgProductEntity::getOrgCode, orgCodeList)
                .eq(SettlementCostQbiOrgProductEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();
        // 3 设置返回信息
        return list.stream().map(x -> {
            OrgProductQbiInfo bean = new OrgProductQbiInfo();
            BeanUtils.copyProperties(x, bean);
            return bean;
        }).collect(Collectors.toList());

    }

    @Override
    public List<OrgProductQbiInfo> listOrgProductQbi(List<String> orgCodeList, String monthSnapshot) {
        // 1 获取机构险种指标信息
        List<SettlementCostQbiOrgProductEntity> list = settlementCostQbiOrgProductService.lambdaQuery()
                .in(SettlementCostQbiOrgProductEntity::getOrgCode, orgCodeList)
                .eq(SettlementCostQbiOrgProductEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();
        // 2 获取险种map 信息
        Map<String, ProductBase> productMap = productBaseService.mapProductBase(list.stream().map(SettlementCostQbiOrgProductEntity::getProductCode).distinct().collect(Collectors.toList()));

        // 3 设置返回信息
        return list.stream().map(x -> {
            OrgProductQbiInfo bean = new OrgProductQbiInfo();
            BeanUtils.copyProperties(x, bean);
            if (productMap.containsKey(bean.getProductCode())) {
                ProductBase productBase = productMap.get(bean.getProductCode());
                bean.setProductGroup(productBase.getProductGroup());
                bean.setLongShortFlag(productBase.getLongShortFlag());
                bean.setProductType(productBase.getProductType());
                bean.setLevel2Code(productBase.getLevel2Code());
                bean.setLevel3Code(productBase.getLevel3Code());
                bean.setLevel2Name(DicCacheHelper.getValue(productBase.getLevel2Code()));
                bean.setLevel3Name(DicCacheHelper.getValue(productBase.getLevel3Code()));
            }
            return bean;
        }).collect(Collectors.toList());
    }

    @Override
    public EmployeeQbiRenewalRate queryEmployeeQbiRenewalRate(String employeeCode, String bizMonth, String monthSnapshot) {
        // 1 获取员工的继续率指标信息
        SettlementCostQbiEmployeeRenewalRateEntity employeeQbi = Optional.ofNullable(
                settlementCostQbiEmployeeRenewalRateService.lambdaQuery()
                        .eq(SettlementCostQbiEmployeeRenewalRateEntity::getEmployeeCode, employeeCode)
                        .eq(SettlementCostQbiEmployeeRenewalRateEntity::getBizMonth, AutoCostUtils.yyyyMMToyyyyMM(bizMonth))
                        .eq(SettlementCostQbiEmployeeRenewalRateEntity::getPt, getSnapshotMonth(monthSnapshot))
                        .last(" limit 1")
                        .one()
        ).orElse(null);
        // 如果不存在则直接返回null
        if(employeeQbi == null){
            return null;
        }
        // 2 设置返回信息
        EmployeeQbiRenewalRate result = new EmployeeQbiRenewalRate();
        BeanUtils.copyProperties(employeeQbi, result);
        result.setRenewalRate(renewalRateIsNull(employeeQbi.getPayAmt())?null:employeeQbi.getRenewalRate());
        return result;
    }

    @Override
    public Map<String, EmployeeQbiRenewalRate> mapEmployeeQbiRenewalRate(List<String> employeeCodeList, String bizMonth, String monthSnapshot) {
        // 1 获取集合
        List<SettlementCostQbiEmployeeRenewalRateEntity> list = settlementCostQbiEmployeeRenewalRateService.lambdaQuery()
                .in(SettlementCostQbiEmployeeRenewalRateEntity::getEmployeeCode, employeeCodeList)
                .eq(SettlementCostQbiEmployeeRenewalRateEntity::getBizMonth, AutoCostUtils.yyyyMMToyyyyMM(bizMonth))
                .eq(SettlementCostQbiEmployeeRenewalRateEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();

        // 2 构建vo集合
        List<EmployeeQbiRenewalRate> qbiList = list.stream().map(x -> {
            EmployeeQbiRenewalRate bean = new EmployeeQbiRenewalRate();
            BeanUtils.copyProperties(x, bean);
            bean.setRenewalRate(renewalRateIsNull(x.getPayAmt())?null:x.getRenewalRate());
            return bean;
        }).collect(Collectors.toList());
        return qbiList.stream().collect(Collectors.toMap(EmployeeQbiRenewalRate::getEmployeeCode, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public List<EmployeeQbiRenewalRate> listEmployeeQbiRenewalRate(String employeeCode, String monthSnapshot) {
        // 1 获取员工的继续率指标信息
        List<SettlementCostQbiEmployeeRenewalRateEntity> list = settlementCostQbiEmployeeRenewalRateService.lambdaQuery()
                .eq(SettlementCostQbiEmployeeRenewalRateEntity::getEmployeeCode, employeeCode)
                .eq(SettlementCostQbiEmployeeRenewalRateEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();

        // 2 构建返回员工继续率指标集合
        return list.stream().map(x -> {
            EmployeeQbiRenewalRate bean = new EmployeeQbiRenewalRate();
            BeanUtils.copyProperties(x, bean);
            bean.setRenewalRate(renewalRateIsNull(x.getPayAmt())?null:x.getRenewalRate());
            return bean;
        }).collect(Collectors.toList());
    }

    @Override
    public List<EmployeeQbiRenewalRate> listEmployeeQbiRenewalRate(List<String> employeeCodeList, String bizMonth, String monthSnapshot) {
        // 1 获取员工的继续率指标信息
        List<SettlementCostQbiEmployeeRenewalRateEntity> list = settlementCostQbiEmployeeRenewalRateService.lambdaQuery()
                .in(SettlementCostQbiEmployeeRenewalRateEntity::getEmployeeCode, employeeCodeList)
                .eq(SettlementCostQbiEmployeeRenewalRateEntity::getBizMonth, AutoCostUtils.yyyyMMToyyyyMM(bizMonth))
                .eq(SettlementCostQbiEmployeeRenewalRateEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();

        // 2 构建返回员工继续率指标集合
        return list.stream().map(x -> {
            EmployeeQbiRenewalRate bean = new EmployeeQbiRenewalRate();
            BeanUtils.copyProperties(x, bean);
            bean.setRenewalRate(renewalRateIsNull(x.getPayAmt())?null:x.getRenewalRate());


            return bean;
        }).collect(Collectors.toList());
    }

    @Override
    public List<EmployeeQbiRenewalRate> listEmployeeQbiRenewalRate(List<String> employeeCodeList, List<String> bizMonthList, String monthSnapshot) {
        // 1 获取员工的继续率指标信息
        List<SettlementCostQbiEmployeeRenewalRateEntity> list = settlementCostQbiEmployeeRenewalRateService.lambdaQuery()
                .in(SettlementCostQbiEmployeeRenewalRateEntity::getEmployeeCode, employeeCodeList)
                .in(SettlementCostQbiEmployeeRenewalRateEntity::getBizMonth, bizMonthList)
                .eq(SettlementCostQbiEmployeeRenewalRateEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();

        // 2 构建返回员工继续率指标集合
        return list.stream().map(x -> {
            EmployeeQbiRenewalRate bean = new EmployeeQbiRenewalRate();
            BeanUtils.copyProperties(x, bean);
            bean.setRenewalRate(renewalRateIsNull(x.getPayAmt())?null:x.getRenewalRate());


            return bean;
        }).collect(Collectors.toList());
    }

    @Override
    public SupervisorQbiRenewalRate querySupervisorQbiRenewalRate(String supervisorCode, String bizMonth, String monthSnapshot) {
        // 1 获取督导的继续率指标信息
        SettlementCostQbiSupervisorRenewalRateEntity supervisorRenewalRate = Optional.ofNullable(
                settlementCostQbiSupervisorRenewalRateService.lambdaQuery()
                        .eq(SettlementCostQbiSupervisorRenewalRateEntity::getEmployeeCode, supervisorCode)
                        .eq(SettlementCostQbiSupervisorRenewalRateEntity::getBizMonth, AutoCostUtils.yyyyMMToyyyyMM(bizMonth))
                        .eq(SettlementCostQbiSupervisorRenewalRateEntity::getPt, getSnapshotMonth(monthSnapshot))
                        .last(" limit 1")
                        .one()
        ).orElse(null);

        // 如果不存在则直接返回null
        if(supervisorRenewalRate == null){
            return null;
        }

        // 2 设置返回信息
        SupervisorQbiRenewalRate result = new SupervisorQbiRenewalRate();
        BeanUtils.copyProperties(supervisorRenewalRate, result);
        result.setRenewalRate(renewalRateIsNull(supervisorRenewalRate.getPayAmt())?null:supervisorRenewalRate.getRenewalRate());
        return result;
    }

    @Override
    public Map<String, SupervisorQbiRenewalRate> mapSupervisorQbiRenewalRate(List<String> supervisorCodeList, String bizMonth, String monthSnapshot) {
        // 1 获取督导的继续率指标信息
        List<SettlementCostQbiSupervisorRenewalRateEntity> list = settlementCostQbiSupervisorRenewalRateService.lambdaQuery()
                .in(SettlementCostQbiSupervisorRenewalRateEntity::getEmployeeCode, supervisorCodeList)
                .eq(SettlementCostQbiSupervisorRenewalRateEntity::getBizMonth, AutoCostUtils.yyyyMMToyyyyMM(bizMonth))
                .eq(SettlementCostQbiSupervisorRenewalRateEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();

        // 2 构建返回督导继续率指标集合
        List<SupervisorQbiRenewalRate> qbiList = list.stream().map(x -> {
            SupervisorQbiRenewalRate bean = new SupervisorQbiRenewalRate();
            BeanUtils.copyProperties(x, bean);
            bean.setRenewalRate(renewalRateIsNull(x.getPayAmt())?null:x.getRenewalRate());
            return bean;
        }).collect(Collectors.toList());
        return qbiList.stream().collect(Collectors.toMap(SupervisorQbiRenewalRate::getEmployeeCode, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public List<SupervisorQbiRenewalRate> listSupervisorQbiRenewalRate(String supervisorCode, String monthSnapshot) {
        // 1 获取督导的继续率指标信息
        List<SettlementCostQbiSupervisorRenewalRateEntity> list = settlementCostQbiSupervisorRenewalRateService.lambdaQuery()
                .eq(SettlementCostQbiSupervisorRenewalRateEntity::getEmployeeCode, supervisorCode)
                .eq(SettlementCostQbiSupervisorRenewalRateEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();

        // 2 构建返回督导继续率指标集合
        return list.stream().map(x -> {
            SupervisorQbiRenewalRate bean = new SupervisorQbiRenewalRate();
            BeanUtils.copyProperties(x, bean);
            bean.setRenewalRate(renewalRateIsNull(x.getPayAmt())?null:x.getRenewalRate());
            return bean;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SupervisorQbiRenewalRate> listSupervisorQbiRenewalRate(List<String> supervisorCodeList, String bizMonth, String monthSnapshot) {
        // 1 获取督导的继续率指标信息
        List<SettlementCostQbiSupervisorRenewalRateEntity> list = settlementCostQbiSupervisorRenewalRateService.lambdaQuery()
                .in(SettlementCostQbiSupervisorRenewalRateEntity::getEmployeeCode, supervisorCodeList)
                .eq(SettlementCostQbiSupervisorRenewalRateEntity::getBizMonth, AutoCostUtils.yyyyMMToyyyyMM(bizMonth))
                .eq(SettlementCostQbiSupervisorRenewalRateEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();

        // 2 构建返回督导继续率指标集合
        return list.stream().map(x -> {
            SupervisorQbiRenewalRate bean = new SupervisorQbiRenewalRate();
            BeanUtils.copyProperties(x, bean);
            bean.setRenewalRate(renewalRateIsNull(x.getPayAmt())?null:x.getRenewalRate());
            return bean;
        }).collect(Collectors.toList());
    }

    @Override
    public OrgQbiRenewalRate queryOrgQbiRenewalRate(String orgCode, String bizMonth, String monthSnapshot) {
        // 1 获取机构的继续率指标信息
        SettlementCostQbiOrgRenewalRateEntity orgRenewalRate = Optional.ofNullable(
                settlementCostQbiOrgRenewalRateService.lambdaQuery()
                        .eq(SettlementCostQbiOrgRenewalRateEntity::getOrgCode, orgCode)
                        .eq(SettlementCostQbiOrgRenewalRateEntity::getBizMonth, AutoCostUtils.yyyyMMToyyyyMM(bizMonth))
                        .eq(SettlementCostQbiOrgRenewalRateEntity::getPt, getSnapshotMonth(monthSnapshot))
                        .last(" limit 1")
                        .one()
        ).orElse(null);

        // 如果不存在则直接返回null
        if(orgRenewalRate == null){
            return null;
        }

        // 2 设置返回信息
        OrgQbiRenewalRate result = new OrgQbiRenewalRate();
        BeanUtils.copyProperties(orgRenewalRate, result);
        result.setRenewalRate(renewalRateIsNull(orgRenewalRate.getPayAmt())?null:orgRenewalRate.getRenewalRate());
        return result;
    }

    @Override
    public Map<String, OrgQbiRenewalRate> mapOrgQbiRenewalRate(List<String> orgCodeList, String bizMonth, String monthSnapshot) {
        // 1 获取督导的继续率指标信息
        List<SettlementCostQbiOrgRenewalRateEntity> list = settlementCostQbiOrgRenewalRateService.lambdaQuery()
                .in(SettlementCostQbiOrgRenewalRateEntity::getOrgCode, orgCodeList)
                .eq(SettlementCostQbiOrgRenewalRateEntity::getBizMonth, AutoCostUtils.yyyyMMToyyyyMM(bizMonth))
                .eq(SettlementCostQbiOrgRenewalRateEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();

        // 2 构建返回督导继续率指标集合
        List<OrgQbiRenewalRate> qbiList = list.stream().map(x -> {
            OrgQbiRenewalRate bean = new OrgQbiRenewalRate();
            BeanUtils.copyProperties(x, bean);
            bean.setRenewalRate(renewalRateIsNull(x.getPayAmt())?null:x.getRenewalRate());
            return bean;
        }).collect(Collectors.toList());
        return qbiList.stream().collect(Collectors.toMap(OrgQbiRenewalRate::getOrgCode, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public List<OrgQbiRenewalRate> listOrgQbiRenewalRate(String orgCode, String monthSnapshot) {
        // 1 获取督导的继续率指标信息
        List<SettlementCostQbiOrgRenewalRateEntity> list = settlementCostQbiOrgRenewalRateService.lambdaQuery()
                .eq(SettlementCostQbiOrgRenewalRateEntity::getOrgCode, orgCode)
                .eq(SettlementCostQbiOrgRenewalRateEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();

        // 2 构建返回督导继续率指标集合
        return list.stream().map(x -> {
            OrgQbiRenewalRate bean = new OrgQbiRenewalRate();
            BeanUtils.copyProperties(x, bean);
            bean.setRenewalRate(renewalRateIsNull(x.getPayAmt())?null:x.getRenewalRate());
            return bean;
        }).collect(Collectors.toList());
    }

    @Override
    public List<OrgQbiRenewalRate> listOrgQbiRenewalRate(List<String> orgCodeList, String bizMonth, String monthSnapshot) {
        // 1 获取督导的继续率指标信息
        List<SettlementCostQbiOrgRenewalRateEntity> list = settlementCostQbiOrgRenewalRateService.lambdaQuery()
                .in(SettlementCostQbiOrgRenewalRateEntity::getOrgCode, orgCodeList)
                .eq(SettlementCostQbiOrgRenewalRateEntity::getBizMonth, AutoCostUtils.yyyyMMToyyyyMM(bizMonth))
                .eq(SettlementCostQbiOrgRenewalRateEntity::getPt, getSnapshotMonth(monthSnapshot))
                .list();

        // 2 构建返回督导继续率指标集合
        return list.stream().map(x -> {
            OrgQbiRenewalRate bean = new OrgQbiRenewalRate();
            BeanUtils.copyProperties(x, bean);
            bean.setRenewalRate(renewalRateIsNull(x.getPayAmt())?null:x.getRenewalRate());
            return bean;
        }).collect(Collectors.toList());
    }

    /**
     * 续期率为空判断
     * @param payAmt
     * @return
     */
    private boolean renewalRateIsNull(BigDecimal payAmt){
        return payAmt == null || BigDecimal.ZERO.compareTo(payAmt)==0;
    }
}