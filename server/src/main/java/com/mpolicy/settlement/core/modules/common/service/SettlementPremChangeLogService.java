package com.mpolicy.settlement.core.modules.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.common.entity.SettlementPremChangeLogEntity;

/**
 *
 * <AUTHOR>
 * @since 2024/08/05
 */
public interface SettlementPremChangeLogService extends IService<SettlementPremChangeLogEntity> {


    /**
     * 保存变更记录
     *
     * @param pushEventCode 推送唯一标识
     * @param premType      类型
     * @param businessCode  事件编码
     * @param data          数据体
     */
    void saveSettlementPremChangeLog(String pushEventCode, Integer premType, String businessCode, String data);

    /**
     * 保存需要发送的变更记录
     * @param pushEventCode
     * @param changeType
     * @param businessCode
     * @param dataJson
     * @param sendStatus
     */
    void saveNeedSendSettlementPremChangeLog(String pushEventCode, Integer changeType, String businessCode, String dataJson,Integer sendStatus);

    /**
     * 更新发送状态
     * @param pushEventCode
     * @param fromStatus
     * @param toStatus
     * @return
     */
    boolean updateSendStatus(String pushEventCode,Integer fromStatus,Integer toStatus);
}
