package com.mpolicy.settlement.core.modules.reconcile.dto.policy;

import com.mpolicy.policy.common.ep.policy.corrected.PolicyCoreVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel("保单变更信息")
public class PolicyCorrectDto implements Serializable {
    @ApiModelProperty("变更流水号")
    private String flowId;

    private String opType;

    @NotNull(message = "变更前信息不能为空")
    @ApiModelProperty("变更前信息")
    private PolicyCoreDto before;

    @NotNull(message = "变更后保单信息不能为空")
    @ApiModelProperty("变更后信息")
    private PolicyCoreDto corrected;
}
