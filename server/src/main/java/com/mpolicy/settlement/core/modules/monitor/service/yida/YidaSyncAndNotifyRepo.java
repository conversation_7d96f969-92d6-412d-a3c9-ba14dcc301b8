package com.mpolicy.settlement.core.modules.monitor.service.yida;

import com.mpolicy.settlement.core.modules.monitor.dto.PolicySyncFailNotifyDTO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 宜搭操作类
 * <AUTHOR>
 */
@Service
public interface YidaSyncAndNotifyRepo {

    /**
     * 通知处理人
     */
    List<String> getNotifyUserId(String errorType) throws Exception;
    /**
     * 获取通知信息
     */
    List<PolicySyncFailNotifyDTO> getNotifyInfoList();
}
