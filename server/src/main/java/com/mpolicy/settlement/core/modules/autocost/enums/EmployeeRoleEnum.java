package com.mpolicy.settlement.core.modules.autocost.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 中和农信成员岗位枚举
 *
 * <AUTHOR>
 * @version 2023/11/02
 */
public enum EmployeeRoleEnum {

    /**
     * 中和农信成员岗位枚举
     */
    ZHNX("ZHNX", "中和农信员工","1"),
    INTERNAL_AFFAIRS("INTERNAL_AFFAIRS", "内务专员","1080"),
    CUSTOMER_MANAGER("CUSTOMER_MANAGER", "客户经理","1081"),
    DIRECTOR("DIRECTOR", "主任","1083"),
    SUPERVISOR("SUPERVISOR", "分支督导","1082"),
    DIRECTOR_ASSISTANT("DIRECTOR_ASSISTANT", "主任助理","1085"),

    SUPERVISOR_AND_INTERNAL_AFFAIRS("SUPERVISOR_AND_INTERNAL_AFFAIRS", "督导兼内务","1084"),
    DEPUTY_DIRECTOR("DEPUTY_DIRECTOR", "副主任","1088"),
    PCO("PCO", "PCO","2357"),
    REGION_OPERATION_MANAGEMENT_POST("REGION_OPERATION_MANAGEMENT_POST", "区域运营管理岗","1172");


    @Getter
    private final String code;

    @Getter
    private final String name;

    @Getter
    private final String jobId;

    EmployeeRoleEnum(String code, String name,String jobId) {
        this.code = code;
        this.name = name;
        this.jobId = jobId;
    }

    /**
     * 结算对象类型枚举
     *
     * @param code 类型编码
     * <AUTHOR>
     * @since 2021/11/02
     */
    public static EmployeeRoleEnum deCode(String code) {
        return Arrays.stream(EmployeeRoleEnum.values()).filter(x -> x.code.equals(code)).findFirst().orElse(null);
    }

    /**
     * 根据岗位id获取岗位枚举信息
     *
     * @param jobId 岗位id
     * @return 员工岗位枚举对象
     * <AUTHOR>
     * @since 2023/11/9 19:00
     */
    public static EmployeeRoleEnum deJobId(String jobId) {
        return Arrays.stream(EmployeeRoleEnum.values()).filter(x -> x.jobId.equals(jobId)).findFirst().orElse(null);
    }
}
