package com.mpolicy.settlement.core.modules.autocost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostUploadPcoInfoEntity;

import java.util.List;

public interface SettlementCostUploadPcoInfoService extends IService<SettlementCostUploadPcoInfoEntity> {
    /**
     * 批量插入
     * @param list
     * @return
     */
    int saveList(List<SettlementCostUploadPcoInfoEntity> list);
}
