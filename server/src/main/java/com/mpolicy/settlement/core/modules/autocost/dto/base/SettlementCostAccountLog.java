package com.mpolicy.settlement.core.modules.autocost.dto.base;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/05/31 9:18 下午
 * @Version 1.0
 */
@Data
@ApiModel(value = "结算账户流水表", description = "结算账户流水表")
public class SettlementCostAccountLog {
    /**
     * 交易流水号。
     */
    private String transCode;

    /**
     * 交易月份。
     */
    private String transMonth;

    /**
     * 交易时间。
     */
    private Date transTime;

    /**
     * 分支编码。
     */
    private String objectOrgCode;

    /**
     * 分支名称。
     */
    private String objectOrgName;

    /**
     * 工号。
     */
    private String sendObjectCode;

    /**
     * 姓名。
     */
    private String sendObjectName;

    /**
     * 交易类型：0出账，1入账。
     */
    private Integer transType;

    /**
     * 变更前上月负值。
     */
    private BigDecimal beforeLastMonthNegativeAmount;

    /**
     * 变更前账户累计金额。
     */
    private BigDecimal beforeAmount;

    /**
     * 当次增加金额。
     */
    private BigDecimal addAmount;

    /**
     * 描述或备注信息。
     */
    private String remark;

    /**
     * 是否删除标志：0有效，1删除。
     */
    private Boolean deleted;

    /**
     * 创建人。
     */
    private String createUser;

    /**
     * 创建时间。
     */
    private Date createTime;

    /**
     * 更新人。
     */
    private String updateUser;

    /**
     * 更新时间。
     */
    private Date updateTime;

    /**
     * 乐观锁版本控制字段。
     */
    private Integer revision;
}
