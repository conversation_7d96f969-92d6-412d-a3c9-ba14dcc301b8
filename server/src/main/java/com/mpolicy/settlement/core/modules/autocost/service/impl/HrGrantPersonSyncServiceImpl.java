package com.mpolicy.settlement.core.modules.autocost.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mpolicy.settlement.core.modules.autocost.dao.HrPostingRecordLdomDao;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostHrGrantPersonInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.service.HrGrantPersonSyncService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostHrGrantPersonInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Hr发放人员同步服务实现
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Service
public class HrGrantPersonSyncServiceImpl implements HrGrantPersonSyncService {

    @Autowired
    private HrPostingRecordLdomDao hrPostingRecordLdomDao;

    @Autowired
    private SettlementCostHrGrantPersonInfoService hrGrantPersonInfoService;

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat DATETIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int syncHrGrantPersonInfo(String settlementCycle, boolean forceOverride) {
        log.info("开始同步Hr发放人员信息，结算周期：{}，是否强制覆盖：{}", settlementCycle, forceOverride);

        if (StringUtils.isBlank(settlementCycle)) {
            throw new IllegalArgumentException("结算周期不能为空");
        }

        try {
            // 解析结算周期
            Date settlementDate = DATE_FORMAT.parse(settlementCycle);
            
            // 计算上月最后一天和当月第一天、最后一天
            Date lastDayOfPrevMonth = DateUtil.endOfMonth(DateUtil.offsetMonth(settlementDate, -1));
            Date firstDayOfMonth = DateUtil.beginOfMonth(settlementDate);
            Date lastDayOfMonth = DateUtil.endOfMonth(settlementDate);
            
            // 构建同步时间（结算周期 + 00:00:01）
            Date syncTime = DATETIME_FORMAT.parse(settlementCycle + " 00:00:01");

            log.info("计算时间范围 - 上月最后一天：{}，当月第一天：{}，当月最后一天：{}，同步时间：{}", 
                    DATE_FORMAT.format(lastDayOfPrevMonth), 
                    DATE_FORMAT.format(firstDayOfMonth), 
                    DATE_FORMAT.format(lastDayOfMonth),
                    DATETIME_FORMAT.format(syncTime));

            // 如果强制覆盖，先删除已存在的数据
            if (forceOverride) {
                int deletedCount = deleteBySettlementCycle(settlementCycle);
                log.info("强制覆盖模式，删除已存在数据：{} 条", deletedCount);
            }

            // 查询符合条件的员工数据
            List<SettlementCostHrGrantPersonInfoEntity> hrGrantPersonList = new ArrayList<>();

            // 1. 查询上月月末仍在职的员工
            List<Map<String, Object>> activeEmployees = hrPostingRecordLdomDao.listActiveEmployeesAtMonthEnd(syncTime, lastDayOfPrevMonth);
            log.info("查询到上月月末仍在职的员工：{} 人", activeEmployees.size());

            // 2. 查询当月离职的员工
            List<Map<String, Object>> resignedEmployees = hrPostingRecordLdomDao.listResignedEmployeesInMonth(syncTime, firstDayOfMonth, lastDayOfMonth);
            log.info("查询到当月离职的员工：{} 人", resignedEmployees.size());

            // 合并员工数据并去重
            Set<String> processedEmployeeCodes = new HashSet<>();

            // 处理在职员工
            if (CollectionUtils.isNotEmpty(activeEmployees)) {
                for (Map<String, Object> employee : activeEmployees) {
                    String employeeCode = (String) employee.get("employee_code");
                    if (StringUtils.isNotBlank(employeeCode) &&
                        !processedEmployeeCodes.contains(employeeCode)) {

                        hrGrantPersonList.add(buildHrGrantPersonInfo(employee, settlementCycle));
                        processedEmployeeCodes.add(employeeCode);
                    }
                }
            }

            // 处理离职员工
            if (CollectionUtils.isNotEmpty(resignedEmployees)) {
                for (Map<String, Object> employee : resignedEmployees) {
                    String employeeCode = (String) employee.get("employee_code");
                    if (StringUtils.isNotBlank(employeeCode) &&
                        !processedEmployeeCodes.contains(employeeCode)) {

                        hrGrantPersonList.add(buildHrGrantPersonInfo(employee, settlementCycle));
                        processedEmployeeCodes.add(employeeCode);
                    }
                }
            }

            log.info("合并去重后的员工总数：{} 人", hrGrantPersonList.size());

            // 批量保存
            int savedCount = 0;
            if (CollectionUtils.isNotEmpty(hrGrantPersonList)) {
                savedCount = hrGrantPersonInfoService.saveList(hrGrantPersonList);
                log.info("批量保存Hr发放人员信息成功，保存记录数：{}", savedCount);
            }

            log.info("同步Hr发放人员信息完成，结算周期：{}，同步记录数：{}", settlementCycle, savedCount);
            return savedCount;

        } catch (ParseException e) {
            log.error("解析结算周期失败：{}", settlementCycle, e);
            throw new IllegalArgumentException("结算周期格式错误，应为：YYYY-MM-DD");
        } catch (Exception e) {
            log.error("同步Hr发放人员信息失败，结算周期：{}", settlementCycle, e);
            throw new RuntimeException("同步Hr发放人员信息失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteBySettlementCycle(String settlementCycle) {
        log.info("开始删除Hr发放人员信息，结算周期：{}", settlementCycle);

        if (StringUtils.isBlank(settlementCycle)) {
            return 0;
        }

        // 先逻辑删除
        LambdaQueryWrapper<SettlementCostHrGrantPersonInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SettlementCostHrGrantPersonInfoEntity::getCostSettlementCycle, settlementCycle);
        queryWrapper.eq(SettlementCostHrGrantPersonInfoEntity::getDeleted, 0);

        List<SettlementCostHrGrantPersonInfoEntity> existingList = hrGrantPersonInfoService.list(queryWrapper);
        int deletedCount = 0;

        if (CollectionUtils.isNotEmpty(existingList)) {
            deletedCount = (int) hrGrantPersonInfoService.removeByIds(
                existingList.stream().map(SettlementCostHrGrantPersonInfoEntity::getId).toList()
            );
        }

        log.info("删除Hr发放人员信息完成，结算周期：{}，删除记录数：{}", settlementCycle, deletedCount);
        return deletedCount;
    }

    /**
     * 构建Hr发放人员信息实体
     */
    private SettlementCostHrGrantPersonInfoEntity buildHrGrantPersonInfo(Map<String, Object> employee, String settlementCycle) {
        String employeeCode = (String) employee.get("employee_code");
        String employeeName = (String) employee.get("employee_name");

        return SettlementCostHrGrantPersonInfoEntity.builder()
                .sendObjectCode(employeeCode)
                .sendObjectName(StringUtils.isNotBlank(employeeName) ? employeeName : "")
                .costSettlementCycle(settlementCycle)
                .hrGrantFlag(1) // 默认设置为hr发放
                .remark("系统自动同步")
                .deleted(0)
                .revision(1)
                .build();
    }
}
