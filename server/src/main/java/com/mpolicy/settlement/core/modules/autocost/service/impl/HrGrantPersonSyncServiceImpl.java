package com.mpolicy.settlement.core.modules.autocost.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.settlement.core.modules.autocost.dao.HrPostingRecordLdomDao;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostHrGrantPersonInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.enums.EmployeeTypeEnum;
import com.mpolicy.settlement.core.modules.autocost.service.HrGrantPersonSyncService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostHrGrantPersonInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Hr发放人员同步服务实现
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HrGrantPersonSyncServiceImpl implements HrGrantPersonSyncService {

    private final HrPostingRecordLdomDao hrPostingRecordLdomDao;
    private final SettlementCostHrGrantPersonInfoService hrGrantPersonInfoService;

    private static final DateTimeFormatter CYCLE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final String SYNC_REMARK = "系统自动同步";
    private static final Integer DEFAULT_HR_GRANT_FLAG = 1;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int syncHrGrantPersonInfo(String settlementCycle, boolean forceOverride) {
        log.info("开始同步Hr发放人员信息，结算周期：{}，是否强制覆盖：{}", settlementCycle, forceOverride);

        // 参数校验
        validateSettlementCycle(settlementCycle);

        try {
            // 解析和计算时间
            TimeRange timeRange = calculateTimeRange(settlementCycle);
            log.info("计算时间范围 - 上月最后一天：{}，当月第一天：{}，当月最后一天：{}，同步时间：{}",
                    timeRange.getLastDayOfPrevMonth(), timeRange.getFirstDayOfMonth(),
                    timeRange.getLastDayOfMonth(), timeRange.getSyncTime());

            // 强制覆盖模式处理
            handleForceOverride(settlementCycle, forceOverride);

            // 查询并合并员工数据
            List<SettlementCostHrGrantPersonInfoEntity> hrGrantPersonList = queryAndMergeEmployeeData(timeRange, settlementCycle);
            log.info("合并去重后的员工总数：{} 人", hrGrantPersonList.size());

            // 批量保存
            int savedCount = saveEmployeeData(hrGrantPersonList);
            log.info("同步Hr发放人员信息完成，结算周期：{}，同步记录数：{}", settlementCycle, savedCount);

            return savedCount;

        } catch (DateTimeParseException e) {
            log.error("解析结算周期失败：{}", settlementCycle, e);
            throw new GlobalException("结算周期格式错误，应为：YYYY-MM-DD");
        } catch (Exception e) {
            log.error("同步Hr发放人员信息失败，结算周期：{}", settlementCycle, e);
            throw new GlobalException("同步Hr发放人员信息失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteBySettlementCycle(String settlementCycle) {
        log.info("开始删除Hr发放人员信息，结算周期：{}", settlementCycle);

        if (StrUtil.isBlank(settlementCycle)) {
            return 0;
        }

        LambdaQueryWrapper<SettlementCostHrGrantPersonInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SettlementCostHrGrantPersonInfoEntity::getCostSettlementCycle, settlementCycle)
                   .eq(SettlementCostHrGrantPersonInfoEntity::getDeleted, 0);

        List<SettlementCostHrGrantPersonInfoEntity> existingList = hrGrantPersonInfoService.list(queryWrapper);
        int deletedCount = 0;

        if (CollectionUtils.isNotEmpty(existingList)) {
            List<Integer> ids = existingList.stream()
                    .map(SettlementCostHrGrantPersonInfoEntity::getId)
                    .collect(Collectors.toList());
            boolean success = hrGrantPersonInfoService.removeByIds(ids);
            deletedCount = success ? existingList.size() : 0;
        }

        log.info("删除Hr发放人员信息完成，结算周期：{}，删除记录数：{}", settlementCycle, deletedCount);
        return deletedCount;
    }

    /**
     * 参数校验
     */
    private void validateSettlementCycle(String settlementCycle) {
        if (StrUtil.isBlank(settlementCycle)) {
            throw new GlobalException("结算周期不能为空");
        }

        try {
            YearMonth.parse(settlementCycle, CYCLE_FORMATTER);
        } catch (DateTimeParseException e) {
            throw new GlobalException("结算周期格式错误，应为：YYYYMM，如：202506");
        }
    }

    /**
     * 计算时间范围
     */
    private TimeRange calculateTimeRange(String settlementCycle) {
        YearMonth yearMonth = YearMonth.parse(settlementCycle, CYCLE_FORMATTER);

        // 计算时间范围 - 当月为结算月，查询上月数据
        YearMonth prevMonth = yearMonth.minusMonths(1);
        LocalDate firstDayOfPrevMonth = prevMonth.atDay(1);
        LocalDate lastDayOfPrevMonth = prevMonth.atEndOfMonth();

        // 构建同步时间 - 使用上月最后一天 + 00:00:01
        String syncDateStr = lastDayOfPrevMonth.format(DATE_FORMATTER) + " 00:00:01";
        LocalDateTime syncDateTime = LocalDateTime.parse(syncDateStr, DATETIME_FORMATTER);

        return new TimeRange(lastDayOfPrevMonth, firstDayOfPrevMonth, lastDayOfPrevMonth, syncDateTime);
    }

    /**
     * 处理强制覆盖
     */
    private void handleForceOverride(String settlementCycle, boolean forceOverride) {
        if (forceOverride) {
            int deletedCount = deleteBySettlementCycle(settlementCycle);
            log.info("强制覆盖模式，删除已存在数据：{} 条", deletedCount);
        }
    }

    /**
     * 查询并合并员工数据
     */
    private List<SettlementCostHrGrantPersonInfoEntity> queryAndMergeEmployeeData(TimeRange timeRange, String settlementCycle) {
        // 查询员工数据
        List<Map<String, Object>> activeEmployees = queryActiveEmployees(timeRange);
        List<Map<String, Object>> resignedEmployees = queryResignedEmployees(timeRange);

        log.info("查询到上月月末仍在职的员工：{} 人", activeEmployees.size());
        log.info("查询到当月离职的员工：{} 人", resignedEmployees.size());

        // 合并并去重
        return mergeAndDeduplicateEmployees(activeEmployees, resignedEmployees, settlementCycle);
    }

    /**
     * 查询在职员工
     */
    private List<Map<String, Object>> queryActiveEmployees(TimeRange timeRange) {
        Date syncTime = DateUtil.date(timeRange.getSyncTime().atZone(java.time.ZoneId.systemDefault()).toInstant());
        Date lastDayOfPrevMonth = DateUtil.date(timeRange.getLastDayOfPrevMonth().atStartOfDay().atZone(java.time.ZoneId.systemDefault()).toInstant());

        return hrPostingRecordLdomDao.listActiveEmployeesAtMonthEnd(syncTime, lastDayOfPrevMonth);
    }

    /**
     * 查询离职员工
     */
    private List<Map<String, Object>> queryResignedEmployees(TimeRange timeRange) {
        Date syncTime = DateUtil.date(timeRange.getSyncTime().atZone(java.time.ZoneId.systemDefault()).toInstant());
        Date firstDayOfMonth = DateUtil.date(timeRange.getFirstDayOfMonth().atStartOfDay().atZone(java.time.ZoneId.systemDefault()).toInstant());
        Date lastDayOfMonth = DateUtil.date(timeRange.getLastDayOfMonth().atStartOfDay().atZone(java.time.ZoneId.systemDefault()).toInstant());

        return hrPostingRecordLdomDao.listResignedEmployeesInMonth(syncTime, firstDayOfMonth, lastDayOfMonth);
    }

    /**
     * 合并并去重员工数据
     */
    private List<SettlementCostHrGrantPersonInfoEntity> mergeAndDeduplicateEmployees(
            List<Map<String, Object>> activeEmployees,
            List<Map<String, Object>> resignedEmployees,
            String settlementCycle) {

        Set<String> processedEmployeeCodes = new HashSet<>();
        List<SettlementCostHrGrantPersonInfoEntity> result = new ArrayList<>();

        // 处理在职员工
        processEmployeeList(activeEmployees, settlementCycle, processedEmployeeCodes, result);

        // 处理离职员工
        processEmployeeList(resignedEmployees, settlementCycle, processedEmployeeCodes, result);

        return result;
    }

    /**
     * 处理员工列表
     */
    private void processEmployeeList(List<Map<String, Object>> employees,
                                   String settlementCycle,
                                   Set<String> processedEmployeeCodes,
                                   List<SettlementCostHrGrantPersonInfoEntity> result) {
        if (CollectionUtils.isEmpty(employees)) {
            return;
        }

        for (Map<String, Object> employee : employees) {
            String employeeCode = (String) employee.get("employee_code");
            if (StrUtil.isNotBlank(employeeCode) && !processedEmployeeCodes.contains(employeeCode)) {
                result.add(buildHrGrantPersonInfo(employee, settlementCycle));
                processedEmployeeCodes.add(employeeCode);
            }
        }
    }

    /**
     * 保存员工数据
     */
    private int saveEmployeeData(List<SettlementCostHrGrantPersonInfoEntity> hrGrantPersonList) {
        if (CollectionUtils.isEmpty(hrGrantPersonList)) {
            log.info("没有需要保存的员工数据");
            return 0;
        }

        int savedCount = hrGrantPersonInfoService.saveList(hrGrantPersonList);
        log.info("批量保存Hr发放人员信息成功，保存记录数：{}", savedCount);
        return savedCount;
    }

    /**
     * 构建Hr发放人员信息实体
     */
    private SettlementCostHrGrantPersonInfoEntity buildHrGrantPersonInfo(Map<String, Object> employee, String settlementCycle) {
        String employeeCode = (String) employee.get("employee_code");
        String employeeName = (String) employee.get("employee_name");

        return SettlementCostHrGrantPersonInfoEntity.builder()
                .sendObjectCode(employeeCode)
                .sendObjectName(StrUtil.isNotBlank(employeeName) ? employeeName : "")
                .costSettlementCycle(settlementCycle)
                .hrGrantFlag(DEFAULT_HR_GRANT_FLAG)
                .remark(SYNC_REMARK)
                .deleted(0)
                .revision(1)
                .build();
    }

    /**
     * 时间范围内部类
     */
    private static class TimeRange {
        private final LocalDate lastDayOfPrevMonth;
        private final LocalDate firstDayOfMonth;
        private final LocalDate lastDayOfMonth;
        private final LocalDateTime syncTime;

        public TimeRange(LocalDate lastDayOfPrevMonth, LocalDate firstDayOfMonth,
                        LocalDate lastDayOfMonth, LocalDateTime syncTime) {
            this.lastDayOfPrevMonth = lastDayOfPrevMonth;
            this.firstDayOfMonth = firstDayOfMonth;
            this.lastDayOfMonth = lastDayOfMonth;
            this.syncTime = syncTime;
        }

        public LocalDate getLastDayOfPrevMonth() { return lastDayOfPrevMonth; }
        public LocalDate getFirstDayOfMonth() { return firstDayOfMonth; }
        public LocalDate getLastDayOfMonth() { return lastDayOfMonth; }
        public LocalDateTime getSyncTime() { return syncTime; }
    }
}
