package com.mpolicy.settlement.core.modules.autocost.helper;

import com.mpolicy.common.redis.IRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 科目方案计算辅助类
 *
 * <AUTHOR>
 * @since 2023-10-22 12:24
 */
@Slf4j
@Component
public class SubjectCalculateHelper {

    public static SubjectCalculateHelper autoCostSubjectDataHelper;

    @Autowired
    IRedisService redisService;

    @PostConstruct
    public void init() {
        autoCostSubjectDataHelper = this;
        // redis服务
        autoCostSubjectDataHelper.redisService = this.redisService;
    }

    public static String test(String reconcileCompanyCode) {
        return "test";
    }
}