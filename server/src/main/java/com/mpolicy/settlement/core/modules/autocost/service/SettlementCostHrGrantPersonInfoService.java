package com.mpolicy.settlement.core.modules.autocost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostHrGrantPersonInfoEntity;

import java.util.List;

/**
 * 结算Hr发放人员表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface SettlementCostHrGrantPersonInfoService extends IService<SettlementCostHrGrantPersonInfoEntity> {

    /**
     * 批量保存Hr发放人员信息
     *
     * @param list Hr发放人员信息列表
     * @return 保存成功的记录数
     */
    int saveList(List<SettlementCostHrGrantPersonInfoEntity> list);

    /**
     * 根据工号和结算周期查询Hr发放人员信息
     *
     * @param sendObjectCode 工号
     * @param costSettlementCycle 结算周期
     * @return Hr发放人员信息
     */
    SettlementCostHrGrantPersonInfoEntity getBySendObjectCodeAndCycle(String sendObjectCode, String costSettlementCycle);

    /**
     * 根据工号查询Hr发放人员信息列表
     *
     * @param sendObjectCode 工号
     * @return Hr发放人员信息列表
     */
    List<SettlementCostHrGrantPersonInfoEntity> listBySendObjectCode(String sendObjectCode);

    /**
     * 根据工号和结算周期更新Hr发放标志位
     *
     * @param sendObjectCode 工号
     * @param costSettlementCycle 结算周期
     * @param hrGrantFlag Hr发放标志位
     * @return 更新是否成功
     */
    boolean updateHrGrantFlag(String sendObjectCode, String costSettlementCycle, Integer hrGrantFlag);

    /**
     * 根据结算周期查询Hr发放人员信息列表
     *
     * @param costSettlementCycle 结算周期
     * @return Hr发放人员信息列表
     */
    List<SettlementCostHrGrantPersonInfoEntity> listByCostSettlementCycle(String costSettlementCycle);

    /**
     * 批量更新Hr发放人员信息
     *
     * @param list Hr发放人员信息列表
     * @return 更新成功的记录数
     */
    int batchUpdateList(List<SettlementCostHrGrantPersonInfoEntity> list);

    /**
     * 根据结算周期删除Hr发放人员信息(物理删除，慎用)
     *
     * @param costSettlementCycle 结算周期
     * @return 删除成功的记录数
     */
    int deletePhysicalByCostSettlementCycle(String costSettlementCycle);
}
