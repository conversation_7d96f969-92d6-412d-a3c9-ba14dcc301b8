package com.mpolicy.settlement.core.modules.autocost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostHrGrantPersonInfoEntity;

import java.util.List;

/**
 * 结算Hr发放人员表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface SettlementCostHrGrantPersonInfoService extends IService<SettlementCostHrGrantPersonInfoEntity> {

    /**
     * 批量保存Hr发放人员信息
     *
     * @param list Hr发放人员信息列表
     * @return 保存成功的记录数
     */
    int saveList(List<SettlementCostHrGrantPersonInfoEntity> list);

    /**
     * 根据工号查询Hr发放人员信息
     *
     * @param sendObjectCode 工号
     * @return Hr发放人员信息
     */
    SettlementCostHrGrantPersonInfoEntity getBySendObjectCode(String sendObjectCode);

    /**
     * 根据工号更新Hr发放标志位
     *
     * @param sendObjectCode 工号
     * @param hrGrantFlag Hr发放标志位
     * @return 更新是否成功
     */
    boolean updateHrGrantFlag(String sendObjectCode, Integer hrGrantFlag);
}
