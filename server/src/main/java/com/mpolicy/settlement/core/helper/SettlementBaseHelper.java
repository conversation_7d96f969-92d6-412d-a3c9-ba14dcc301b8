package com.mpolicy.settlement.core.helper;

import com.alibaba.fastjson.JSON;
import com.mpolicy.common.redis.IRedisService;
import com.mpolicy.service.common.woodpecker.MonitorWoodpeckerHelper;
import com.mpolicy.service.common.woodpecker.dto.MonitorWoodpeckerData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 结算系统基础helper支持
 *
 * <AUTHOR>
 * @since 2022/11/19
 */
@Component
@Slf4j
public class SettlementBaseHelper {

    public static SettlementBaseHelper settlementBaseHelper;

    @Autowired
    IRedisService redisService;

    @PostConstruct
    public void init() {
        settlementBaseHelper = this;
        // redis服务
        settlementBaseHelper.redisService = this.redisService;
    }


    /**
     * 【结算中心】异步执行写入监控啄木鸟数据信息
     *
     * @param data 触发事件源报文
     * <AUTHOR>
     * @since 2023/5/22 12:17
     */
    public static void addMonitorWoodpeckerDataAsync(MonitorWoodpeckerData data) {
        log.debug("【结算中心】异步执行写入监控啄木鸟数据信息表，请求主体对象={}", JSON.toJSONString(data));
        if (data == null) {
            log.warn("监控数据请求对象对象为空");
        } else {
            MonitorWoodpeckerHelper.addMonitorWoodpeckerDataAsync("settlement-center-core","结算中心",data);
        }
    }
}
