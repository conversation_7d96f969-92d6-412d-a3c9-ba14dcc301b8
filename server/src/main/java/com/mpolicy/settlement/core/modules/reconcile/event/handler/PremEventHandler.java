package com.mpolicy.settlement.core.modules.reconcile.event.handler;

import com.mpolicy.settlement.core.modules.reconcile.event.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public abstract class PremEventHandler implements InitializingBean {

    /**
     * 获取保单险种费率,会返回一个集合,因为存在不同的科目,科目是唯一的
     *
     * @param input
     * @return
     */
    public List<PolicyProductPremResult> queryPolicyProductPrem(PolicyProductPremInput input) {
        log.info("没有实现获取保单险种费率方法");
        return Collections.emptyList();
    }

    /**
     * 获取一单一议的费率
     *
     * @param input
     * @return
     */
    public PolicyPremResult queryPolicyPrem(PolicyPremInput input) {
        log.info("没有实现获取一单一议的费率");
        return null;
    }

    public PolicyTaxPremResult queryPolicyTaxPrem(PolicyPremInput input) {
        log.info("没有实现获取一单一议的税率");
        return null;
    }
}