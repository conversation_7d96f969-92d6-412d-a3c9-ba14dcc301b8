package com.mpolicy.settlement.core.modules.autocost.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 支出结算科目计算信息表
 * 
 * <AUTHOR>
 * @since 2023-11-06 21:00:27
 */
@TableName("settlement_cost_subject_calculate_record")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostSubjectCalculateRecordEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 所属周期
	 */
	private String costSettlementCycle;
	/**
	 * 科目编码
	 */
	private String subjectCode;
	/**
	 * 科目计算总数
	 */
	private Integer subjectCalculateSize;
	/**
	 * 科目计算总金额
	 */
	private BigDecimal subjectCalculateCash;
	/**
	 * 完成科目数据耗时
	 */
	private Integer finishMillis;
	/**
	 * 科目计算完成状态
	 */
	private Integer subjectCalculateStatus;
	/**
	 * 科目计算完成描述
	 */
	private String subjectCalculateDesc;

	/**
	 * 凭证编号
	 */
	private String documentCode;
	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
