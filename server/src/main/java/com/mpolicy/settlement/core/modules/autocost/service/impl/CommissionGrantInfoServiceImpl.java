package com.mpolicy.settlement.core.modules.autocost.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.oss.StorageService;
import com.mpolicy.common.utils.excel.ExcelUtil;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.modules.autocost.dto.CommissionGrantInfoExcelDto;
import com.mpolicy.settlement.core.modules.autocost.dto.PcoBaseInfoExcelDto;
import com.mpolicy.settlement.core.modules.autocost.dto.cache.OrganizationCache;
import com.mpolicy.settlement.core.modules.autocost.dto.dynamic.SubjectDataDynamicErrorBase;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostCommissionGrantInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostSubjectDataDynamicApplyEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostUploadPcoInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.enums.GrantWayEnum;
import com.mpolicy.settlement.core.modules.autocost.helper.SettlementEmployeeHelper;
import com.mpolicy.settlement.core.modules.autocost.service.*;
import com.mpolicy.settlement.core.modules.referrer.entity.ChannelApplicationReferrerEntity;
import com.mpolicy.settlement.core.service.SysDocumentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
@Slf4j
public class CommissionGrantInfoServiceImpl extends AbstractSettlementCostImportService implements CommissionGrantInfoService {
    @Value("${insure.temp-file-path:/logs}")
    private String tempPath;
    @Autowired
    private SysDocumentService sysDocumentService;
    @Autowired
    private StorageService storageService;
    @Autowired
    protected SettlementCostCommissionGrantInfoService settlementCostCommissionGrantInfoService;

    @Autowired
    private SettlementCostSubjectDataDynamicApplyService settlementCostSubjectDataDynamicApplyService;
    @Autowired
    private SettlementCostDynamicSubjectErrorDataService settlementCostDynamicSubjectErrorDataService;
    @Autowired
    private TransactionTemplate transactionTemplate;
    public Boolean loadGrantInfo(SettlementCostSubjectDataDynamicApplyEntity apply, String userName){
        log.info("开始导入发放方式信息");
        String localFilePath = tempPath.concat(apply.getFileName());
        storageService.downloadFileToLocal(apply.getFilePath(), localFilePath);
        List<CommissionGrantInfoExcelDto> excelDtos = ExcelUtil.readExcel(localFilePath, CommissionGrantInfoExcelDto.class);

        if(CollectionUtils.isEmpty(excelDtos)){
            return Boolean.TRUE;
        }
        log.info("获取导入文件记录数：{}", excelDtos.size());
        Map<String, OrganizationCache> orgMap = mapOrg();
        List<SettlementCostCommissionGrantInfoEntity> grantList =  Lists.newArrayListWithCapacity(excelDtos.size());
        Map<Integer,SubjectDataDynamicErrorBase> errorData = Maps.newHashMap();
        List<String> employeeCodes = excelDtos.stream().map(CommissionGrantInfoExcelDto::getEmployeeCode).collect(Collectors.toList());
        Map<String, ChannelApplicationReferrerEntity>   employeeMap =  SettlementEmployeeHelper.mapByEmployeeCodes(employeeCodes);
        for(CommissionGrantInfoExcelDto p : excelDtos){
            try {
                if(employeeMap.get(p.getEmployeeCode()) == null){
                    addErrorData(errorData,apply.getApplyCode(),apply.getCostSettlementCycle(),p.getNum(), StrUtil.format("工号{}对应的信息不存在",p.getEmployeeCode()),userName);
                }else if(!Objects.equals(employeeMap.get(p.getEmployeeCode()).getReferrerName(),p.getEmployeeName())){
                    addErrorData(errorData,apply.getApplyCode(),apply.getCostSettlementCycle(),p.getNum(), StrUtil.format("工号{}对应的姓名{}与导入的姓名{}不一致",p.getEmployeeCode(),employeeMap.get(p.getEmployeeCode()).getReferrerName(),p.getEmployeeName()),userName);
                }
                SettlementCostCommissionGrantInfoEntity grantInfoEntity = new SettlementCostCommissionGrantInfoEntity();
                BeanUtils.copyProperties(p, grantInfoEntity);
                grantInfoEntity.setGrantWay(GrantWayEnum.deName(p.getGrantWay()).getCode());
                if(Objects.equals(grantInfoEntity.getGrantWay(),GrantWayEnum.BANK_CARD.getCode())
                    && StringUtils.isBlank(p.getBankCardNo())){
                    addErrorData(errorData,apply.getApplyCode(),apply.getCostSettlementCycle(),p.getNum(), StrUtil.format("发放方式为银行卡时，卡号不能为空"),userName);
                }
                if(Objects.equals(grantInfoEntity.getGrantWay(),GrantWayEnum.BANK_CARD.getCode())
                        && StringUtils.isBlank(p.getBankName())){
                    addErrorData(errorData,apply.getApplyCode(),apply.getCostSettlementCycle(),p.getNum(), StrUtil.format("发放方式为银行卡时，银行不能为空"),userName);
                }


                grantInfoEntity.setApplyCode(apply.getApplyCode());
                grantInfoEntity.setOpMonth(apply.getCostSettlementCycle());
                grantList.add(grantInfoEntity);
            }catch (GlobalException e){
                log.warn("佣金发放信息导入处理异常:{}",p.getEmployeeCode(),e.getMsg(),e);
                addErrorData(errorData,apply.getApplyCode(),apply.getCostSettlementCycle(),p.getNum(), StrUtil.format("佣金发放信息导入处理异常:{}",p.getEmployeeCode(),e.getMsg()),userName);
                //throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("pco{}基础信息导入处理异常:{}",p.getEmployeeCode(),e.getMsg())));
            }catch (Exception e){
                addErrorData(errorData,apply.getApplyCode(),apply.getCostSettlementCycle(),p.getNum(), StrUtil.format("佣金发放信息导入处理异常:{}",p.getEmployeeCode(),e.getMessage()),userName);
                //throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("pco{}基础信息导入处理异常:{}",p.getEmployeeCode(),e.getMessage())));

            }
        }

        Boolean addResult = true;
        //入库
        if(CollectionUtils.isEmpty(errorData.values())) {
            addResult = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                @Override
                public Boolean doInTransaction(TransactionStatus transactionStatus) {
                    try {

                        settlementCostSubjectDataDynamicApplyService.lambdaUpdate().set(SettlementCostSubjectDataDynamicApplyEntity::getApplyStatus, 1)
                                .set(SettlementCostSubjectDataDynamicApplyEntity::getApplyEnable, 1)
                                .set(SettlementCostSubjectDataDynamicApplyEntity::getApplyMessage, Constant.DEFAULT_SUCCESS)
                                .eq(SettlementCostSubjectDataDynamicApplyEntity::getApplyCode, apply.getApplyCode())
                                .update();
                        settlementCostSubjectDataDynamicApplyService.lambdaUpdate().set(SettlementCostSubjectDataDynamicApplyEntity::getApplyStatus, -2)
                                .set(SettlementCostSubjectDataDynamicApplyEntity::getApplyEnable, 0)
                                .ne(SettlementCostSubjectDataDynamicApplyEntity::getApplyCode, apply.getApplyCode())
                                .eq(SettlementCostSubjectDataDynamicApplyEntity::getCostSettlementCycle, apply.getCostSettlementCycle())
                                .update();
                        settlementCostCommissionGrantInfoService.remove(Wrappers.<SettlementCostCommissionGrantInfoEntity>lambdaQuery()
                                .ne(SettlementCostCommissionGrantInfoEntity::getApplyCode, apply.getApplyCode())
                                .eq(SettlementCostCommissionGrantInfoEntity::getOpMonth,apply.getCostSettlementCycle()));
                        settlementCostCommissionGrantInfoService.saveList(grantList);
                        return Boolean.TRUE;
                    } catch (Exception e){
                        //回滚
                        log.warn("设置有效 + 清空之前的申请数据(包括抽数记录)失败",e);
                        transactionStatus.setRollbackOnly();
                        return Boolean.FALSE;
                    }
                }
            });

        }
        if(CollectionUtils.isNotEmpty(errorData.values()) || !addResult){
            if(CollectionUtils.isNotEmpty(errorData.values())){
                settlementCostDynamicSubjectErrorDataService.saveList(errorData.values().stream().collect(Collectors.toList()));
            }
            // 主表状态设置为失败
            settlementCostSubjectDataDynamicApplyService.lambdaUpdate().set(SettlementCostSubjectDataDynamicApplyEntity::getApplyStatus, -1)
                    .set(SettlementCostSubjectDataDynamicApplyEntity::getApplyMessage, "发放方式信息导入失败")
                    .set(SettlementCostSubjectDataDynamicApplyEntity::getErrorNum,errorData.size())
                    .eq(SettlementCostSubjectDataDynamicApplyEntity::getApplyCode, apply.getApplyCode())

                    .update();
            log.warn("发放方式信息导入失败!!!");
            return Boolean.FALSE;
        }
        return Boolean.TRUE;

    }

    private void addErrorData(Map<Integer,SubjectDataDynamicErrorBase> errorData,String applyCode,String costSettlementCycle,
                                Integer serialNumber,String errorMsg,String userName){
        Integer rowNum = serialNumber+1;
        if(errorData.containsKey(rowNum)){
            errorData.get(rowNum).addErrorMessage(errorMsg);
        }else {
            SubjectDataDynamicErrorBase errorBase = new SubjectDataDynamicErrorBase();
            errorBase.setSheetIndex(1);
            errorBase.setSheetName("sheet1");
            errorBase.setRowNum(rowNum);
            errorBase.setSerialNumber(serialNumber);
            errorBase.setApplyCode(applyCode);
            errorBase.setUserName(userName);
            errorBase.setCostSettlementCycle(costSettlementCycle);
            errorBase.addErrorMessage(errorMsg);
            errorData.put(rowNum,errorBase);
        }
    }

    public void deleteGrantInfo(String applyCode,String costSettlementCycle){
        settlementCostCommissionGrantInfoService.remove(Wrappers.<SettlementCostCommissionGrantInfoEntity>lambdaQuery()
                .ne(SettlementCostCommissionGrantInfoEntity::getApplyCode, applyCode)
                .eq(SettlementCostCommissionGrantInfoEntity::getOpMonth,costSettlementCycle));
    }


}
