package com.mpolicy.settlement.core.modules.autocost.dao;

import com.mpolicy.service.common.mapper.ImsBaseMapper;
import com.mpolicy.settlement.core.modules.autocost.dto.hr.HrPostingRecordDto;
import com.mpolicy.settlement.core.modules.autocost.entity.HrPostingRecordLdomEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface HrPostingRecordLdomDao extends ImsBaseMapper<HrPostingRecordLdomEntity> {

    /**
     * 获取任职
     * @param syncTime
     * @param startTime
     * @param endTime
     * @return
     */
    @Select("select o.code  as regionCode, o.name as regionName,p.posting_dept as postingDept,d.code as bchCode, d.name as bchName,p.employee_id as employeeId,p.employee_code as employeeCode " +
            " , l.name as employeeName "+
            " ,p.posting_start_date as postStartDate,p.posting_end_date as postEndDate,p.job_postion_id as jobId,p.job_code as jobCode, p.service_type as postTypeCode " +
            " ,p.entry_date as joinDate,p.last_work_date as leaveDate" +
            " from hr_posting_record_ldom  p " +
            " LEFT JOIN hr_organization_ldom o on p.posting_org = o.id and o.sync_time =#{syncTime} and o.enabled_flag =1 " +
            " LEFT JOIN hr_organization_ldom d on p.posting_dept = d.id and d.sync_time =#{syncTime} and d.enabled_flag =1 " +
            " LEFT JOIN hr_employee_ldom l on p.employee_id = l.id and l.sync_time =#{syncTime} and l.enabled_flag =1 " +
            " where p.sync_time =#{syncTime} and p.enabled_flag = 1 " +
            " and p.posting_status in(0,1) and p.service_type in (0, 1) and p.job_postion_id = 304279 " +
            " and p.posting_start_date <=#{startTime} and posting_end_date >=#{endTime} " +
            " and l.employee_status not in (6,8) " +
            " order by employee_code asc ")
    List<HrPostingRecordDto> listEmployingPcoEffectivePostRecord(@Param("syncTime") Date syncTime, @Param("startTime") Date startTime,@Param("endTime") Date endTime);

    /**
     * 查询上月月末仍在职的员工（普通员工或返聘员工）
     *
     * @param syncTime 同步时间
     * @param lastDayOfPrevMonth 上月最后一天
     * @return 员工列表
     */
    @Select("SELECT p.employee_code, l.name as employee_name " +
            "FROM hr_posting_record_ldom p " +
            "LEFT JOIN hr_employee_ldom l ON p.employee_id = l.id AND l.sync_time = #{syncTime} AND l.enabled_flag = 1 " +
            "WHERE p.enabled_flag = 1 " +
            "AND p.service_type = 0 " +
            "AND p.posting_status = 0 " +
            "AND p.posting_end_date >= #{lastDayOfPrevMonth} " +
            "AND p.employ_type IN (0, 3) " +
            "AND p.sync_time = #{syncTime}")
    List<Map<String, Object>> listActiveEmployeesAtMonthEnd(@Param("syncTime") Date syncTime, @Param("lastDayOfPrevMonth") Date lastDayOfPrevMonth);

    /**
     * 查询当月离职的员工（普通员工或返聘员工）
     *
     * @param syncTime 同步时间
     * @param monthStart 当月第一天
     * @param monthEnd 当月最后一天
     * @return 员工列表
     */
    @Select("SELECT p.employee_code, l.name as employee_name " +
            "FROM hr_posting_record_ldom p " +
            "LEFT JOIN hr_employee_ldom l ON p.employee_id = l.id AND l.sync_time = #{syncTime} AND l.enabled_flag = 1 " +
            "WHERE p.enabled_flag = 1 " +
            "AND p.service_type = 0 " +
            "AND p.posting_status = 1 " +
            "AND p.last_work_date >= #{monthStart} " +
            "AND p.last_work_date <= #{monthEnd} " +
            "AND p.employ_type IN (0, 3) " +
            "AND p.sync_time = #{syncTime}")
    List<Map<String, Object>> listResignedEmployeesInMonth(@Param("syncTime") Date syncTime, @Param("monthStart") Date monthStart, @Param("monthEnd") Date monthEnd);
}
