package com.mpolicy.settlement.core.modules.reconcile.event.project.policyplatform;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileStatusEnum;
import com.mpolicy.settlement.core.enums.ReconcileTypeEnum;
import com.mpolicy.settlement.core.enums.SettlementPolicyHandlerEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.ConfirmStatusEnum;
import com.mpolicy.settlement.core.modules.govern.dto.proejct.ChangePolicyCodeData;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostCorrectionDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.SettlementPolicyFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementPolicyHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.project.common.AbsPolicyCommonEvent;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventCheckResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventData;
import com.mpolicy.settlement.core.modules.reconcile.service.impl.SettlementCostProcessServiceImpl;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.modules.reconcile.vo.BasisSettlementPolicyInfoDomain;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.SYSTEM_CORRECTION_USER;

/**
 * <AUTHOR>
 * @description 保单号变更变更事件
 * @date 2024/2/25 1:43 下午
 * @Version 1.0
 */
@Service
@Slf4j
public class PolicyNoChangeEvent extends AbsPolicyCommonEvent {
    @Override
    public HandleEventCheckResult handleIncomeEventCheck(SettlementEventJobEntity eventJob, Integer reconcileType) {
        ChangePolicyCodeData changePolicyData =
            JSONObject.parseObject(eventJob.getEventRequest(), ChangePolicyCodeData.class);
        String sourcePolicyNo = changePolicyData.getPolicyCode();
        // 2.参数校验
        if (StrUtil.isBlank(sourcePolicyNo)) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg("保单号不存在").build();
        }
        // 新保单号
        String policyNo = changePolicyData.getNewPolicyCode();
        if (StrUtil.isBlank(policyNo)) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg("保单号不存在").build();
        }
        // 判断事件是否已经生成了明细数据,如果生成了就不在运行了
        boolean checkSettlementPolicyInfo = checkSettlementPolicyInfo(eventJob.getPushEventCode(), reconcileType);
        // 如果存在纪录
        if (checkSettlementPolicyInfo) {
            return HandleEventCheckResult.builder().checkStatus(false)
                .checkMsg(StrUtil.format("保单号变更变更事件已经写入明细")).build();
        }
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleIncomeEvent(SettlementEventJobEntity eventJob, HandleEventData handleEventData) {
        ChangePolicyCodeData changePolicyData =
            JSONObject.parseObject(eventJob.getEventRequest(), ChangePolicyCodeData.class);
        // 原保单号
        String sourcePolicyNo = changePolicyData.getPolicyCode();
        List<SettlementPolicyInfoEntity> settlementPolicyInfoList =
            settlementPolicyInfoService.lambdaQuery().eq(SettlementPolicyInfoEntity::getPolicyNo, sourcePolicyNo)
                .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode())
                .eq(SettlementPolicyInfoEntity::getReconcileType, handleEventData.getReconcileType()).list();
        if (!settlementPolicyInfoList.isEmpty()) {
            // 判断一下 是否存在已经结算的数据,如果存在,那么提示失败
            boolean anyMatch = settlementPolicyInfoList.stream()
                .anyMatch(a -> ReconcileStatusEnum.RECONCILE_FINISH.getStatusCode().equals(a.getReconcileStatus()));
            if (anyMatch) {
                return "明细存在已经结算的数据,暂不处理";
            }
            // 冲正错误数据
            settlementPolicyInfoService.rectification(settlementPolicyInfoList);
        }
        // 处理保单号变更事件
        try {
            PolicyProductTypeEnum prodTypeEnum =
                PolicyProductTypeEnum.getProdTypeEnum(handleEventData.getPolicyInfo().getPolicyProductType());
            ReconcileTypeEnum reconcileTypeEnum = ReconcileTypeEnum.matchSearchCode(handleEventData.getReconcileType());
            BasisSettlementPolicyInfoDomain domain = new BasisSettlementPolicyInfoDomain();
            domain.setContractInfo(handleEventData.getPolicyInfo());
            domain.setSettlementEventTypeEnum(
                prodTypeEnum == PolicyProductTypeEnum.GROUP ? SettlementEventTypeEnum.GROUP_NEW_POLICY
                    : SettlementEventTypeEnum.PERSONAL_NEW_POLICY);
            domain.setReconcileTypeEnum(reconcileTypeEnum);
            domain.setEventSourceCode(eventJob.getPushEventCode());
            // 获取处理事件
            SettlementPolicyHandler handler =
                SettlementPolicyFactory.getInvoke(SettlementPolicyHandlerEnum.NEW_INSURANCE);
            // 构建结算明细基础信息
            List<SettlementPolicyInfoEntity> policyInfoList = handler.buildBasisSettlementPolicyInfo(domain);
            if (CollUtil.isNotEmpty(policyInfoList)) {
                //匹配产品信息
                handler.matchInsuranceProduct(reconcileTypeEnum, policyInfoList);
                // 构建手续费和折标保费金额
                List<SettlementPolicyInfoEntity> resultList = handler.buildSettlementSubjectAmount(policyInfoList);
                if (!resultList.isEmpty()) {
                    settlementPolicyInfoService.saveBatch(resultList);
                }
            } else {
                return StrUtil.format("没有生成结算明细，保单号={}, 事件编码={}", eventJob.getEventBusinessCode(),
                    eventJob.getPushEventCode());
            }
        } catch (GlobalException e) {
            throw e;
        } catch (Exception e) {
            String msg = StrUtil.format("生成结算明细异常，保单号={}, 事件编码={}", eventJob.getEventBusinessCode(),
                    eventJob.getPushEventCode());
            log.warn(msg, e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(msg));
        }
        return "success";
    }

    @Override
    public HandleEventCheckResult handleCostEventCheck(SettlementEventJobEntity eventJob) {
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleCostEvent(SettlementEventJobEntity eventJob, HandleEventData eventData) {
        ChangePolicyCodeData changePolicyData =
            JSONObject.parseObject(eventJob.getEventRequest(), ChangePolicyCodeData.class);
        String policyNo = changePolicyData.getPolicyCode();
        String newPolicyNo = changePolicyData.getNewPolicyCode();
        if (StrUtil.isEmpty(policyNo)) {
            return StrUtil.format("保单号-{}不存在-success", policyNo);
        }

        List<SettlementCostInfoEntity> costInfoEntityList = settlementCostInfoService.lambdaQuery()
            .eq(SettlementCostInfoEntity::getContractCode, changePolicyData.getContractCode())
            .eq(SettlementCostInfoEntity::getCorrectionFlag, 0).list();
        if (CollectionUtils.isEmpty(costInfoEntityList)) {
            return StrUtil.format("保单号/合同号-{}/{}不存在-success", policyNo, changePolicyData.getContractCode());
        }

        SettlementCostPolicyInfoEntity costPolicy =
            settlementCostPolicyInfoService.getCostPolicyInfoEntityByContractCode(eventJob.getContractCode());

        if (Objects.isNull(costPolicy)) {
            return "结算保单维度信息缺失-success";
        }
        //2、参数验证，验证不通过抛出异常
        settlementCostProcessService.validParam(eventJob, null);

        //        //新插入一条保单
        //        SettlementCostPolicyInfoEntity newCostPolicyInfoEntity = new SettlementCostPolicyInfoEntity();
        //        BeanUtil.copyProperties(costPolicy, newCostPolicyInfoEntity);
        //        newCostPolicyInfoEntity.setPolicyNo(policyNo);
        //        settlementCostPolicyInfoService.save(newCostPolicyInfoEntity);

        //冲正
        CostCorrectionDto costCorrectionDto = new CostCorrectionDto();

        List<SettlementCostInfoEntity> newCostList = costInfoEntityList.stream().map(x -> {
            //生成一条新结算信息
            SettlementCostInfoEntity newSettlementCostInfo = new SettlementCostInfoEntity();
            BeanUtil.copyProperties(x, newSettlementCostInfo);
            newSettlementCostInfo.setId(null);
            newSettlementCostInfo.setPolicyNo(newPolicyNo);
            if (Objects.equals(x.getPolicyNo(), x.getCompanyPolicyNo())) {
                newSettlementCostInfo.setCompanyPolicyNo(newPolicyNo);
            }
            newSettlementCostInfo.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
            //记账时间处理
            newSettlementCostInfo.setSettlementTime(new Date());
            newSettlementCostInfo.setSettlementDate(newSettlementCostInfo.getSettlementTime());

            //事件编号
            newSettlementCostInfo.setEventSourceCode(eventJob.getPushEventCode());

            //事件信息
            newSettlementCostInfo.setSettlementEventCode(handlerEventType().getEventCode());
            newSettlementCostInfo.setSettlementEventDesc(handlerEventType().getEventDesc());
            newSettlementCostInfo.setCommissionType(x.getCommissionType());
            newSettlementCostInfo.setSettlementGenerateType(2);
            //是否冲正
            //业务记账时间处理
            newSettlementCostInfo.setBusinessAccountTime(
                SettlementCostProcessServiceImpl.getCorrectionBusinessAccountTime(x.getBusinessAccountTime(),eventJob.getCreateTime()));
            newSettlementCostInfo.setSourceCostCode(x.getCostCode());
            newSettlementCostInfo.setCorrectionTime(new Date());
            newSettlementCostInfo.setCorrectionFlag(0);

            newSettlementCostInfo.setCorrectionOpType(1);
            if (StringUtil.isNotBlank(changePolicyData.getBusinessDesc())) {
                newSettlementCostInfo.setCorrectionRemark(changePolicyData.getBusinessDesc());
            }
            //清除确认信息
            newSettlementCostInfo.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
            newSettlementCostInfo.setConfirmUser(null);
            newSettlementCostInfo.setConfirmTime(null);
            newSettlementCostInfo.setConfirmGrantTime(null);
            newSettlementCostInfo.setDocumentCode(null);
            newSettlementCostInfo.setAutoCostCode(null);
            newSettlementCostInfo.setCostSettlementCycle(null);
            newSettlementCostInfo.setBusinessDiscountPremium(x.getBusinessDiscountPremium());
            //生成一条对冲信息
            SettlementCostInfoEntity correctionSettlementCostInfoEntity =
                settlementCostProcessService.builderOffsetCostInfo(eventJob, handlerEventType(), x,eventJob.getCreateTime(),
                    SYSTEM_CORRECTION_USER, "批单号变更冲正", Boolean.TRUE);
            return Lists.newArrayList(correctionSettlementCostInfoEntity, newSettlementCostInfo);

        }).flatMap(Collection::stream).collect(Collectors.toList());

        costCorrectionDto.setNewCostList(newCostList);
        costCorrectionDto.setCorrectionOldIds(
            costInfoEntityList.stream().map(SettlementCostInfoEntity::getId).collect(Collectors.toList()));
        costCorrectionDto.setPolicy(costPolicy);

        settlementCostCorrectionService.saveCostCommissionRecord(eventJob, costCorrectionDto);
        policyCenterBaseClient.governApplyConfirm(eventJob.getEventBusinessCode(), "settlement_server", "支出端");
        return "success";
    }

    @Override
    public SettlementEventTypeEnum handlerEventType() {
        return SettlementEventTypeEnum.CHANGE_POLICY_CODE;
    }
}
