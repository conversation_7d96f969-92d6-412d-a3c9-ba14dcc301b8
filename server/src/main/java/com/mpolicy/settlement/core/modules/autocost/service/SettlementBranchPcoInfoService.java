package com.mpolicy.settlement.core.modules.autocost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementBranchDirectorInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementBranchPcoInfoEntity;

import java.util.List;

public interface SettlementBranchPcoInfoService extends IService<SettlementBranchPcoInfoEntity> {
    /**
     * 批量插入结算生服对接人信息
     * @param listData
     * @return
     */
    int saveList(List<SettlementBranchPcoInfoEntity> listData);
}
