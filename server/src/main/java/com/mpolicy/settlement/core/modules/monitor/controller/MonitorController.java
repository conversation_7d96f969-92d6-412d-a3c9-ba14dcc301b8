package com.mpolicy.settlement.core.modules.monitor.controller;

import com.alibaba.fastjson.JSON;
import com.mpolicy.common.result.Result;
import com.mpolicy.settlement.core.controller.BaseController;
import com.mpolicy.settlement.core.modules.monitor.dto.CostCalcFailNotifyDTO;
import com.mpolicy.settlement.core.modules.monitor.dto.PolicySyncFailNotifyDTO;
import com.mpolicy.settlement.core.modules.monitor.job.SettlementCostFailHandler;
import com.mpolicy.settlement.core.modules.monitor.service.yida.YidaSyncAndNotifyRepo;
import com.mpolicy.settlement.core.modules.monitor.service.yida.YidaSyncCostAndNotifyRepo;
import com.mpolicy.web.common.annotation.PassToken;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 监控模块控制器
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Slf4j
@RestController
@RequestMapping("/monitor")
@Api(tags = "监控模块")
public class MonitorController extends BaseController {

    @Autowired
    YidaSyncCostAndNotifyRepo yidaSyncCostAndNotifyRepo;
    @Autowired
    private YidaSyncAndNotifyRepo yidaSyncAndNotifyRepo;

    @Autowired
    SettlementCostFailHandler settlementCostFailHandler;


    @GetMapping("/getNotifyInfoList")
    @ApiOperation("获取结算异常通知配置信息")
    @PassToken
    public Result<String> getNotifyInfoList() {
        // 查询通知配置信息
        log.info("开始获取异常通知配置信息");
        List<CostCalcFailNotifyDTO> notifyInfoList = yidaSyncCostAndNotifyRepo.getNotifyInfoList();
        log.info("获取到的配置信息：{}", JSON.toJSONString(notifyInfoList));
        return Result.success();
    }
    @GetMapping("/syncCostFailDataToYiDa")
    @ApiOperation("同步异常数据到宜搭")
    @PassToken
    public Result<String> syncCostFailDataToYiDa() {
        // 查询通知配置信息
        log.info("开始同步异常数据到宜搭");
        settlementCostFailHandler.settlementCostFailNotifyHandler();
        log.info("同步异常数据到宜搭结束");
        return Result.success();
    }



    @GetMapping("/getPolicyNotifyInfoList")
    @ApiOperation("获取保单异常通知配置信息")
    @PassToken
    public Result<String> getPolicyNotifyInfoList() {
        // 查询通知配置信息
        log.info("获取保单异常通知配置信息");
        List<PolicySyncFailNotifyDTO> notifyInfoList = yidaSyncAndNotifyRepo.getNotifyInfoList();
        log.info("获取到保单的配置信息：{}", JSON.toJSONString(notifyInfoList));
        return Result.success();
    }
}
