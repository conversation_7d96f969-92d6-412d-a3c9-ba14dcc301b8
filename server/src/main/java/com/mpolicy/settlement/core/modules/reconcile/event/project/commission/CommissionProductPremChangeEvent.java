package com.mpolicy.settlement.core.modules.reconcile.event.project.commission;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostCorrectionDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.common.AbsSettlementEvent;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventCheckResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.mpolicy.settlement.core.common.Constant.MAX_QUERY_SIZE;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/24 3:26 下午
 * @Version 1.0
 */
@Service
@Slf4j
public class CommissionProductPremChangeEvent extends AbsSettlementEvent {
    @Override
    public HandleEventCheckResult handleIncomeEventCheck(SettlementEventJobEntity eventJob,Integer reconcileType) {
        return HandleEventCheckResult.builder().checkStatus(false).checkMsg("基础佣金一单一议变更不影响收入").build();
    }

    @Override
    public String handleIncomeEvent(SettlementEventJobEntity eventJob, HandleEventData eventData) {
        return "支出端佣金配置变更业务不处理-success";
    }

    @Override
    public HandleEventCheckResult handleCostEventCheck(SettlementEventJobEntity eventJob) {
        // 检查是否已存在纪录
        Integer count = settlementCostInfoService.countEventCostInfoByEventSourceCode(eventJob.getPushEventCode());
        log.info("支出端-佣金配置变更事件，检查是否已存在纪录:{}，eventJob={}",count, eventJob);
        // 如果存在纪录
        if (count > 0) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg(StrUtil.format("支出端-该佣金配置变更编码已存在，保单号={}", eventJob.getPushEventCode())).build();
        }
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleCostEvent(SettlementEventJobEntity eventJob, HandleEventData eventData) {

        JSONObject param = JSONObject.parseObject(eventJob.getEventRequest());
        JSONArray deleteArr = param.getJSONArray("removePremCodeList");
        JSONArray updateArr = param.getJSONArray("updatePremCodeList");
        Date newBusinessTime = param.get("opeTime")!=null?param.getDate("opeTime"):null;
        if(CollectionUtils.isEmpty(deleteArr) && CollectionUtils.isEmpty(updateArr)){
            log.warn(StrUtil.format("支出端-佣金配置变更事件，没有被删除的key和更新的key，报文如下 ={}",eventJob.getEventRequest()));
            return "没有被删除的key和更新的key-success";
        }
        List<String> keys = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(deleteArr)) {
            keys.addAll(deleteArr.toJavaList(String.class));
        }
        if(CollectionUtils.isNotEmpty(updateArr)){
            keys.addAll(updateArr.toJavaList(String.class));
        }

        List<String> contractCodes = settlementCostInfoService.listContractCodeByCostConfigKeys(keys);
        if(CollectionUtils.isEmpty(contractCodes)){
            log.warn(StrUtil.format("支出端-佣金配置变更事件，被删除的和更新的key没有佣金记录，keys ={}", keys));
            return "删除的key没有佣金记录-success";
        }

        int end = contractCodes.size();
        List<String> errors = Lists.newArrayList();
        for(int i = 0; i < end; ) {
            List<String> codes =  contractCodes.subList(i, Math.min(i + MAX_QUERY_SIZE, end));
            try {
                CostCorrectionDto dto = settlementCostCorrectionService.builderConfigChangeCostInfo(eventJob,handlerEventType(),newBusinessTime,codes,keys);
                settlementCostCorrectionService.saveCostCommissionRecord(eventJob,dto);

            }catch(Exception e){
                log.warn("佣金费率变更失败的合同号={}",codes,e);
                errors.addAll(codes);
            }
            i = i + MAX_QUERY_SIZE;
        }
        if(CollectionUtils.isNotEmpty(errors)){
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("支出端-佣金配置费率变更事件{}，处理失败的合同编号={}", eventJob.getPushEventCode(),errors)));
        }
        return "success";
    }

    @Override
    public SettlementEventTypeEnum handlerEventType() {
        return SettlementEventTypeEnum.COMMISSION_PRODUCT_PREM_CHANGE;
    }


}
