package com.mpolicy.settlement.core.modules.autocost.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.Result;
import com.mpolicy.open.common.bms.req.UserPageReq;
import com.mpolicy.open.common.bms.resp.UserPageResp;
import com.mpolicy.open.common.cfpamf.hr.*;
import com.mpolicy.open.common.hr.vo.EmployeeLdomVO;
import com.mpolicy.open.common.hr.vo.OrganizationLdomVO;
import com.mpolicy.settlement.core.modules.autocost.dao.EmployeeDao;
import com.mpolicy.settlement.core.modules.autocost.dto.employee.EmployeeInfo;
import com.mpolicy.settlement.core.modules.autocost.entity.DimInsuranceSpvsrManageProtectionDfpEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementBranchDirectorInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostQbiOrgEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostUploadPcoInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.enums.EmployeeRoleEnum;
import com.mpolicy.settlement.core.modules.autocost.service.*;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.service.common.OpenApiBaseService;
import com.netflix.discovery.converters.Auto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.MAX_BATCH_UPDATE_NUM;

/**
 * 中和农信员工服务接口实现
 * 文档：https://cfpamf.yuque.com/xg4y3f/voxwu3/vd2axn9cwepsvp9f
 *
 * <AUTHOR>
 * @since 2023-10-31 22:12
 */
@Slf4j
@Service
public class EmployeeServiceImpl implements EmployeeService {

    @Autowired
    private EmployeeDao employeeDao;

    @Autowired
    private OpenApiBaseService openApiBaseService;

    @Autowired
    private DimInsuranceSpvsrManageProtectionDfpService dimInsuranceSpvsrManageProtectionDfpService;

    @Autowired
    private SettlementCostQbiOrgService settlementCostQbiOrgService;

    @Autowired
    private SettlementBranchDirectorInfoService settlementBranchDirectorInfoService;

    @Autowired
    private SettlementCostUploadPcoInfoService settlementCostUploadPcoInfoService;

    @Autowired
    private BranchService branchService;

    public static final int PAGE_SIZE = 200;

    /**
     * 获取快照周期年份
     *
     * @param monthSnapshot 快照周期
     * @return 快照年份
     * <AUTHOR>
     * @since 2023/11/18 11:09
     */
    private Integer getSnapshotYear(String monthSnapshot) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date date = null;
        try {
            date = sdf.parse(monthSnapshot);
        } catch (ParseException e) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("格式无法进行转换日期", monthSnapshot)));
        }
        return Integer.parseInt(DateUtil.format(DateUtil.offsetMonth(date, -1), "yyyy"));
    }

    /**
     * 获取快照周期月份
     *
     * @param monthSnapshot 快照周期
     * @return 快照月份
     * <AUTHOR>
     * @since 2023/11/18 11:09
     */
    private Integer getSnapshotMonth(String monthSnapshot) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date date = null;
        try {
            date = sdf.parse(monthSnapshot);
        } catch (ParseException e) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("格式无法进行转换日期", monthSnapshot)));
        }
        return Integer.parseInt(DateUtil.format(DateUtil.offsetMonth(date, -1), "MM"));
    }

    /**
     * 根据openApi员工信息获取结算员工信息
     *
     * @param employee openApi员工信息
     * @return 结算员工dto信息
     * <AUTHOR>
     * @since 2023/11/18 11:28
     */
    private EmployeeInfo buildEmployeeInfo(EmployeeLdomVO employee) {
        EmployeeInfo result = new EmployeeInfo();
        BeanUtils.copyProperties(employee, result);
        // 设置员工id
        if (employee.getEmployeeId() != null) {
            result.setEmployeeId(employee.getEmployeeId().intValue());
        }
        // 设置员工岗位信息
        if (StringUtils.isNotBlank(employee.getJobPositionCode())) {
            result.setJobId(employee.getJobPositionCode());
        }
        result.setJobName(employee.getJobPositionName());
        // 设置员工部门分支信息
        result.setOrgCode(employee.getBranchCode());
        result.setOrgName(employee.getBranchName());
        // 设置员工区域、片区信息
        if (employee.getZoneId() != null) {
            result.setZoneId(employee.getZoneId().intValue());
        }
        if (employee.getRegionId() != null) {
            result.setRegionId(employee.getRegionId().intValue());
        }
        // 设置员工督导信息、直线经理
        if (employee.getSupervisorId() != null) {
            result.setSupervisorId(employee.getSupervisorId().intValue());
        }
        if (employee.getLeaderEmployeeId() != null) {
            result.setLeaderEmployeeId(employee.getLeaderEmployeeId().intValue());
        }
        return result;
    }

    @Override
    public EmployeeInfo queryEmployeeInfo(String employeeNo) {
        // 1 调用openApi获取员工信息
        EmployeeByCodeRequest request = new EmployeeByCodeRequest();
        request.setEmployeeCode(employeeNo);
        request.setIncludeLevelFlag(true);
        EmployeeLdomVO employee = openApiBaseService.getByEmployeeCode(request, true);
        log.info("openApi获取员工信息：{}", employee);
        // 2 构建响应dto员工内容
        return buildEmployeeInfo(employee);
    }

    @Override
    public EmployeeInfo queryEmployeeInfoSnapshot(String employeeNo, String monthSnapshot) {
        // 1 调用openApi获取员工信息 + 快照信息
        EmployeeByCodeRequest request = new EmployeeByCodeRequest();
        request.setEmployeeCode(employeeNo);
        request.setYear(getSnapshotYear(monthSnapshot));
        request.setMonth(getSnapshotMonth(monthSnapshot));
        request.setIncludeLevelFlag(true);
        EmployeeLdomVO employee = openApiBaseService.getByEmployeeCode(request, true);
        // 2 构建响应dto员工内容
        return buildEmployeeInfo(employee);
    }

    @Override
    public List<EmployeeInfo> queryEmployeeAllInfo() {
        List<EmployeeInfo> result = new ArrayList<>();
        EmployeeTotalRequest request = new EmployeeTotalRequest();
        request.setIncludeLevelFlag(true);
        Integer page = 1;
        // 2 业务处理
        while (true) {
            request.setPage(page);
            request.setSize(PAGE_SIZE);
            // 2-1 分页获取分支信息
            List<EmployeeLdomVO> employeeList = openApiBaseService.queryAllEmployeesByPage(request, true);
            if (employeeList.isEmpty()) {
                break;
            }
            result.addAll(employeeList.stream().filter(x -> StringUtils.isNotBlank(x.getEmployeeCode())).map(this::buildEmployeeInfo).collect(Collectors.toList()));
            page++;
        }
        return result;
    }

    @Override
    public List<EmployeeInfo> queryEmployeeAllSnapshot(String monthSnapshot) {
        List<EmployeeInfo> result = new ArrayList<>();
        // 1 构建查询条件
        EmployeeTotalRequest request = new EmployeeTotalRequest();
        request.setYear(getSnapshotYear(monthSnapshot));
        request.setMonth(getSnapshotMonth(monthSnapshot));
        request.setIncludeLevelFlag(true);
        Integer page = 1;
        Set<String> existSet = Sets.newHashSet();
        // 2 业务处理
        while (true) {
            request.setPage(page);
            request.setSize(PAGE_SIZE);
            // 2-1 分页获取分支信息
            List<EmployeeLdomVO> employeeList = openApiBaseService.queryAllEmployeesByPage(request, true);
            if (employeeList.isEmpty()) {
                break;
            }
            //过滤掉职位编码为2000的，信贷员（停用）
            result.addAll(employeeList.stream().filter(x -> StringUtils.isNotBlank(x.getEmployeeCode()) && !Objects.equals(x.getJobPositionCode(),"2000")).map(this::buildEmployeeInfo).collect(Collectors.toList()));
            page++;
        }
        existSet = result.stream().map(EmployeeInfo::getEmployeeCode).collect(Collectors.toSet());

        // 非hr员工查询
        result.addAll(queryInformalUserPage(existSet));

        return result;
    }

    @Override
    public List<EmployeeInfo> listEmployeeByEmployeeCodes(List<String> employeeCodes) {
        if(CollectionUtils.isEmpty(employeeCodes)){
            return Collections.EMPTY_LIST;
        }
        EmployeeByCodesRequest request = new EmployeeByCodesRequest();
        request.setEmployeeCodes(employeeCodes);
        request.setIncludeLevelFlag(true);
        List<EmployeeLdomVO> employeeList = openApiBaseService.queryByEmployeeCodes(request, true);
        return employeeList.stream().map(this::buildEmployeeInfo).collect(Collectors.toList());
    }

    @Override
    public Map<String, EmployeeInfo> mapEmployeeByEmployeeCodes(List<String> employeeCodes) {
        if(CollectionUtils.isEmpty(employeeCodes)){
            return Collections.EMPTY_MAP;
        }
        List<EmployeeInfo> list = this.listEmployeeByEmployeeCodes(employeeCodes);
        return list.stream().collect(Collectors.toMap(EmployeeInfo::getEmployeeCode, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public List<EmployeeInfo> listEmployeeByEmployeeCodesSnapshot(List<String> employeeCodes, String monthSnapshot) {
        if(CollectionUtils.isEmpty(employeeCodes)){
            return Collections.EMPTY_LIST;
        }
        EmployeeByCodesRequest request = new EmployeeByCodesRequest();
        request.setEmployeeCodes(employeeCodes);
        request.setYear(getSnapshotYear(monthSnapshot));
        request.setMonth(getSnapshotMonth(monthSnapshot));
        request.setIncludeLevelFlag(true);
        return listEmployeeByEmployeeByCodesRequest(request);
    }

    private List<EmployeeInfo> listEmployeeByEmployeeByCodesRequest(EmployeeByCodesRequest request){
        List<EmployeeLdomVO> employeeList = openApiBaseService.queryByEmployeeCodes(request, true);
        return employeeList.stream().map(this::buildEmployeeInfo).collect(Collectors.toList());
    }

    @Override
    public Map<String, EmployeeInfo> mapEmployeeByEmployeeCodesSnapshot(List<String> employeeCodes, String monthSnapshot) {
        List<EmployeeInfo> list = this.listEmployeeByEmployeeCodesSnapshot(employeeCodes, monthSnapshot);
        return list.stream().collect(Collectors.toMap(EmployeeInfo::getEmployeeCode, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public List<EmployeeInfo> listEmployeeByEmployeeRole(EmployeeRoleEnum employeeRole, Boolean mainJobFlag) {
        List<EmployeeInfo> result = new ArrayList<>();
        // 1 构建岗位获取员工集合
        EmployeeByPostingCodesRequest request = new EmployeeByPostingCodesRequest();
        request.setMainJobFlag(mainJobFlag);
        request.setPostingCodes(Collections.singletonList(String.valueOf(employeeRole.getJobId())));
        Map<String, List<EmployeeLdomVO>> stringListMap = openApiBaseService.queryByPostingCodes(request, true);
        // 2 构建List<EmployeeInfo>
        if (!stringListMap.isEmpty()) {
            stringListMap.forEach((k, v) -> result.addAll(v.stream().map(this::buildEmployeeInfo).collect(Collectors.toList())));
        }
        return result;
    }

    @Override
    public Map<String, EmployeeInfo> mapEmployeeByEmployeeRole(EmployeeRoleEnum employeeRole, Boolean mainJobFlag) {
        List<EmployeeInfo> list = this.listEmployeeByEmployeeRole(employeeRole, mainJobFlag);
        return list.stream().collect(Collectors.toMap(EmployeeInfo::getEmployeeCode, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public List<EmployeeInfo> listEmployeeByEmployeeRoleSnapshot(EmployeeRoleEnum employeeRole, Boolean mainJobFlag, String monthSnapshot) {
        // 2024-06-24 modify by zhangjian pco从hr获取的数据存在问题，与产品、业务沟通从子旭的机构指标表中获取对应信息，
        // 后续可能会作一个导入功能工
        if(Objects.equals(EmployeeRoleEnum.PCO.getCode(),employeeRole.getCode())){
            return listPcoEmployeeFromUploadPcoInfo(monthSnapshot);
        }else if(Objects.equals(EmployeeRoleEnum.SUPERVISOR.getCode(),employeeRole.getCode())){
            //2025-03-13 督导数据直接从及数仓的督导管辖关系表获取
            return listSupervisorEmployeeFromSpvsrManageProtectionDfpV2(monthSnapshot);
        }else {
            // 1 构建岗位获取员工集合
            List<EmployeeInfo> result = new ArrayList<>();
            EmployeeByPostingCodesRequest request = new EmployeeByPostingCodesRequest();
            request.setYear(getSnapshotYear(monthSnapshot));
            request.setMonth(getSnapshotMonth(monthSnapshot));
            request.setMainJobFlag(mainJobFlag);
            request.setPostingCodes(Collections.singletonList(String.valueOf(employeeRole.getJobId())));
            Map<String, List<EmployeeLdomVO>> stringListMap = openApiBaseService.queryByPostingCodes(request, true);
            // 2 构建List<EmployeeInfo>
            if (!stringListMap.isEmpty()) {
                stringListMap.forEach((k, v) -> result.addAll(v.stream().map(this::buildEmployeeInfo).collect(Collectors.toList())));
            }
            return result;
        }

    }

    /**
     *
     * 获取所有机构负责人（当机构没有负责人时，默认为""）
     * @param monthSnapshot
     * @return
     */
    public List<EmployeeInfo> listAllBranchDirectorEmployeeBySnapshot(String monthSnapshot){
        if(StringUtils.isBlank(monthSnapshot)){
            log.warn("");
            return Collections.EMPTY_LIST;
        }

        List<OrganizationLdomVO> orgList = branchService.queryBranchAllSnapshot(monthSnapshot);
        if(CollectionUtils.isEmpty(orgList)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("获取分支记录为空，请核对，monthSnapshot={}", monthSnapshot)));
        }

        String pt = getPcoSnapshotMonth(monthSnapshot);
        List<SettlementBranchDirectorInfoEntity> directorList = settlementBranchDirectorInfoService.lambdaQuery()
                .eq(SettlementBranchDirectorInfoEntity::getGenerateDate,pt)
                .list();
        Map<String,SettlementBranchDirectorInfoEntity> directorMap = directorList.stream().collect(Collectors.toMap(SettlementBranchDirectorInfoEntity::getBchCode, Function.identity(), (k1, k2) -> k1));

        List<EmployeeInfo> result = orgList.stream().map(b->{
            EmployeeInfo employee = new EmployeeInfo();
            if(directorMap.containsKey(b.getBranchCode())){
                SettlementBranchDirectorInfoEntity o = directorMap.get(b.getBranchCode());
                BeanUtils.copyProperties(o,employee);
                employee.setOrgCode(o.getBchCode());
                employee.setOrgName(o.getBchName());
                if(o.getJobId()!=null) {
                    employee.setJobId(o.getJobId() + "");
                }
            }else{
                employee.setEmployeeCode("");
                employee.setEmployeeName("");
                employee.setOrgCode(b.getBranchCode());
                employee.setOrgName(b.getBranchName());
            }
            return employee;

        }).collect(Collectors.toList());
        return result;
    }

    /**
     * 获取机构负责人（当分支负责人不存在，则这个分支信息就不存在）
     * @param monthSnapshot
     * @return
     */
    public List<EmployeeInfo> listBranchDirectorEmployeeBySnapshot(String monthSnapshot){
        if(StringUtils.isBlank(monthSnapshot)){
            log.warn("");
            return Collections.EMPTY_LIST;
        }
        String pt = getPcoSnapshotMonth(monthSnapshot);
        List<SettlementBranchDirectorInfoEntity> orgQbi = settlementBranchDirectorInfoService.lambdaQuery()
                .eq(SettlementBranchDirectorInfoEntity::getGenerateDate,pt)
                .list();
        if(CollectionUtils.isEmpty(orgQbi)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("获取分支负责人记录为空，请核对，pt={}", pt)));
        }
        List<EmployeeInfo> result = orgQbi.stream().map(o->{
            EmployeeInfo employee = new EmployeeInfo();
            BeanUtils.copyProperties(o,employee);
            employee.setOrgCode(o.getBchCode());
            employee.setOrgName(o.getBchName());
            employee.setJobId(o.getJobId()+"");
            return employee;
        }).collect(Collectors.toList());
        return result;
    }

    public List<EmployeeInfo> listPcoEmployeeFromSettlementCostQbiOrg(String monthSnapshot){
        if(StringUtils.isBlank(monthSnapshot)){
            log.warn("");
            return Collections.EMPTY_LIST;
        }
        String pt = getPcoSnapshotMonth(monthSnapshot);
        List<SettlementCostQbiOrgEntity> orgQbi = settlementCostQbiOrgService.lambdaQuery()
                .eq(SettlementCostQbiOrgEntity::getPt,pt)
                .ne(SettlementCostQbiOrgEntity::getPcoEmployeeCode,"")
                .list();
        if(CollectionUtils.isEmpty(orgQbi)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("获取pco记录为空，请核对，pt={}", pt)));
        }
        List<EmployeeInfo> result = orgQbi.stream().map(o->{
            EmployeeInfo employee = new EmployeeInfo();
            employee.setEmployeeCode(o.getPcoEmployeeCode());
            employee.setEmployeeName(o.getPcoEmployeeName());
            employee.setOrgCode(o.getOrgCode());
            employee.setOrgName(o.getOrgName());
            employee.setJobId(EmployeeRoleEnum.PCO.getJobId());
            employee.setJobName(EmployeeRoleEnum.PCO.getName());
            return employee;
        }).collect(Collectors.toList());
        return result;
    }

    public List<EmployeeInfo> listPcoEmployeeFromUploadPcoInfo(String monthSnapshot){
        if(StringUtils.isBlank(monthSnapshot)){
            log.warn("");
            return Collections.EMPTY_LIST;
        }
        String pt = getUploadPcoInfoSnapshotMonth(monthSnapshot);
        List<SettlementCostUploadPcoInfoEntity> orgQbi = settlementCostUploadPcoInfoService.lambdaQuery()
                .eq(SettlementCostUploadPcoInfoEntity::getOpMonth,pt)
                .list();
        if(CollectionUtils.isEmpty(orgQbi)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("获取pco记录为空，请核对，pt={}", pt)));
        }
        List<EmployeeInfo> result = orgQbi.stream().map(o->{
            EmployeeInfo employee = new EmployeeInfo();
            employee.setEmployeeCode(o.getEmployeeCode());
            employee.setEmployeeName(o.getEmployeeName());
            employee.setOrgCode(o.getOrgCode());
            employee.setOrgName(o.getOrgName());
            employee.setPcoLevel(o.getPcoLevel());
            employee.setPerCapitaCount(o.getPerCapitaCount());
            employee.setClaimTimelinessQualifyFlag(o.getClaimTimelinessQualifyFlag());
            employee.setBusinessKnowledgeQualifyFlag(o.getBusinessKnowledgeQualifyFlag());
            employee.setJobId(EmployeeRoleEnum.PCO.getJobId());
            employee.setJobName(EmployeeRoleEnum.PCO.getName());

            return employee;
        }).collect(Collectors.toList());
        return result;
    }

    /**
     * 2025-03-13 督导信息直接从表
     * @param monthSnapshot
     * @return
     */
    public List<EmployeeInfo> listSupervisorEmployeeFromSpvsrManageProtectionDfpV2(String monthSnapshot){
        String superSnapshotMonth = getSuperSnapshotMonth(monthSnapshot);
        List<DimInsuranceSpvsrManageProtectionDfpEntity> dfpList = dimInsuranceSpvsrManageProtectionDfpService.listSpvsrInfoBySnapshotMonth(superSnapshotMonth);
        if(CollectionUtils.isEmpty(dfpList)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("获取督导记录为空，请核对，snapshotMonth={}", superSnapshotMonth)));
        }
        return dfpList.stream().map(d->{
            EmployeeInfo employeeInfo = new EmployeeInfo();
            employeeInfo.setEmployeeCode(d.getSpvsrId());
            employeeInfo.setEmployeeName(d.getSpvsrName());
            employeeInfo.setOrgName(d.getBchName());
            employeeInfo.setOrgCode(d.getBchCode());
            employeeInfo.setJobId(EmployeeRoleEnum.SUPERVISOR.getJobId());
            return employeeInfo;
        }).collect(Collectors.toList());

    }
    public List<EmployeeInfo> listSupervisorEmployeeFromSpvsrManageProtectionDfp(String monthSnapshot){
        String superSnapshotMonth = getSuperSnapshotMonth(monthSnapshot);

        List<String>  superCodes =   dimInsuranceSpvsrManageProtectionDfpService.listSpvsrCodeBySnapshotMonth(superSnapshotMonth);
        if(CollectionUtils.isEmpty(superCodes)){
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("获取督导记录为空，请核对，snapshotMonth={}", superSnapshotMonth)));
        }
        List<EmployeeInfo> result = Lists.newArrayList();

        EmployeeByCodesRequest request = new EmployeeByCodesRequest();
        request.setYear(getSnapshotYear(monthSnapshot));
        request.setMonth(getSnapshotMonth(monthSnapshot));
        request.setIncludeLevelFlag(true);
        if (superCodes.size() > PAGE_SIZE) {
            List<List<String>> partition = ListUtils.partition(superCodes, PAGE_SIZE);
            for (List<String> x : partition) {
                request.setEmployeeCodes(x);
                result.addAll(builderSupervisorInfo(request));
            }
        } else {
            request.setEmployeeCodes(superCodes);
            result = builderSupervisorInfo(request);
        }
        return result;
    }

    private List<EmployeeInfo> builderSupervisorInfo(EmployeeByCodesRequest request){
        List<EmployeeLdomVO> employeeList = openApiBaseService.queryByEmployeeCodes(request, true);
        if(CollectionUtils.isEmpty(employeeList)){
            return Collections.EMPTY_LIST;
        }

        Map<String,EmployeeInfo> result = Maps.newHashMap();

        for(EmployeeLdomVO resp : employeeList){
            log.info("督导信息:{}", JSON.toJSONString(resp));
            //if(result.containsKey(resp.getEmployeeCode())){
            if(Objects.equals(resp.getServiceType(),0)
                    && (Objects.equals(resp.getJobPositionCode(),EmployeeRoleEnum.SUPERVISOR.getJobId())
                        || Objects.equals(resp.getJobPositionCode(),EmployeeRoleEnum.DIRECTOR_ASSISTANT.getJobId())
                        || Objects.equals(resp.getJobPositionCode(),EmployeeRoleEnum.SUPERVISOR_AND_INTERNAL_AFFAIRS.getJobId()))){
                result.put(resp.getEmployeeCode(),buildEmployeeInfo(resp));
            }
            //}

        }
        return result.values().stream().collect(Collectors.toList());
    }



    @Override
    public Map<String, EmployeeInfo> mapEmployeeByEmployeeRoleSnapshot(EmployeeRoleEnum employeeRole, Boolean mainJobFlag, String monthSnapshot) {
        List<EmployeeInfo> list = this.listEmployeeByEmployeeRoleSnapshot(employeeRole, mainJobFlag, monthSnapshot);
        return list.stream().collect(Collectors.toMap(EmployeeInfo::getEmployeeCode, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public List<EmployeeInfo> listEmployeeByEmployeeRole(List<EmployeeRoleEnum> employeeRoleList, Boolean mainJobFlag) {
        List<EmployeeInfo> result = new ArrayList<>();
        // 1 构建岗位获取员工集合
        EmployeeByPostingCodesRequest request = new EmployeeByPostingCodesRequest();
        request.setMainJobFlag(mainJobFlag);
        request.setPostingCodes(employeeRoleList.stream().map(x -> String.valueOf(x.getJobId())).collect(Collectors.toList()));
        Map<String, List<EmployeeLdomVO>> stringListMap = openApiBaseService.queryByPostingCodes(request, true);
        // 2 构建List<EmployeeInfo>
        if (!stringListMap.isEmpty()) {
            stringListMap.forEach((k, v) -> {
                result.addAll(v.stream().map(this::buildEmployeeInfo).collect(Collectors.toList()));
                result.forEach(p -> p.setJobId(k));
            });
        }
        return result;
    }



    @Override
    public Map<String, EmployeeInfo> mapEmployeeByEmployeeRole(List<EmployeeRoleEnum> employeeRoleList, Boolean mainJobFlag) {
        List<EmployeeInfo> list = this.listEmployeeByEmployeeRole(employeeRoleList, mainJobFlag);
        return list.stream().collect(Collectors.toMap(EmployeeInfo::getEmployeeCode, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public List<EmployeeInfo> listEmployeeByEmployeeRoleSnapshot(List<EmployeeRoleEnum> employeeRoleList, Boolean mainJobFlag, String monthSnapshot) {
        List<EmployeeInfo> result = new ArrayList<>();
        // 1 构建岗位获取员工集合
        EmployeeByPostingCodesRequest request = new EmployeeByPostingCodesRequest();
        request.setYear(getSnapshotYear(monthSnapshot));
        request.setMonth(getSnapshotMonth(monthSnapshot));
        request.setMainJobFlag(mainJobFlag);
        request.setPostingCodes(employeeRoleList.stream().map(x -> String.valueOf(x.getJobId())).collect(Collectors.toList()));
        Map<String, List<EmployeeLdomVO>> stringListMap = openApiBaseService.queryByPostingCodes(request, true);
        // 2 构建List<EmployeeInfo>
        if (!stringListMap.isEmpty()) {
            stringListMap.forEach((k, v) -> {
                result.addAll(v.stream().map(this::buildEmployeeInfo).collect(Collectors.toList()));
                result.forEach(p -> p.setJobId(k));
            });
        }
        return result;
    }

    @Override
    public Map<String, EmployeeInfo> mapEmployeeByEmployeeRoleSnapshot(List<EmployeeRoleEnum> employeeRoleList, Boolean mainJobFlag, String monthSnapshot) {
        List<EmployeeInfo> list = this.listEmployeeByEmployeeRoleSnapshot(employeeRoleList, mainJobFlag, monthSnapshot);
        return list.stream().collect(Collectors.toMap(EmployeeInfo::getEmployeeCode, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public List<EmployeeInfo> listEmployeeByRegion(String regionCode) {
        List<EmployeeInfo> result = new ArrayList<>();
        EmployeeByOrgCodeRequest request = new EmployeeByOrgCodeRequest();
        request.setRegionCode(regionCode);
        request.setYear(null);
        request.setMonth(null);
        Integer page = 1;
        // 2 业务处理
        while (true) {
            request.setPage(page);
            request.setSize(PAGE_SIZE);
            // 2-1 分页获取分支信息
            List<EmployeeLdomVO> resList = openApiBaseService.getByEmployeeListByPage(request, true);
            if (resList.isEmpty()) {
                break;
            }
            result.addAll(resList.stream().filter(x -> StringUtils.isNotBlank(x.getEmployeeCode())).map(this::buildEmployeeInfo).collect(Collectors.toList()));
            page++;
        }
        return result;
    }

    @Override
    public List<EmployeeInfo> listEmployeeByRegionSnapshot(String regionCode, String monthSnapshot) {
        List<EmployeeInfo> result = new ArrayList<>();
        EmployeeByOrgCodeRequest request = new EmployeeByOrgCodeRequest();
        request.setRegionCode(regionCode);
        request.setYear(getSnapshotYear(monthSnapshot));
        request.setMonth(getSnapshotMonth(monthSnapshot));
        Integer page = 1;
        // 2 业务处理
        while (true) {
            request.setPage(page);
            request.setSize(PAGE_SIZE);
            // 2-1 分页获取分支信息
            List<EmployeeLdomVO> resList = openApiBaseService.getByEmployeeListByPage(request, true);
            if (resList.isEmpty()) {
                break;
            }
            result.addAll(resList.stream().filter(x -> StringUtils.isNotBlank(x.getEmployeeCode())).map(this::buildEmployeeInfo).collect(Collectors.toList()));
            page++;
        }
        return result;
    }

    @Override
    public Map<String, EmployeeInfo> mapEmployeeByRegion(String regionCode) {
        List<EmployeeInfo> list = this.listEmployeeByRegion(regionCode);
        return list.stream().collect(Collectors.toMap(EmployeeInfo::getEmployeeCode, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public Map<String, EmployeeInfo> mapEmployeeByRegionSnapshot(String regionCode, String monthSnapshot) {
        List<EmployeeInfo> list = this.listEmployeeByRegionSnapshot(regionCode, monthSnapshot);
        return list.stream().collect(Collectors.toMap(EmployeeInfo::getEmployeeCode, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public List<EmployeeInfo> listEmployeeByOrg(String orgCode) {
        List<EmployeeInfo> result = new ArrayList<>();
        EmployeeByOrgCodeRequest request = new EmployeeByOrgCodeRequest();
        request.setBranchCode(orgCode);
        request.setYear(null);
        request.setMonth(null);
        Integer page = 1;
        // 2 业务处理
        while (true) {
            request.setPage(page);
            request.setSize(PAGE_SIZE);
            // 2-1 分页获取分支信息
            List<EmployeeLdomVO> resList = openApiBaseService.getByEmployeeListByPage(request, true);
            if (resList.isEmpty()) {
                break;
            }
            result.addAll(resList.stream().filter(x -> StringUtils.isNotBlank(x.getEmployeeCode())).map(this::buildEmployeeInfo).collect(Collectors.toList()));
            page++;
        }
        return result;
    }

    @Override
    public List<EmployeeInfo> listEmployeeByOrgSnapshot(String orgCode, String monthSnapshot) {
        List<EmployeeInfo> result = new ArrayList<>();
        EmployeeByOrgCodeRequest request = new EmployeeByOrgCodeRequest();
        request.setBranchCode(orgCode);
        request.setYear(getSnapshotYear(monthSnapshot));
        request.setMonth(getSnapshotMonth(monthSnapshot));
        request.setIncludeLevelFlag(true);
        Integer page = 1;
        // 2 业务处理
        while (true) {
            request.setPage(page);
            request.setSize(PAGE_SIZE);
            // 2-1 分页获取分支信息
            List<EmployeeLdomVO> resList = openApiBaseService.getByEmployeeListByPage(request, true);
            if (resList.isEmpty()) {
                break;
            }
            result.addAll(resList.stream().filter(x -> StringUtils.isNotBlank(x.getEmployeeCode())).map(this::buildEmployeeInfo).collect(Collectors.toList()));
            page++;
        }
        return result;
    }

    @Override
    public Map<String, EmployeeInfo> mapEmployeeByOrg(String orgCode) {
        List<EmployeeInfo> list = this.listEmployeeByOrg(orgCode);
        return list.stream().collect(Collectors.toMap(EmployeeInfo::getEmployeeCode, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public Map<String, EmployeeInfo> mapEmployeeByOrgSnapshot(String orgCode, String monthSnapshot) {
        List<EmployeeInfo> list = this.listEmployeeByOrgSnapshot(orgCode, monthSnapshot);
        return list.stream().collect(Collectors.toMap(EmployeeInfo::getEmployeeCode, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public List<EmployeeInfo> listEmployeeBySupervisor(String supervisorEmployeeNo) {
        EmployeeByCodeRequest request = new EmployeeByCodeRequest();
        request.setEmployeeCode(supervisorEmployeeNo);
        request.setIncludeLevelFlag(true);
        List<EmployeeLdomVO> employeeList = openApiBaseService.queryBySupervisorCode(request, true);
        return employeeList.stream().map(this::buildEmployeeInfo).collect(Collectors.toList());
    }

    @Override
    public List<EmployeeInfo> listEmployeeBySupervisorSnapshot(String supervisorEmployeeNo, String monthSnapshot) {
        EmployeeByCodeRequest request = new EmployeeByCodeRequest();
        request.setEmployeeCode(supervisorEmployeeNo);
        request.setYear(getSnapshotYear(monthSnapshot));
        request.setMonth(getSnapshotMonth(monthSnapshot));
        request.setIncludeLevelFlag(true);
        List<EmployeeLdomVO> employeeList = openApiBaseService.queryBySupervisorCode(request, true);
        return employeeList.stream().map(this::buildEmployeeInfo).collect(Collectors.toList());
    }

    public List<EmployeeInfo> listEmployeeBySuperCodeSnapshot(String supervisorEmployeeNo, String monthSnapshot){

        String superSnapshotMonth = getSuperSnapshotMonth(monthSnapshot);

        List<DimInsuranceSpvsrManageProtectionDfpEntity>  dfpList =   dimInsuranceSpvsrManageProtectionDfpService.listEmployeeBySpvsrCode(supervisorEmployeeNo,superSnapshotMonth);
        if(CollectionUtils.isEmpty(dfpList)){
            return Collections.EMPTY_LIST;
        }

        List<String> employeeCodes = dfpList.stream().map(DimInsuranceSpvsrManageProtectionDfpEntity::getEmpId).collect(Collectors.toList());

        return this.listEmployeeByEmployeeCodesSnapshot(employeeCodes,monthSnapshot);
    }

    public List<EmployeeInfo> listEmployeeByOrgCodeAndSuperCodeSnapshot(String orgCode,String supervisorEmployeeNo, String monthSnapshot){

        String superSnapshotMonth = getSuperSnapshotMonth(monthSnapshot);

        List<DimInsuranceSpvsrManageProtectionDfpEntity>  dfpList =   dimInsuranceSpvsrManageProtectionDfpService.listEmployeeBySpvsrCodeAndBchCode(orgCode,supervisorEmployeeNo,superSnapshotMonth);
        if(CollectionUtils.isEmpty(dfpList)){
            return Collections.EMPTY_LIST;
        }

        List<String> employeeCodes = dfpList.stream().map(DimInsuranceSpvsrManageProtectionDfpEntity::getEmpId).collect(Collectors.toList());

        return this.listEmployeeByEmployeeCodesSnapshot(employeeCodes,monthSnapshot);
    }

    /**
     * 获取督导关系快照周期
     *
     * @param monthSnapshot 快照周期
     * @return 快照月份
     * <AUTHOR>
     * @since 2023/11/18 11:09
     */
    private String getSuperSnapshotMonth(String monthSnapshot) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date date = null;
        try {
            date = sdf.parse(monthSnapshot);
        } catch (ParseException e) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("格式无法进行转换日期", monthSnapshot)));
        }
        return DateUtil.format(DateUtil.offsetMonth(date, -1), "yyyyMM");
    }

    /**
     * 获取督导关系快照周期
     *
     * @param monthSnapshot 快照周期
     * @return 快照月份
     * <AUTHOR>
     * @since 2023/11/18 11:09
     */
    private String getPcoSnapshotMonth(String monthSnapshot) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date date = null;
        try {
            date = sdf.parse(monthSnapshot);
        } catch (ParseException e) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("格式无法进行转换日期", monthSnapshot)));
        }
        return DateUtil.format(DateUtil.offsetDay(date, -1), "yyyyMMdd");
    }

    /**
     * 获取督导关系快照周期
     *
     * @param monthSnapshot 快照周期
     * @return 快照月份
     * <AUTHOR>
     * @since 2023/11/18 11:09
     */
    private String getUploadPcoInfoSnapshotMonth(String monthSnapshot) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date date = null;
        try {
            date = sdf.parse(monthSnapshot);
        } catch (ParseException e) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("格式无法进行转换日期", monthSnapshot)));
        }
        return DateUtil.format(DateUtil.offsetDay(date, -1), "yyyyMM");
    }


    public List<EmployeeInfo>  queryInformalUserPage(Set<String> existSet){
        UserPageReq request = new UserPageReq();
        //request.setEmployeeStatus(12);
        request.setEmployeeType(1);
        Integer page = 1;

        List<EmployeeInfo> result = Lists.newArrayList();
        // 2 业务处理
        while (true) {
            request.setPage(page);
            request.setSize(PAGE_SIZE);
            // 2-1 分页获取分支信息
            List<UserPageResp> employeeList = openApiBaseService.getInformalUserPage(request, true);
            if (employeeList.isEmpty()) {
                break;
            }
            result.addAll(employeeList.stream().filter(x -> StringUtils.isNotBlank(x.getJobNumber()) && !existSet.contains(x.getJobNumber())).map(this::builerInformalUserToEmployeeInfo).collect(Collectors.toList()));
            page++;
        }
        return result;
    }

    public EmployeeInfo builerInformalUserToEmployeeInfo(UserPageResp employee){
        EmployeeInfo result = new EmployeeInfo();
        BeanUtils.copyProperties(employee, result);
        result.setEmployeeCode(employee.getJobNumber());
        // 设置员工id
        result.setEmployeeId(employee.getEmployeeId());
        //设置岗位名称
        result.setJobName(employee.getPostName());
        return result;
    }
}