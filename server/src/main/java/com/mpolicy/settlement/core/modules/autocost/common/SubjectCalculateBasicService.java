package com.mpolicy.settlement.core.modules.autocost.common;

import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.modules.autocost.dto.calculate.CostSubjectCalculateRecord;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostSubjectCalculateRecordEntity;
import com.mpolicy.settlement.core.modules.autocost.enums.DynamicSubjectDataRuleEnum;
import com.mpolicy.settlement.core.modules.autocost.project.calculate.common.AutoCostProcessService;
import com.mpolicy.settlement.core.modules.autocost.project.calculate.common.DynamicSubjectShareService;
import com.mpolicy.settlement.core.modules.autocost.project.calculate.rule.CostSubjectCalculateRuleService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostAutoRecordQueryService;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostSubjectCalculateRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

/**
 * 科目计算基础服务接口
 *
 * <AUTHOR>
 * @since 2023-10-22 13:01
 */
@Slf4j
public class SubjectCalculateBasicService extends SubjectBasicService{

    @Autowired
    protected AutoCostProcessService autoCostProcessService;
    /**
     * 科目计算服务
     */
    @Autowired
    protected SettlementCostSubjectCalculateRecordService settlementCostSubjectCalculateRecordService;

    @Autowired
    protected CostSubjectCalculateRuleService costSubjectCalculateRuleService;
    @Autowired
    protected SettlementCostAutoRecordQueryService costAutoRecordQueryService;

    @Autowired
    protected List<DynamicSubjectShareService> dynamicSubjectShareServices;

    public DynamicSubjectShareService route(DynamicSubjectDataRuleEnum eventEnum) {
        log.info("动态科目分摊规则:{}/{}",eventEnum.getCode(),eventEnum.getName());
        return dynamicSubjectShareServices.stream()
                .filter(x -> x.route(eventEnum))
                .findAny()
                .orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("未发现对应业财数据抽取服务")));
    }




    /**
     * 保存科目计算记录信息
     * @param record
     */
    protected void saveSubjectCalculateRecord(CostSubjectCalculateRecord record) {
        SettlementCostSubjectCalculateRecordEntity entity = new SettlementCostSubjectCalculateRecordEntity();
        BeanUtils.copyProperties(record,entity);
        settlementCostSubjectCalculateRecordService.save(entity);
    }

    protected boolean updateSubjectCalculateRecordStatus(CostSubjectCalculateRecord record){
        return settlementCostSubjectCalculateRecordService.lambdaUpdate()

                .set(SettlementCostSubjectCalculateRecordEntity::getSubjectCalculateStatus,record.getSubjectCalculateStatus())
                .set(SettlementCostSubjectCalculateRecordEntity::getSubjectCalculateDesc,record.getSubjectCalculateDesc())
                .set(record.getSubjectCalculateSize()!=null,SettlementCostSubjectCalculateRecordEntity::getSubjectCalculateSize,record.getSubjectCalculateSize())
                .set(record.getSubjectCalculateCash()!=null,SettlementCostSubjectCalculateRecordEntity::getSubjectCalculateCash,record.getSubjectCalculateCash())
                .set(record.getFinishMillis()!=null,SettlementCostSubjectCalculateRecordEntity::getFinishMillis,record.getFinishMillis())
                .eq(SettlementCostSubjectCalculateRecordEntity::getCostSettlementCycle,record.getCostSettlementCycle())
                .eq(SettlementCostSubjectCalculateRecordEntity::getSubjectCode,record.getSubjectCode())
                .update();
    }

    protected boolean checkSubjectCalculateSuccess(String costSettlementCycle, String subjectCode){
        // 获取周期+科目的所有记录信息
        // 获取科目记录
        List<SettlementCostSubjectCalculateRecordEntity> list = settlementCostSubjectCalculateRecordService.lambdaQuery()
                .eq(SettlementCostSubjectCalculateRecordEntity::getCostSettlementCycle, costSettlementCycle)
                .eq(SettlementCostSubjectCalculateRecordEntity::getSubjectCode, subjectCode)
                .list();


        SettlementCostSubjectCalculateRecordEntity entity = list.stream().filter(x -> x.getSubjectCalculateStatus() == 1).findFirst().orElse(null);
        return entity!=null;
    }

    protected CostSubjectCalculateRecord getSubjectCalculateRecord(String costSettlementCycle, String subjectCode){
        List<SettlementCostSubjectCalculateRecordEntity> list = settlementCostSubjectCalculateRecordService.lambdaQuery()
                .eq(SettlementCostSubjectCalculateRecordEntity::getCostSettlementCycle, costSettlementCycle)
                .eq(SettlementCostSubjectCalculateRecordEntity::getSubjectCode, subjectCode)
                .list();


        SettlementCostSubjectCalculateRecordEntity entity = list.stream().filter(x -> x.getSubjectCalculateStatus() == 1).findFirst().orElse(null);
        if(Objects.isNull(entity)){
            return null;
        }
        CostSubjectCalculateRecord record = new CostSubjectCalculateRecord();
        BeanUtils.copyProperties(entity,record);
        return record;
    }




}