package com.mpolicy.settlement.core.modules.reconcile.event.project.policyplatform;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileStatusEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.ConfirmStatusEnum;
import com.mpolicy.settlement.core.modules.govern.dto.proejct.ChangePremiumInsured;
import com.mpolicy.settlement.core.modules.govern.dto.proejct.ChangePremiumInsuredProduct;
import com.mpolicy.settlement.core.modules.govern.dto.proejct.ChangeRenewalRealityPremiumData;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostCorrectionDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.CommissionTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.project.common.AbsPolicyCommonEvent;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventCheckResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventData;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.service.impl.SettlementCostProcessServiceImpl;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.SYSTEM_CORRECTION_USER;

/**
 * <AUTHOR>
 * @description 续期实收保费变更
 * @date 2024/2/26 12:26 上午
 * @Version 1.0
 */
@Service
@Slf4j
public class RenewalRealityPremiumChangeEvent extends AbsPolicyCommonEvent {
    @Override
    public HandleEventCheckResult handleIncomeEventCheck(SettlementEventJobEntity eventJob, Integer reconcileType) {
        ChangeRenewalRealityPremiumData changePolicyCodeData = JSONObject.parseObject(eventJob.getEventRequest(),
                ChangeRenewalRealityPremiumData.class);
        String policyNo = changePolicyCodeData.getPolicyCode();
        // 2.参数校验
        if (StrUtil.isEmpty(policyNo)) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg("续期实收保费变更事件保单号不存在").build();
        }
        // 判断事件是否已经生成了明细数据,如果生成了就不在运行了
        boolean checkSettlementPolicyInfo = checkSettlementPolicyInfo(eventJob.getPushEventCode(), reconcileType);
        // 如果存在纪录
        if (checkSettlementPolicyInfo) {
            return HandleEventCheckResult.builder().checkStatus(false)
                .checkMsg(StrUtil.format("续期实收保费变更事件已经写入明细")).build();
        }
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleIncomeEvent(SettlementEventJobEntity eventJob, HandleEventData handleEventData) {
        ChangeRenewalRealityPremiumData changePolicyCodeData = JSONObject.parseObject(eventJob.getEventRequest(),
                ChangeRenewalRealityPremiumData.class);
        String policyNo = changePolicyCodeData.getPolicyCode();
        Integer period = changePolicyCodeData.getPeriod();
        // 判断在这之前有没有生成明细,如果没有生成明细 先执行之前的数据
        List<SettlementPolicyInfoEntity> rectificationList =
            settlementPolicyInfoService.lambdaQuery().eq(SettlementPolicyInfoEntity::getPolicyNo, policyNo)
                .eq(SettlementPolicyInfoEntity::getPolicyNo, policyNo)
                .eq(SettlementPolicyInfoEntity::getRenewalPeriod, period)
                .eq(SettlementPolicyInfoEntity::getReconcileType, handleEventData.getReconcileType())
                .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode()).list();
        if (rectificationList.isEmpty()) {
            return "没有生成结算明细数据,暂不处理";
        }
        // 判断一下 是否存在已经结算的数据,如果存在,那么提示失败
        boolean anyMatch = rectificationList.stream()
            .anyMatch(a -> ReconcileStatusEnum.RECONCILE_FINISH.getStatusCode().equals(a.getReconcileStatus()));
        if (anyMatch) {
            return "明细存在已经结算的数据,暂不处理";
        }
        // 将已有数据进行冲正处理,然后处理新的数据
        settlementPolicyInfoRectification(rectificationList);

        return "success";
    }

    @Override
    public HandleEventCheckResult handleCostEventCheck(SettlementEventJobEntity eventJob) {
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleCostEvent(SettlementEventJobEntity eventJob, HandleEventData eventData) {

        ChangeRenewalRealityPremiumData renewalFallback = JSONObject.parseObject(eventJob.getEventRequest(),
                ChangeRenewalRealityPremiumData.class);
        String policyNo = renewalFallback.getPolicyCode();
        if (StrUtil.isEmpty(policyNo)) {
            return StrUtil.format("保单号-{}不存在-success", policyNo);
        }

        List<SettlementCostInfoEntity> costInfoEntityList = settlementCostInfoService.lambdaQuery()
                .eq(SettlementCostInfoEntity::getContractCode, renewalFallback.getContractCode())
                .eq(SettlementCostInfoEntity::getRenewalPeriod, renewalFallback.getPeriod())
                .eq(SettlementCostInfoEntity::getCorrectionFlag, 0)
                .list();

        if (CollectionUtils.isEmpty(costInfoEntityList)) {
            return StrUtil.format("保单号/合同号-{}/{}不存在-success", policyNo, renewalFallback.getContractCode());
        }

        SettlementCostPolicyInfoEntity costPolicy =
                settlementCostPolicyInfoService.getCostPolicyInfoEntityByContractCode(eventJob.getContractCode());

        if (Objects.isNull(costPolicy)) {
            return "结算保单信息缺失-success";
        }


        if (CollectionUtils.isEmpty(costInfoEntityList)) {
            return StrUtil.format("批改单结算信息-{}不存在-success", JSONObject.toJSONString(eventData));
        }

        //2、参数验证，验证不通过抛出异常
        settlementCostProcessService.validParam(eventJob, null);

        //校验
        List<ChangePremiumInsured> premiumInsuredList = renewalFallback.getInsuredList();
        if (CollectionUtils.isEmpty(premiumInsuredList)) {
            return StrUtil.format("更改被保人-{}不存在-success", JSONObject.toJSONString(eventData));
        }


        Map<String, SettlementCostInfoEntity> insuredSettlementMap =
                costInfoEntityList.stream().collect(Collectors.toMap(x -> StrUtil.format(
                        "{}-{}",
                        x.getInsuredCode(),
                        x.getProductCode()
                ), Function.identity()));

        List<SettlementCostInfoEntity> newCostList = new ArrayList<>();
        for (ChangePremiumInsured changePremiumInsured : premiumInsuredList) {
            for (ChangePremiumInsuredProduct changePremiumInsuredProduct : changePremiumInsured.getInsuredProduct()) {
                SettlementCostInfoEntity oldSettlementCostInfo = insuredSettlementMap.get(StrUtil.format(
                        "{}-{}",
                        changePremiumInsured.getInsuredCode(),
                        changePremiumInsuredProduct.getProductCode()
                ));

                if (Objects.isNull(oldSettlementCostInfo)) {
                    return StrUtil.format("更改被保人-{}-{}不存在-success", changePremiumInsured.getInsuredCode(),
                            changePremiumInsuredProduct.getProductCode());
                }
                //冲正
                newCostList.add(settlementCostProcessService.builderOffsetCostInfo(
                        eventJob,
                        handlerEventType(),
                        oldSettlementCostInfo,
                        eventJob.getCreateTime(),
                        SYSTEM_CORRECTION_USER,
                        "续期保费变更冲正",
                        Boolean.TRUE
                ));
                //生成一条新结算信息
                SettlementCostInfoEntity newSettlementCostInfo = new SettlementCostInfoEntity();
                BeanUtil.copyProperties(oldSettlementCostInfo, newSettlementCostInfo);
                newSettlementCostInfo.setId(null);
                newSettlementCostInfo.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
                //记账时间处理
                newSettlementCostInfo.setSettlementTime(new Date());
                newSettlementCostInfo.setSettlementDate(newSettlementCostInfo.getSettlementTime());

                //事件编号
                newSettlementCostInfo.setEventSourceCode(eventJob.getPushEventCode());

                //事件信息
                newSettlementCostInfo.setSettlementEventCode(handlerEventType().getEventCode());
                newSettlementCostInfo.setSettlementEventDesc(handlerEventType().getEventDesc());
                newSettlementCostInfo.setCommissionType(oldSettlementCostInfo.getCommissionType());
                newSettlementCostInfo.setSettlementGenerateType(2);
                //是否冲正
                //业务记账时间处理
                newSettlementCostInfo.setBusinessAccountTime(SettlementCostProcessServiceImpl.getCorrectionBusinessAccountTime(oldSettlementCostInfo.getBusinessAccountTime(),eventJob.getCreateTime()));
                newSettlementCostInfo.setSourceCostCode(oldSettlementCostInfo.getCostCode());
                newSettlementCostInfo.setCorrectionTime(new Date());
                newSettlementCostInfo.setCorrectionFlag(0);

                newSettlementCostInfo.setCorrectionOpType(1);
                if (StringUtil.isNotBlank(renewalFallback.getBusinessDesc())) {
                    newSettlementCostInfo.setCorrectionRemark(renewalFallback.getBusinessDesc());
                }
                //清除确认信息
                newSettlementCostInfo.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                newSettlementCostInfo.setConfirmUser(null);
                newSettlementCostInfo.setConfirmTime(null);
                newSettlementCostInfo.setConfirmGrantTime(null);
                newSettlementCostInfo.setDocumentCode(null);
                newSettlementCostInfo.setAutoCostCode(null);
                newSettlementCostInfo.setCostSettlementCycle(null);
                //冲正金额字段
                newSettlementCostInfo.setPremium(changePremiumInsuredProduct.getPremium());
                newSettlementCostInfo.setBusinessPremium(changePremiumInsuredProduct.getPremium());
                //newSettlementCostInfo.setPremium(changePremiumInsuredProduct.getPremium());
//                newSettlementCostInfo.setDiscountPremium(
//                        ReconcileBaseHelper.calcDiscountPremium(
//                                costPolicy.getPolicyNo()
//                                , changePremiumInsuredProduct.getProductCode()
//                                , oldSettlementCostInfo.getInsuredPolicyAge()
//                                , PolicyProductTypeEnum.getProdTypeEnum(costPolicy.getPolicyProductType())
//                                , oldSettlementCostInfo.getLongShortFlag()
//                                , changePremiumInsuredProduct.getPremium()
//                                , oldSettlementCostInfo.getPeriodType()
//                                , oldSettlementCostInfo.getPaymentPeriodType()
//                                , oldSettlementCostInfo.getPaymentPeriod()
//                        )
//                );
                //newSettlementCostInfo.setCostAmount(newSettlementCostInfo.getPremium().multiply
                // (newSettlementCostInfo.getCostRate()));
                //newSettlementCostInfo.setGrantAmount(newSettlementCostInfo.getCostAmount().multiply
                // (newSettlementCostInfo.getGrantRate()));
                calcNewCommission(newSettlementCostInfo);
                newCostList.add(newSettlementCostInfo);
            }
        }

        //冲正
        CostCorrectionDto costCorrectionDto = new CostCorrectionDto();

        costCorrectionDto.setNewCostList(newCostList);
        costCorrectionDto.setCorrectionOldIds(costInfoEntityList.stream().map(SettlementCostInfoEntity::getId).collect(Collectors.toList()));
        costCorrectionDto.setPolicy(costPolicy);

        settlementCostCorrectionService.saveCostCommissionRecord(eventJob, costCorrectionDto);
        policyCenterBaseClient.governApplyConfirm(eventJob.getEventBusinessCode(), "settlement_server", "支出端");
        return "success";

    }

    @Override
    public SettlementEventTypeEnum handlerEventType() {

        return SettlementEventTypeEnum.CHANGE_RENEWAL_REALITY_PREMIUM;

    }
}
