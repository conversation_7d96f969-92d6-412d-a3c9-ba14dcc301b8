package com.mpolicy.settlement.core.modules.autocost.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 员工类型枚举
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@AllArgsConstructor
public enum EmployeeTypeEnum {

    /**
     * 普通员工
     */
    INTERNAL(0, "普通员工"),

    /**
     * 外部员工
     */
    EXTERNAL(1, "外部员工"),

    /**
     * 实习生
     */
    PRACTICE(2, "实习生"),

    /**
     * 返聘员工
     */
    REHIRE_AFTER_RETIREMENT(3, "返聘员工"),

    /**
     * 外派员工
     */
    EXPATRIATE(4, "外派员工");

    /**
     * 类型值
     */
    private final Integer value;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 员工类型枚举
     */
    public static EmployeeTypeEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (EmployeeTypeEnum typeEnum : values()) {
            if (typeEnum.getValue().equals(value)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 判断是否为普通员工或返聘员工
     *
     * @param value 类型值
     * @return 是否为普通员工或返聘员工
     */
    public static boolean isInternalOrRehire(Integer value) {
        return INTERNAL.getValue().equals(value) || REHIRE_AFTER_RETIREMENT.getValue().equals(value);
    }
}
