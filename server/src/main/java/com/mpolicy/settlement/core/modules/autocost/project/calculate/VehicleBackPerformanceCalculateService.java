package com.mpolicy.settlement.core.modules.autocost.project.calculate;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.abs.AbsSubjectCalculate;
import com.mpolicy.settlement.core.modules.autocost.dto.CostAutoRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectCalculateRuleConfig;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.SettlementCostInfoDto;
import com.mpolicy.settlement.core.modules.autocost.dto.base.SettlementCostInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.base.SettlementCostQuery;
import com.mpolicy.settlement.core.modules.autocost.dto.calculate.CostSubjectCalculateRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.data.*;
import com.mpolicy.settlement.core.modules.autocost.dto.employee.EmployeeInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.OrgProductQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.SupervisorProductQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.SupervisorQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.enums.AutoCostAmountTypeEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.CostDataTypeEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.EmployeeRoleEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.OrgNatureEnum;
import com.mpolicy.settlement.core.utils.LambdaUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 【车险后台绩效】结佣计算
 * @date 2023/11/6 1:11 上午
 * @Version 1.0
 */
@Service
@Slf4j
public class VehicleBackPerformanceCalculateService extends AbsSubjectCalculate {


    @Override
    public String handleCostCalculate(CostSubjectCalculateRecord record) {
        log.info("【科目】{}科目数据计算 构建开始......", getSubjectDataEnum().getName());
        /******1、获取科目源数据******/
        List<SubjectDataPolicyComm> sourceDataList = getCalculateSourceData(record.getCostSettlementCycle());
        if(org.apache.commons.collections.CollectionUtils.isEmpty(sourceDataList)){
            log.info("【科目】{}科目数据计算 ......",getSubjectDataEnum().getName());
            return "";
        }

        /******2、根据源数据初始化佣金记录列表 ******/
        log.info("【科目】{}科目,根据源数据，开始初始化每个人结算佣金记录",getSubjectDataEnum().getName());
        List<CostAutoRecord> costAutoRecordList = initCostAutoRecordBySource(record.getDocumentCode(),record, sourceDataList);

        /******3、获取分摊数据明细 ******/
        //先按岗位角色进行分类:督导类、非督导类,再获取其对应的分摊明细数据
        //督导
        List<CostAutoRecord> supervisorAutoRecords = costAutoRecordList.stream().filter(o->Objects.equals(o.getSendObjectType(),EmployeeRoleEnum.SUPERVISOR.getCode())).collect(Collectors.toList());
        log.info("【科目】{}科目,根据源数据，督导结算佣金记录数：{}",getSubjectDataEnum().getName(),supervisorAutoRecords.size());
        Map<String,List<SupervisorProductQbiInfo>> supervisorItemDetailMap = getSupervisorProductQbiInfoMap(supervisorAutoRecords,record.getCostSettlementCycle());

        //pco、主任或副主任
        List<CostAutoRecord> otherAutoRecords = costAutoRecordList.stream().filter(o->!Objects.equals(o.getSendObjectType(),EmployeeRoleEnum.SUPERVISOR.getCode())).collect(Collectors.toList());
        log.info("【科目】{}科目,根据源数据，pco、主任或副主任结算佣金记录数：{}",getSubjectDataEnum().getName(),otherAutoRecords.size());
        Map<String,List<OrgProductQbiInfo>> orgItemDetailMap = getOrgProductQbiInfoMap(otherAutoRecords,record.getCostSettlementCycle());

        /******4、获取科目规则集******/
        Map<String, CostSubjectCalculateRuleConfig> ruleConfigMap = costSubjectCalculateRuleService.mapRuleConfig(getSubjectDataEnum().getCode());
        log.info("【科目】{}科目数据计算 获取科目规则集={}",getSubjectDataEnum().getName(),ruleConfigMap);

        /******5、计算科目规则,生成数据******/
        BigDecimal totalCash = BigDecimal.ZERO;
        for(CostAutoRecord costAutoRecord : costAutoRecordList){

            /***5-1、计算科目规则脚本入参****/
            Map<String,Object> params = createScriptParams(costAutoRecord);
            execScript(ruleConfigMap,params,costAutoRecord);

            /***5-2、校验科目规则生成的数据****/
            checkCostAutoRecord(costAutoRecord);

            /***5-3、计算汇总数据***/
            calcCostAutoRecordTotal(costAutoRecord);

            /***5-4、计算明细数据****/
            String itemMapKey = getItemMapKey(costAutoRecord.getSendObjectCode(),costAutoRecord.getObjectOrgCode(),costAutoRecord.getSettlementInstitution());
            if(Objects.equals(costAutoRecord.getSendObjectType(),EmployeeRoleEnum.SUPERVISOR.getCode())){
                autoCostProcessService.calcSupervisorProductQbiShare(costAutoRecord,supervisorItemDetailMap.get(itemMapKey));
            }else{
                autoCostProcessService.calcOrgProductQbiShare(costAutoRecord,orgItemDetailMap.get(itemMapKey));
            }
            totalCash = totalCash.add(costAutoRecord.getGrantAmount());

        }
        /***6、数据存储****/
        //过滤发放金额为空的记录
        autoCostProcessService.saveAutoCostProductResult(costAutoRecordList);
        record.setSubjectCalculateCash(totalCash);
        record.setSubjectCalculateSize(costAutoRecordList.size());

        return "success";
    }

    @Override
    public Integer resetSubjectCalculate(String costSettlementCycle,String documentCode) {
        autoCostProcessService.resetAutoCostProduct(getSubjectDataEnum().getCode(),costSettlementCycle);
        return 0;
    }


    private List<SubjectDataPolicyComm> getCalculateSourceData(String costSettlementCycle){
        return getPolicyCommSourceData(costSettlementCycle);
    }

    /**
     * 主任记录生成规则：
     * 1、主任的岗位是主职的，则直接生成
     * 2、主任的岗位是兼职的，则查询兼职主任的主职信息，
     *    如果主职为总部员工，
     * @param documentCode
     * @param record
     * @param sourceDataList
     * @return
     */

    protected List<CostAutoRecord> initCostAutoRecordBySource(String documentCode, CostSubjectCalculateRecord record, List<SubjectDataPolicyComm> sourceDataList) {
        List<String> costCodes = sourceDataList.stream().map(SubjectDataPolicyComm::getDocumentCode).collect(Collectors.toList());
        List<SettlementCostInfoDto>  costInfoDtos = settlementBaseCommService.listByCostCode(costCodes);

        //按分支编码分组
        Map<String,List<SettlementCostInfoDto>> orgCostInfoMap = LambdaUtils.groupBy(costInfoDtos,SettlementCostInfoDto::getOwnerThirdOrg);

        List<String> orgCodes = sourceDataList.stream().map(SubjectDataPolicyComm::getOrgCode).distinct().collect(Collectors.toList());
        //pco
        List<EmployeeInfo> pcoList = employeeService.listEmployeeByEmployeeRoleSnapshot(EmployeeRoleEnum.PCO,false,record.getCostSettlementCycle());
        pcoList = pcoList.stream().filter(o->orgCodes.contains(o.getOrgCode())).collect(Collectors.toList());
        Map<String,List<EmployeeInfo>> pcoMap = LambdaUtils.groupBy(pcoList,EmployeeInfo::getOrgCode);
        //主任、副主任
        List<EmployeeInfo> directorList = employeeService.listEmployeeByEmployeeRoleSnapshot(Arrays.asList(EmployeeRoleEnum.DIRECTOR,EmployeeRoleEnum.DEPUTY_DIRECTOR),false,record.getCostSettlementCycle())
                .stream().filter(o->orgCodes.contains(o.getOrgCode())).collect(Collectors.toList());;
        Map<String,List<EmployeeInfo>> directorMap = LambdaUtils.groupBy(directorList,EmployeeInfo::getOrgCode);
        //获取兼职主任的工号，用于查主职信息
        List<String> sidelineDirectorCodes = directorList.stream().filter(o->!Objects.equals(o.getServiceType(), 0)).map(EmployeeInfo::getEmployeeCode).collect(Collectors.toList());
        //查询兼职主任们的主职信息，并按机构编码分组
        Map<String,EmployeeInfo> directorMainPostMap = employeeService.listEmployeeByEmployeeCodesSnapshot(sidelineDirectorCodes,record.getCostSettlementCycle())
                .stream().filter(o->Objects.equals(o.getServiceType(), 0)).collect(Collectors.toMap(EmployeeInfo::getOrgCode, Function.identity(),(key1, key2) -> key1));


        List<CostAutoRecord> costAutoRecords = Lists.newArrayList();
        for(String orgCode : orgCodes){
            List<SettlementCostInfoDto> orgCostInfos = orgCostInfoMap.get(orgCode);
            Map<String,List<SettlementCostInfoDto>> settlementOrgCostInfo = LambdaUtils.groupBy(orgCostInfos,SettlementCostInfoDto::getSettlementInstitution);
            List<EmployeeInfo> orgPcoList = pcoMap.get(orgCode);
            List<EmployeeInfo> orgDirectorList = directorMap.get(orgCode);
            for(String settlementInstitution : settlementOrgCostInfo.keySet()) {
                BigDecimal promotion = settlementOrgCostInfo.get(settlementInstitution).stream()
                        .map(SettlementCostInfoDto::getGrantAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
                if(CollectionUtils.isNotEmpty(orgPcoList)){
                    EmployeeInfo pco = orgPcoList.get(0);

                    costAutoRecords.add(builderCostAutoRecord(documentCode,record,
                            EmployeeRoleEnum.PCO,
                            AutoCostAmountTypeEnum.PROMOTION,
                            pco,
                            settlementOrgCostInfo.get(settlementInstitution).get(0).getSettlementInstitution(),
                            settlementOrgCostInfo.get(settlementInstitution).get(0).getSettlementInstitutionName(),
                            promotion));
                }
                if(CollectionUtils.isNotEmpty(orgDirectorList)){
                    Optional<EmployeeInfo> opt = orgDirectorList.stream().filter(o-> Objects.equals(EmployeeRoleEnum.DIRECTOR.getJobId(),o.getJobId())).findFirst();
                    if(opt.isPresent()){
                        EmployeeInfo employee = opt.get();
                        //是主职
                        if(Objects.equals(employee.getServiceType(), 0)){
                            costAutoRecords.add(builderCostAutoRecord(documentCode,record,
                                    EmployeeRoleEnum.DIRECTOR,
                                    AutoCostAmountTypeEnum.PROMOTION,
                                    opt.get(),
                                    settlementOrgCostInfo.get(settlementInstitution).get(0).getSettlementInstitution(),
                                    settlementOrgCostInfo.get(settlementInstitution).get(0).getSettlementInstitutionName(),
                                    promotion));
                        }else {
                            //不是主职，则获取主职位信息
                            EmployeeInfo employeeInfo = directorMainPostMap.get(employee.getEmployeeCode());
                            //不是总部员工（总部员工 = 主职在总部机构任职的人）
                            if(employeeInfo!=null && !OrgNatureEnum.isHeadquartersOrg(employeeInfo.getOrgNature())){
                                costAutoRecords.add(builderCostAutoRecord(documentCode,record,
                                        EmployeeRoleEnum.DIRECTOR,
                                        AutoCostAmountTypeEnum.PROMOTION,
                                        opt.get(),
                                        settlementOrgCostInfo.get(settlementInstitution).get(0).getSettlementInstitution(),
                                        settlementOrgCostInfo.get(settlementInstitution).get(0).getSettlementInstitutionName(),
                                        promotion));
                            }else{
                                //给副主任
                                builderDeputyDirectorCostUtoRecord(documentCode,record,orgDirectorList,
                                        directorMainPostMap,settlementOrgCostInfo.get(settlementInstitution),promotion,costAutoRecords);
                            }
                        }
                    }else{
                        //给副主任
                        builderDeputyDirectorCostUtoRecord(documentCode,record,orgDirectorList,
                                directorMainPostMap,settlementOrgCostInfo.get(settlementInstitution),promotion,costAutoRecords);
                    }
                }
            }
        }

        /******** 督导处理逻辑 *************/
        //将督导编号设置到每个基础佣金记录上
        for(SettlementCostInfoDto costInfoDto : costInfoDtos){
            SubjectDataPolicyComm comm = sourceDataList.stream().filter(o->Objects.equals(o.getDocumentCode(),costInfoDto.getCostCode())).findFirst().get();
            if(StringUtils.isNotBlank(comm.getSupervisorCode())) {
                costInfoDto.setOwnerThirdSuperiorCode(comm.getSupervisorCode());
            }else{
                //如果岗位是客户经理，且直接上级为空的话，直接抛异常还是记录日志，todo
                if(Objects.equals(EmployeeRoleEnum.CUSTOMER_MANAGER.getJobId(), comm.getJobId())){
                    log.warn("客户经理{}没有直接上级，直接抛异常",comm.getEmployeeCode());
                }
            }
        }

        //督导
        List<String> supervisorCodeList = sourceDataList.stream().map(SubjectDataPolicyComm::getSupervisorCode).distinct().collect(Collectors.toList());
        //todo 如果一个工号存在多个职位，根据工号获取employee信息是返回多条数据，还是一条
        log.info("获取{}的督导人员信息",getSubjectDataEnum().getCode());
        List<EmployeeInfo> supervisorList = employeeService.listEmployeeByEmployeeCodes(supervisorCodeList);
        supervisorList = supervisorList.stream().filter(o->Objects.equals(o.getJobId(),EmployeeRoleEnum.SUPERVISOR.getJobId())).collect(Collectors.toList());

        BigDecimal supervisorPromotion = null;
        //对costInfoDto进行分组，分组主键为：督导工号+督导机构+督导
        Map<String,List<SettlementCostInfoDto>> supervisorCostInfoMap = LambdaUtils.groupBy(costInfoDtos.stream().filter(c->StringUtils.isNotBlank(c.getOwnerThirdSuperiorCode())).collect(Collectors.toList()), o->getItemMapKey(o.getOwnerThirdSuperiorCode(),o.getOwnerThirdOrg(),o.getSettlementInstitution()));
        for(String key : supervisorCostInfoMap.keySet()){
            List<SettlementCostInfoDto> costList = supervisorCostInfoMap.get(key);
            SettlementCostInfoDto firstCost = costList.get(0);
            //累计推广费
            supervisorPromotion = costList.stream()
                    .map(SettlementCostInfoDto::getGrantAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
            Optional<EmployeeInfo> supervisorInfoOpt = supervisorList.stream().filter(o->Objects.equals(o.getEmployeeCode(),firstCost.getOwnerThirdSuperiorCode())
                    && Objects.equals(o.getOrgCode(),firstCost.getOwnerThirdOrg())).findFirst();
            if(supervisorInfoOpt.isPresent()){
                costAutoRecords.add(builderCostAutoRecord(documentCode,record,EmployeeRoleEnum.SUPERVISOR,AutoCostAmountTypeEnum.PROMOTION,
                        supervisorInfoOpt.get(),firstCost.getSettlementInstitution(),firstCost.getSettlementInstitutionName(),supervisorPromotion));
            }else{
                log.warn("科目】{}科目，初始化督导信息，未找到{}的基础信息(employeeInfo)",getSubjectDataEnum().getName(),key);
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("科目】{}科目，初始化督导信息，未找到{}的基础信息(employeeInfo)",getSubjectDataEnum().getName(),key)));
            }
        }

        return costAutoRecords;
    }
    private void builderDeputyDirectorCostUtoRecord(String documentCode,
                                                    CostSubjectCalculateRecord record,
                                                    List<EmployeeInfo> orgDirectorList,
                                                    Map<String,EmployeeInfo> directorMainPostMap,
                                                    List<SettlementCostInfoDto> costInfoDtoList,
                                                    BigDecimal promotion,
                                                    List<CostAutoRecord> costAutoRecords){
        Optional<EmployeeInfo> opt = orgDirectorList.stream().filter(o-> Objects.equals(EmployeeRoleEnum.DEPUTY_DIRECTOR.getJobId(),o.getJobId())).findFirst();
        if(opt.isPresent()) {
            EmployeeInfo employee = opt.get();
            //是主职
            if (Objects.equals(employee.getServiceType(), 0)) {
                costAutoRecords.add(builderCostAutoRecord(documentCode, record,
                        EmployeeRoleEnum.DIRECTOR,
                        AutoCostAmountTypeEnum.PROMOTION,
                        opt.get(),
                        costInfoDtoList.get(0).getSettlementInstitution(),
                        costInfoDtoList.get(0).getSettlementInstitutionName(),
                        promotion));
            } else {
                //不是主职，则获取主职位信息
                EmployeeInfo employeeInfo = directorMainPostMap.get(employee.getEmployeeCode());
                //不是总部员工（总部员工 = 主职在总部机构任职的人）
                if(employeeInfo!=null && !OrgNatureEnum.isHeadquartersOrg(employeeInfo.getOrgNature())){
                    costAutoRecords.add(builderCostAutoRecord(documentCode, record,
                            EmployeeRoleEnum.DIRECTOR,
                            AutoCostAmountTypeEnum.PROMOTION,
                            opt.get(),
                            costInfoDtoList.get(0).getSettlementInstitution(),
                            costInfoDtoList.get(0).getSettlementInstitutionName(),
                            promotion));
                }
            }
        }
    }

    private CostAutoRecord builderCostAutoRecord(String documentCode,
                                                 CostSubjectCalculateRecord record,
                                                 EmployeeRoleEnum roleEnum,
                                                 AutoCostAmountTypeEnum amountTypeEnum,
                                                 EmployeeInfo employee,
                                                 String settlementInstitution,
                                                 String settlementInstitutionName,
                                                 BigDecimal promotion){
        CostAutoRecord costAutoRecord = initCostAutoRecord(record.getProgrammeCode(),documentCode,record.getCostSettlementCycle());
        costAutoRecord.setCostSettlementCycle(record.getCostSettlementCycle());
        costAutoRecord.setSendObjectType(roleEnum.getCode());
        costAutoRecord.setSendObjectCode(employee.getEmployeeCode());
        costAutoRecord.setSendObjectName(employee.getEmployeeName());
        costAutoRecord.setObjectOrgCode(employee.getOrgCode());
        costAutoRecord.setObjectOrgName(employee.getOrgName());
        costAutoRecord.setSettlementInstitution(settlementInstitution);
        costAutoRecord.setSettlementInstitutionName(settlementInstitutionName);

        costAutoRecord.setAmountType(amountTypeEnum.getCode());
        costAutoRecord.setCostDataType(CostDataTypeEnum.PRODUCT.getCode());
        costAutoRecord.setAmount(promotion);
        return costAutoRecord;
    }





    protected Map<String, Object> createScriptParams(CostAutoRecord costAutoRecord) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("roleCode",costAutoRecord.getSendObjectType());
        return params;
    }


    protected void checkCostAutoRecord(CostAutoRecord costAutoRecord) {

    }


    protected void calcCostAutoRecordTotal(CostAutoRecord costAutoRecord) {
        costAutoRecord.setCommissionAmount(calcCommissionAmt(costAutoRecord.getAmount(),costAutoRecord.getCommissionRate()));
        costAutoRecord.setGrantAmount(calcGrantAmt(costAutoRecord.getCommissionAmount(),costAutoRecord.getGrantRate()));
    }




    @Override
    public CostSubjectEnum getSubjectDataEnum() {
        return CostSubjectEnum.VEHICLE_PERFORMANCE;
    }
}