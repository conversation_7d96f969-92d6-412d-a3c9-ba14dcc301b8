package com.mpolicy.settlement.core.modules.reconcile.event.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/3/5
 * @Version 1.0
 */

@Data
public class PolicyTaxPremResult {

    /**
     * 小鲸险种编码
     */
    @ApiModelProperty("小鲸险种编码")
    private String productCode;

    /**
     * 费率的唯一标识
     */
    @ApiModelProperty("费率的唯一标识")
    private String premCode;

    /**
     * 小鲸险种名称
     */
    @ApiModelProperty("小鲸险种名称")
    private String productName;

    /**
     * 税前保费
     */
    @ApiModelProperty("税前保费")
    private BigDecimal premium;

    /**
     * 结算方式 按全保费(含税)结算 按净保费(税后)结算
     */
    @ApiModelProperty("结算方式 按全保费(含税)结算 按净保费(税后)结算")
    private String settlementMethod;

    /**
     * 税率
     */
    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    /**
     * 税后保费
     */
    @ApiModelProperty("税后保费")
    private BigDecimal taxAfterPremium;

}
