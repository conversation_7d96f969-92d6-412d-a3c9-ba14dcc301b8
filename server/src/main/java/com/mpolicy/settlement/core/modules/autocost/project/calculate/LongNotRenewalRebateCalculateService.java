package com.mpolicy.settlement.core.modules.autocost.project.calculate;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.abs.AbsSubjectCalculate;
import com.mpolicy.settlement.core.modules.autocost.dto.CostAutoRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.SettlementCostInfoDto;
import com.mpolicy.settlement.core.modules.autocost.dto.calculate.CostSubjectCalculateRecord;
import com.mpolicy.settlement.core.modules.autocost.dto.calculate.PolicyProductItemCalculateParamDto;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataPolicyComm;
import com.mpolicy.settlement.core.modules.autocost.dto.employee.EmployeeInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.EmployeeQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.enums.AutoCostAmountTypeEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.CostDataTypeEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.EmployeeRoleEnum;
import com.mpolicy.settlement.core.modules.autocost.project.calculate.common.PolicyCalculateService;
import com.mpolicy.settlement.core.modules.autocost.project.calculate.common.PolicyProductCalculateService;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.utils.LambdaUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 【长险续期回扣】结佣计算
 * @date 2023/11/6 1:11 上午
 * @Version 1.0
 */
@Service
@Slf4j
public class LongNotRenewalRebateCalculateService extends PolicyProductCalculateService<SubjectDataPolicyComm> {



    @Override
    public CostSubjectEnum getSubjectDataEnum() {
        return CostSubjectEnum.LONG_NOT_RENEWAL_REBATE_COMM;
    }

    @Override
    protected List<String> listEmployeeCodes(List<SubjectDataPolicyComm> sourceDataList) {
        return sourceDataList.stream().map(SubjectDataPolicyComm::getEmployeeCode).distinct().collect(Collectors.toList());
    }

    @Override
    protected Map<String, List<SubjectDataPolicyComm>> mapT(List<SubjectDataPolicyComm> sourceDataList) {
        return LambdaUtils.groupBy(sourceDataList,SubjectDataPolicyComm::getEmployeeCode);
    }

    @Override
    protected Map<String, List<SettlementCostInfoDto>> mapSettlementCostInfo(List<SubjectDataPolicyComm> sourceDataList) {
        List<String> costCodes = sourceDataList.stream().map(SubjectDataPolicyComm::getDocumentCode).collect(Collectors.toList());
        List<SettlementCostInfoDto> dtos = settlementBaseCommService.listByCostCode(costCodes);

        Map<String,List<SettlementCostInfoDto>> maps = LambdaUtils.groupBy(dtos, SettlementCostInfoDto::getOwnerThirdCode);

        return maps;
    }


    @Override
    public List<CostAutoRecord> builderCostAutoRecord(String documentCode,CostSubjectCalculateRecord record, List<SubjectDataPolicyComm> sourceData) {
        List<CostAutoRecord> list = Lists.newArrayList();

        Map<String,List<SubjectDataPolicyComm>> map = LambdaUtils.groupBy(sourceData,SubjectDataPolicyComm::getSettlementInstitution);
        for(String key : map.keySet()){
            CostAutoRecord costAutoRecord = initCostAutoRecord(record.getProgrammeCode(),documentCode,record.getCostSettlementCycle());
            SubjectDataPolicyComm policyComm = map.get(key).get(0);
            costAutoRecord.setSettlementInstitution(key);
            costAutoRecord.setSettlementInstitutionName(map.get(key).get(0).getSettlementInstitutionName());
            costAutoRecord.setCostSettlementCycle(record.getCostSettlementCycle());
            costAutoRecord.setSendObjectType(EmployeeRoleEnum.ZHNX.getCode());
            costAutoRecord.setSendObjectCode(policyComm.getEmployeeCode());
            costAutoRecord.setSendObjectName(policyComm.getEmployeeName());
            costAutoRecord.setObjectOrgCode(policyComm.getOrgCode());
            costAutoRecord.setObjectOrgName(policyComm.getOrgName());
            costAutoRecord.setAmountType(AutoCostAmountTypeEnum.PROMOTION.getCode());
            costAutoRecord.setCostDataType(CostDataTypeEnum.POLICY_PRODUCT.getCode());
            costAutoRecord.setGrantRate(DEFAULT_GRANT_RATE);
            list.add(costAutoRecord);
        }

        return list;
    }

    @Override
    public Map<String, Object> createScriptParams(CostAutoRecord costAutoRecord) {
        return Collections.EMPTY_MAP;
    }

    @Override
    public void checkCostAutoRecord(CostAutoRecord costAutoRecord) {

    }

    @Override
    public void calcCostAutoRecordItem(PolicyProductItemCalculateParamDto<SubjectDataPolicyComm> itemParam) {
        BigDecimal totalPromotion = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        List<SettlementCostInfoDto> updateDtoList = Lists.newArrayList();
        Set<String> keySet = Sets.newHashSet();
        for(int i=0;i<itemParam.getOldCost().size();i++){
            SettlementCostInfoDto dto = itemParam.getOldCost().get(i);

            //结算机构不一致，则返回
            if(!Objects.equals(dto.getSettlementInstitution(),itemParam.getCostAutoRecord().getSettlementInstitution())){
                continue;
            }
            SettlementCostInfoDto updateDto = new SettlementCostInfoDto();
            updateDto.setId(dto.getId());

            BigDecimal grate = PolicySettlementUtils.calcAmtByPercent(dto.getGrantRate(),itemParam.getCostAutoRecord().getGrantRate(),2);
            //BigDecimal grantAmount = PolicySettlementUtils.calcGrantAmount(dto.getCostAmount(),grate);
            updateDto.setGrantRate(grate);
            BigDecimal grantAmount = dto.getGrantAmount();
            updateDto.setGrantAmount(grantAmount);
            totalPromotion = totalPromotion.add(grantAmount);
            //注意：基础佣金表中存在暂发、补发及原记录，其costAmount存在一致，只是发放比例和发放金额不一致。所以key相同的情况下只需要增加一次
            if(!keySet.contains(keyCostInfo(dto))) {
                totalAmount = totalAmount.add(dto.getCostAmount());
            }
            updateDto.setAutoCostCode(itemParam.getCostAutoRecord().getAutoCostCode());
            updateDto.setDocumentCode(itemParam.getCostAutoRecord().getDocumentCode());
            updateDto.setCostSettlementCycle(itemParam.getCostAutoRecord().getCostSettlementCycle());
            updateDto.setGrantMonth(DateUtil.format(itemParam.getCostAutoRecord().getCostSettlementTime(), DatePattern.NORM_DATE_FORMAT));
            updateDtoList.add(updateDto);
            keySet.add(keyCostInfo(dto));

        }
        itemParam.getCostAutoRecord().setUpdateItemList(updateDtoList);

        itemParam.getCostAutoRecord().setAmount(totalAmount);
        itemParam.getCostAutoRecord().setGrantAmount(totalPromotion);
    }

    /**
     *
     * @param dto
     * @return
     */
    private String keyCostInfo(SettlementCostInfoDto dto){
        return StrUtil.format("{}_{}_{}_{}_{}_{}_{}",
                dto.getSettlementInstitution(),
                dto.getContractCode(),
                dto.getProductCode(),
                dto.getInsuredCode(),
                dto.getRenewalPeriod(),
                dto.getInitialEventCode(),
                dto.getProductStatus());
    }
}