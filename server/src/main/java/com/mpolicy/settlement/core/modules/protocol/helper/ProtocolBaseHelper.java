package com.mpolicy.settlement.core.modules.protocol.helper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.policy.common.enums.PolicyPaymentPeriodTypeEnum;
import com.mpolicy.policy.common.enums.PolicyPaymentTypeEnum;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.common.protocol.CheckOrgListIsMateInput;
import com.mpolicy.settlement.core.common.protocol.CheckOrgListIsMateOut;
import com.mpolicy.settlement.core.common.protocol.ProductOrgListOut;
import com.mpolicy.settlement.core.common.protocol.ProductOrgListVo;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileSubjectOnlineEnum;
import com.mpolicy.settlement.core.enums.ReconcileTypeEnum;
import com.mpolicy.settlement.core.modules.protocol.dto.ProtocolInsuranceProductInfo;
import com.mpolicy.settlement.core.modules.protocol.dto.ProtocolInsuranceProductInfoOut;
import com.mpolicy.settlement.core.modules.protocol.entity.*;
import com.mpolicy.settlement.core.modules.protocol.service.*;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyProductFeePremEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyProductPremEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyInsuredPeriodTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.*;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileSubjectHelper;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementPolicyProductFeePremService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementPolicyProductPremService;
import com.mpolicy.settlement.core.utils.PremUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 协议管理基础辅助服务
 *
 * <AUTHOR>
 * @since 2023/05/20
 */

@Slf4j
@Component
public class ProtocolBaseHelper {

    public static ProtocolBaseHelper protocolBaseHelper;

    @Autowired
    ProtocolBasicInfoService protocolBasicInfoService;

    @Autowired
    ProtocolCompanyService protocolCompanyService;

    @Autowired
    ProtocolEnclosureService protocolEnclosureService;

    @Autowired
    ProtocolInsuranceProductProductService protocolInsuranceProductProductService;

    @Autowired
    ProtocolInsuranceProductService protocolInsuranceProductService;

    @Autowired
    ProtocolProductPremService protocolProductPremService;

    @Autowired
    OrgInfoService orgInfoService;

    @Autowired
    SettlementPolicyProductPremService settlementPolicyProductPremService;
    @Autowired
    ProtocolInsuranceProductProductPlanService protocolInsuranceProductProductPlanService;

    @Autowired
    SettlementPolicyProductFeePremService policyProductFeePremService;
    // 机构转总部
    private final static List<String> TRANSFER_HEADQUARTERS = CollUtil.newArrayList("OR20220427165152MGiQiz");

    @PostConstruct
    public void init() {
        protocolBaseHelper = this;
        // redis服务
        protocolBaseHelper.orgInfoService = this.orgInfoService;
        protocolBaseHelper.protocolBasicInfoService = this.protocolBasicInfoService;
        protocolBaseHelper.protocolCompanyService = this.protocolCompanyService;
        protocolBaseHelper.protocolEnclosureService = this.protocolEnclosureService;
        protocolBaseHelper.protocolInsuranceProductProductService = this.protocolInsuranceProductProductService;
        protocolBaseHelper.protocolInsuranceProductService = this.protocolInsuranceProductService;
        protocolBaseHelper.protocolProductPremService = this.protocolProductPremService;
        protocolBaseHelper.settlementPolicyProductPremService = this.settlementPolicyProductPremService;
        protocolBaseHelper.protocolInsuranceProductProductPlanService = this.protocolInsuranceProductProductPlanService;
        protocolBaseHelper.policyProductFeePremService = this.policyProductFeePremService;
    }

    /**
     * 获取协议产品编码(小鲸)
     *
     * @param innerSignatoryCode    内部签署方编码
     * @param externalSignatoryType 外部签署方类型
     * @param externalSignatoryCode 外部签署方编码
     * @param companyCodeList       产品所属保司编码
     * @return
     */
    public static List<ProtocolInsuranceProductInfo> queryProtocolInsuranceProductList(String innerSignatoryCode,
                                                                                       String externalSignatoryType, String externalSignatoryCode, List<String> companyCodeList) {
        // 1.获取协议
        List<String> protocolCodeList = protocolBaseHelper.protocolBasicInfoService.lambdaQuery()
                .eq(ProtocolBasicInfoEntity::getInnerSignatoryCode, innerSignatoryCode)
                .in(ProtocolBasicInfoEntity::getExternalSignatoryCode, StrUtil.split(externalSignatoryCode, ','))
                .eq(ProtocolBasicInfoEntity::getExternalSignatoryType, externalSignatoryType).list().stream()
                .map(ProtocolBasicInfoEntity::getProtocolCode).collect(Collectors.toList());
        if (protocolCodeList.isEmpty()) {
            log.info("根据内外部签署方innerSignatoryCode={},externalSignatoryCode={},没有匹配到协议信息",
                    innerSignatoryCode, externalSignatoryCode);
            return Collections.emptyList();
        }
        // 2.直接读取费率表获取协议险种编码
        List<String> insuranceProductCodeList = protocolBaseHelper.protocolProductPremService.lambdaQuery()
                .in(ProtocolProductPremEntity::getProtocolCode, protocolCodeList)
                .in(ProtocolProductPremEntity::getCompanyCode, companyCodeList).list().stream()
                .map(ProtocolProductPremEntity::getInsuranceProductCode).distinct().collect(Collectors.toList());
        if (insuranceProductCodeList.isEmpty()) {
            log.info("全部协议编码{}没有匹配上传到的协议产品信息", JSONUtil.toJsonStr(protocolCodeList));
            return Collections.emptyList();
        }
        //获取协议产品信息
        List<ProtocolInsuranceProductEntity> insuranceProductList =
                protocolBaseHelper.protocolInsuranceProductService.lambdaQuery()
                        .in(ProtocolInsuranceProductEntity::getInsuranceProductCode, insuranceProductCodeList)
                        .eq(ProtocolInsuranceProductEntity::getReconcileType, ReconcileTypeEnum.PROTOCOL.getCode()).list();
        if (insuranceProductList.isEmpty()) {
            log.info("协议产品信息不存在");
            return Collections.emptyList();
        }
        // 3.获取保司产品和小鲸险种关联关系
        Map<Integer, List<ProtocolInsuranceProductProductEntity>> protocolInsuranceProductProductMap =
                protocolBaseHelper.protocolInsuranceProductProductService.lambdaQuery()
                        .in(ProtocolInsuranceProductProductEntity::getInsuranceProductId,
                                insuranceProductList.stream().map(ProtocolInsuranceProductEntity::getId)
                                        .collect(Collectors.toList())).list().stream()
                        .collect(Collectors.groupingBy(ProtocolInsuranceProductProductEntity::getInsuranceProductId));
        if (protocolInsuranceProductProductMap.isEmpty()) {
            log.info("保司产品{}都没有配置小鲸险种关联关系", JSONUtil.toJsonStr(insuranceProductCodeList));
            return Collections.emptyList();
        }
        // 4.获取保司产品信息
        List<ProtocolInsuranceProductInfo> resultList = insuranceProductList.stream().map(m -> {
            ProtocolInsuranceProductInfo result = BeanUtil.copyProperties(m, ProtocolInsuranceProductInfo.class);
            List<ProtocolInsuranceProductProductEntity> protocolInsuranceProductProductList =
                    protocolInsuranceProductProductMap.get(m.getId());
            if (CollUtil.isNotEmpty(protocolInsuranceProductProductList)) {
                result.setProductCodeList(protocolInsuranceProductProductList.stream()
                        .map(ProtocolInsuranceProductProductEntity::getProductCode).distinct()
                        .collect(Collectors.toList()));
            }
            return result;
        }).collect(Collectors.toList());
        log.info("根据innerSignatoryCode={},externalSignatoryCode={},companyCode={}匹配的协议产品险种信息:{}",
                innerSignatoryCode, externalSignatoryCode, JSONUtil.toJsonStr(companyCodeList),
                JSONUtil.toJsonStr(resultList));
        return resultList;
    }

    /**
     * 根据小鲸险种编码集合获取协议产品集合信息
     *
     * @param productCodeList 小鲸险种编码
     * @param reconcileType   结算类型 0:小鲸 1:非小鲸
     * @return
     */
    public static List<ProtocolInsuranceProductInfoOut> queryProtocolInsuranceProductList(List<String> productCodeList,
                                                                                          Integer reconcileType) {
        if (CollUtil.isEmpty(productCodeList) || reconcileType == null) {
            log.info("小鲸险种编码为空");
            return Collections.emptyList();
        }
        log.info("根据小鲸险种编码{}集合获取{}产品集合信息", JSONUtil.toJsonStr(productCodeList),
                reconcileType == 0 ? "协议" : "合约");
        List<ProtocolInsuranceProductInfoOut> resultList =
                protocolBaseHelper.protocolInsuranceProductProductService.findProtocolInsuranceProductList(productCodeList,
                        reconcileType);
        log.info("传入小鲸险种编码={}条,成功匹配{}产品集合信息={}", productCodeList.size(),
                reconcileType == 0 ? "协议" : "合约", JSONUtil.toJsonStr(resultList));
        return resultList;
    }

    /**
     * 根据小鲸险种编码获取协议产品信息,如果没有配置返回null
     *
     * @param productCode   小鲸险种编码
     * @param reconcileType 结算类型 0:小鲸 1:非小鲸
     * @return
     */
    public static ProtocolInsuranceProductInfoOut queryProtocolInsuranceProductInfoByProductCode(String productCode,
                                                                                                 Integer reconcileType) {
        ProtocolInsuranceProductInfoOut result =
                protocolBaseHelper.protocolInsuranceProductProductService.findProtocolInsuranceProductInfo(productCode,
                        reconcileType);
        return result;
    }

    /**
     * 匹配费率信息
     *
     * @param vo
     * @return
     */
    public static List<PolicyProductPremResult> queryPolicyProductPrem(PolicyProductPrem vo) {
        //校验其他必填项是否给了
        if (!StrUtil.isAllNotBlank(vo.getInsuranceProductCode(), vo.getEffectiveDate())) {
            log.warn("[协议]匹配费率参数:{} 请求参数存在必填项为空", JSONUtil.toJsonStr(vo));
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("请求参数存在必填项为空,请检查."));
        }
        //过滤保司协议险种编码
        List<ProtocolProductPremEntity> productPremList = protocolBaseHelper.protocolProductPremService.lambdaQuery()
                .eq(ProtocolProductPremEntity::getInsuranceProductCode, vo.getInsuranceProductCode()).list();
        if (productPremList.isEmpty()) {
            log.warn("[协议]匹配费率参数:{} 根据协议编码没有匹配费率信息", JSONUtil.toJsonStr(vo));
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("保单={},协议产品编码={}没有匹配费率信息", vo.getPolicyNo(),
                            vo.getInsuranceProductCode())));
        }
        //匹配科目类型
        List<String> costList = CollUtil.newArrayList(ReconcileSubjectOnlineEnum.FIRST_YR_COMM.getName());
        productPremList =
                productPremList.stream().filter(f -> costList.contains(f.getCostType())).collect(Collectors.toList());
        if (productPremList.isEmpty()) {
            log.warn("[协议]匹配费率参数:{} 根据费用类型没有匹配到费率信息", JSONUtil.toJsonStr(vo));
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("保单={},费用类型=首续年没有匹配到费率信息", vo.getPolicyNo())));
        }
        // 匹配适用分公司
        List<String> orgList = CollUtil.newArrayList(vo.getOrgCode(), Constant.NATIONWIDE_ORG_CODE);
        List<String> orgCodeList =
                productPremList.stream().map(ProtocolProductPremEntity::getOrgCode).distinct().collect(Collectors.toList());
        productPremList =
                productPremList.stream().filter(f -> orgList.contains(f.getOrgCode())).collect(Collectors.toList());
        if (productPremList.isEmpty()) {
            log.warn("[协议]匹配费率参数:{} 根据适用分公司没有匹配费率信息", JSONUtil.toJsonStr(vo));
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("保单={},协议产品={}," + "适用分公司={}没有匹配到费率信息[{}]", vo.getPolicyNo(),
                            vo.getInsuranceProductCode(), vo.getOrgCode(), orgCodeList)));
        }
        // 匹配时间
        List<String> timeList = productPremList.stream()
                .map(m -> m.getEffectiveStartDate() + "~" + DateUtil.endOfDay(DateUtil.parseDate(m.getEffectiveEndDate())))
                .distinct().collect(Collectors.toList());
        productPremList = productPremList.stream().filter(
                f -> DateUtil.isIn(DateUtil.parseDate(vo.getEffectiveDate()), DateUtil.parseDate(f.getEffectiveStartDate()),
                        DateUtil.endOfDay(DateUtil.parseDate(f.getEffectiveEndDate())))).collect(Collectors.toList());
        if (productPremList.isEmpty()) {
            log.warn("[协议]匹配费率参数:{} 根据时间区间没有匹配到响应的费率信息", JSONUtil.toJsonStr(vo));
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("保单={},协议产品={}," + "时间={}没有匹配到费率信息[{}]", vo.getPolicyNo(),
                            vo.getInsuranceProductCode(), vo.getEffectiveDate(), timeList)));
        }
        // 过滤费率信息
        List<ProtocolProductPremEntity> resultProductPremList = ProtocolBaseHelper.mateProductPrem(productPremList, vo);
        if (resultProductPremList.isEmpty()) {
            log.warn("[协议]匹配费率参数:{} 没有匹配到相应的费率信息", JSONUtil.toJsonStr(vo));
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("保单={},协议产品={}没有匹配到相应的费率信息", vo.getPolicyNo(),
                            vo.getInsuranceProductCode())));
        }
        Map<String, ProtocolProductPremEntity> protocolProductPremMap = resultProductPremList.stream()
                .collect(Collectors.toMap(ProtocolProductPremEntity::getCostType, v -> v, (v1, v2) -> {
                    log.warn("[协议]科目:{} 命中多条费率信息:{}", JSONUtil.toJsonStr(vo),
                            JSONUtil.toJsonStr(resultProductPremList));
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                            StrUtil.format("保单={}," + "协议产品={}科目={}配到费率多条费率信息,请检查", vo.getPolicyNo(),
                                    vo.getInsuranceProductCode(), v1.getCostType())));
                }));

        List<PolicyProductPremResult> resultList = new ArrayList<>();
        protocolProductPremMap.forEach((costType, protocolProductPrem) -> {
            String subjectCode = ReconcileSubjectHelper.matchSearchName(costType);
            if (StrUtil.isBlank(subjectCode)) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                        StrUtil.format("保单={},合约产品={}科目={}科目信息未配置", vo.getPolicyNo(),
                                vo.getInsuranceProductCode(), costType)));
            }
            PolicyProductPremResult result =
                    ProtocolBaseHelper.extractProductPrem(protocolProductPrem, vo.getRenewalYear());
            if (result != null) {
                result.setSettlementSubjectCode(subjectCode);
                result.setSettlementSubjectName(costType);
                result.setReportOrgCode(vo.getReportOrgCode());
                result.setReportOrgName(vo.getReportOrgName());
                try {
//                    result.setTaxRate(result.getTaxRate());
                    result.setSettlementMethod(protocolProductPrem.getSettlementMethod());
                } catch (Exception e) {
                    result.setTaxRate(BigDecimal.ZERO);
                    result.setSettlementMethod(StatusEnum.INVALID.getCode());
                }
                result.setBusinessCode(protocolProductPrem.getProtocolCode());
                result.setPremCode(protocolProductPrem.getPremCode());
                result.setInsuranceProductCode(protocolProductPrem.getInsuranceProductCode());
                result.setInsuranceProductName(protocolProductPrem.getInsuranceProductName());
                ProtocolBasicInfoEntity protocolBasicInfo = protocolBaseHelper.protocolBasicInfoService.lambdaQuery()
                        .eq(ProtocolBasicInfoEntity::getProtocolCode, protocolProductPrem.getProtocolCode()).one();
                if (protocolBasicInfo != null) {
                    result.setExternalSignatoryCode(protocolBasicInfo.getExternalSignatoryCode());
                    result.setExternalSignatoryName(protocolBasicInfo.getExternalSignatoryName());
                    result.setExternalSignatoryType(protocolBasicInfo.getExternalSignatoryType());
                    result.setInnerSignatoryCode(protocolBasicInfo.getInnerSignatoryCode());
                    result.setInnerSignatoryName(protocolBasicInfo.getInnerSignatoryName());
                    result.setInnerSignatoryType("3");
                }
                resultList.add(result);
            }

        });
        if (resultList.isEmpty()) {
            log.warn("[协议]匹配费率参数:{} 没有匹配到相应的费率信息", JSONUtil.toJsonStr(vo));
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("保单={},协议产品={}没有匹配到相应的费率信息", vo.getPolicyNo(),
                            vo.getInsuranceProductCode())));
        }
        log.info("[协议]保单号={}成功匹配费率信息={}", vo.getPolicyNo(), JSONUtil.toJsonStr(resultList));
        return resultList;
    }

    /**
     * 查询一单一议
     *
     * @param input
     * @return
     */
    public static PolicyPremResult queryPolicyPrem(PolicyPremInput input) {
        log.info("[协议]协议查询一单一议入参={}", JSONUtil.toJsonStr(input));
        List<SettlementPolicyProductPremEntity> premList =
                protocolBaseHelper.settlementPolicyProductPremService.lambdaQuery()
                        .eq(SettlementPolicyProductPremEntity::getPolicyNo, input.getPolicyNo())
                        .eq(SettlementPolicyProductPremEntity::getReconcileType, 0).list();
        if (premList.isEmpty()) {
            log.info("[协议]协议查询一单一议入参={}没有匹配到数据", JSONUtil.toJsonStr(input));
            return null;
        }
        // 判断有没有满足险种的数据,没有的话取 险种编码为空的数据
        if (StrUtil.isBlank(input.getProductCode())) {
            premList = premList.stream().filter(f -> StrUtil.isBlank(f.getProductCode())).collect(Collectors.toList());
        } else {
            List<SettlementPolicyProductPremEntity> resultList = premList.stream().filter(
                            f -> StrUtil.isNotBlank(f.getProductCode()) && input.getProductCode().equals(f.getProductCode()))
                    .collect(Collectors.toList());
            if (resultList.isEmpty()) {
                resultList =
                        premList.stream().filter(f -> StrUtil.isBlank(f.getProductCode())).collect(Collectors.toList());
            }
            premList = resultList;
        }
        if (premList.isEmpty()) {
            return null;
        }
        // 判断有没有满足产品的数据,没有的话取 险种编码为空的数据
        if (StrUtil.isBlank(input.getInsuranceProductCode())) {
            premList = premList.stream().filter(f -> StrUtil.isBlank(f.getInsuranceProductCode())).collect(Collectors.toList());
        } else {
            List<SettlementPolicyProductPremEntity> resultList = premList.stream().filter(
                            f -> StrUtil.isNotBlank(f.getInsuranceProductCode()) && input.getInsuranceProductCode().equals(f.getInsuranceProductCode()))
                    .collect(Collectors.toList());
            if (resultList.isEmpty()) {
                resultList =
                        premList.stream().filter(f -> StrUtil.isBlank(f.getInsuranceProductCode())).collect(Collectors.toList());
            }
            premList = resultList;
        }
        if (premList.isEmpty()) {
            return null;
        }

        // 批单号同险种编码
        if (StrUtil.isBlank(input.getBatchCode())) {
            premList = premList.stream().filter(f -> StrUtil.isBlank(f.getBatchCode())).collect(Collectors.toList());
        } else {
            List<SettlementPolicyProductPremEntity> resultList = premList.stream()
                    .filter(f -> StrUtil.isNotBlank(f.getBatchCode()) && input.getBatchCode().equals(f.getBatchCode()))
                    .collect(Collectors.toList());
            if (resultList.isEmpty()) {
                resultList =
                        premList.stream().filter(f -> StrUtil.isBlank(f.getBatchCode())).collect(Collectors.toList());
            }
            premList = resultList;
        }
        if (premList.isEmpty()) {
            return null;
        }
        // 保单年期
        if (input.getYear() == null) {
            premList = premList.stream().filter(f -> f.getYear() == null).collect(Collectors.toList());
        } else {
            List<SettlementPolicyProductPremEntity> resultList =
                    premList.stream().filter(f -> f.getYear() != null && input.getYear().equals(f.getYear()))
                            .collect(Collectors.toList());
            if (resultList.isEmpty()) {
                resultList = premList.stream().filter(f -> f.getYear() == null).collect(Collectors.toList());
            }
            premList = resultList;
        }
        if (premList.isEmpty()) {
            return null;
        }

        // 缴费期次
        if (input.getPeriod() == null) {
            premList = premList.stream().filter(f -> f.getPeriod() == null).collect(Collectors.toList());
        } else {
            List<SettlementPolicyProductPremEntity> resultList =
                    premList.stream().filter(f -> f.getPeriod() != null && input.getPeriod().equals(f.getPeriod()))
                            .collect(Collectors.toList());
            if (resultList.isEmpty()) {
                resultList = premList.stream().filter(f -> f.getPeriod() == null).collect(Collectors.toList());
            }
            premList = resultList;
        }
        if (premList.isEmpty()) {
            return null;
        }
        if (premList.size() > 1) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("获取一单一议请求参数={},匹配到了多个费率信息", JSONUtil.toJsonStr(input))));
        }
        PolicyPremResult policyPremResult = premList.stream().map(m -> {
            PolicyPremResult result = new PolicyPremResult();
            result.setPremCode(m.getPremCode());
            result.setYearRate(m.getYearRate());
            result.setPremium(m.getPremium());
            result.setTaxAfterPremium(m.getTaxAfterPremium());
            result.setProductCode(m.getProductCode());
            result.setProductName(m.getProductName());
            result.setSettlementMethod(m.getSettlementMethod());
            result.setTaxRate(m.getTaxRate());
            return result;
        }).findFirst().get();
        log.info("[协议]保单号={}一单一议成功匹配费率信息={}", input.getPolicyNo(),
                JSONUtil.toJsonStr(policyPremResult));
        return policyPremResult;
    }

    /**
     * 匹配费率数据
     *
     * @param productPremList 费率配置
     * @param vo              查询条件
     * @return
     */
    private static List<ProtocolProductPremEntity> mateProductPrem(List<ProtocolProductPremEntity> productPremList,
                                                                   PolicyProductPrem vo) {
        //开始处理数据
        return productPremList.stream().filter(f -> {
            //主险保司协议险种编码
            if (!PremUtil.verifyParam(vo.getMainInsuranceProductCode(), f.getMainInsuranceProductCode())) {
                log.info("[协议]主险保司协议险种编码不匹配请求参数={},费率表参数={}", vo.getMainProductCode(),
                        f.getMainInsuranceProductCode());
                return false;
            }
            //投保计划
            if (!PremUtil.verifyParam(vo.getProductPlan(), f.getProductPlan())) {
                log.info("[协议]投保计划不匹配请求参数={},费率表参数={}", vo.getProductPlan(), f.getProductPlan());
                return false;
            }
            //满期年龄
            if (!PremUtil.verifyParam(vo.getExpireAge(), f.getExpireAge())) {
                log.info("[协议]满期年龄不匹配请求参数={},费率表参数={}", vo.getExpireAge(), f.getExpireAge());
                return false;
            }
            //被保人性别
            if (!PremUtil.verifyParam(vo.getInsuredGender(), f.getInsuredGender())) {
                log.info("[协议]被保人性别不匹配请求参数={},费率表参数={}", vo.getInsuredGender(),
                        f.getInsuredGender());
                return false;
            }
            //投保人性别
            if (!PremUtil.verifyParam(vo.getApplicantGender(), f.getApplicantGender())) {
                log.info("[协议]投保人性别不匹配请求参数={},费率表参数={}", vo.getApplicantGender(),
                        f.getApplicantGender());
                return false;
            }
            //投保人年龄
            if (!PremUtil.verifyAge(vo.getApplicantAge(), f.getApplicantAge())) {
                log.info("[协议]投保计划不匹配请求参数={},费率表参数={}", vo.getApplicantAge(), f.getApplicantAge());
                return false;
            }
            //被保人年龄
            if (!PremUtil.verifyAge(vo.getInsuredAge(), f.getInsuredAge())) {
                log.info("被保人年龄不匹配请求参数={},费率表参数={}", vo.getInsuredAge(), f.getInsuredAge());
                return false;
            }
            //保障期间
            if (!PremUtil.verifyParam(vo.getCoveragePeriod(), f.getCoveragePeriod())) {
                log.info("[协议]保障期间不匹配请求参数={},费率表参数={}", vo.getCoveragePeriod(),
                        f.getCoveragePeriod());
                return false;
            }
            //缴费期间类型
            if (!PremUtil.verifyParam(vo.getPaymentPeriodType(), f.getPaymentPeriodType())) {
                log.info("[协议]缴费间期不匹配请求参数={},费率表参数={}", vo.getPaymentPeriodType(),
                        f.getPaymentPeriodType());
                return false;
            }
            //缴费方式
            if (!PremUtil.verifyParam(vo.getPaymentType(), f.getPaymentType())) {
                log.info("[协议]缴费方式不匹配请求参数={},费率表参数={}", vo.getPaymentType(), f.getPaymentType());
                return false;
            }
            //缴费期
            if (!PremUtil.verifyParam(vo.getPaymentPeriod(), f.getPaymentPeriod())) {
                log.info("缴费期不匹配请求参数={},费率表参数={}", vo.getPaymentPeriod(), f.getPaymentPeriod());
                return false;
            }
            //是否为自保件
            if (!PremUtil.verifyParam(vo.getSelfPreservation(), f.getSelfPreservation())) {
                log.info("[协议]是否为自保件不匹配请求参数={},费率表参数={}", vo.getSelfPreservation(),
                        f.getSelfPreservation());
                return false;
            }
            return true;
        }).collect(Collectors.toList());
    }

    /**
     * 获取费率信息
     *
     * @param protocolProductPrem 费率文件
     * @param renewalYear         续投,续期 期次
     * @return
     */
    private static PolicyProductPremResult extractProductPrem(ProtocolProductPremEntity protocolProductPrem,
                                                              Integer renewalYear) {
        //匹配费率信息
        String yearRate = "";
        switch (renewalYear) {
            case 1: {
                yearRate = protocolProductPrem.getFirstYearBrokage();
                break;
            }
            case 2: {
                yearRate = protocolProductPrem.getRenewalBrokage2year();
                break;
            }
            case 3: {
                yearRate = protocolProductPrem.getRenewalBrokage3year();
                break;
            }
            case 4: {
                yearRate = protocolProductPrem.getRenewalBrokage4year();
                break;
            }
            case 5: {
                yearRate = protocolProductPrem.getRenewalBrokage5year();
                break;
            }
            case 6: {
                yearRate = protocolProductPrem.getRenewalBrokage6year();
                break;
            }
            default: {
                if (StrUtil.isNotBlank(protocolProductPrem.getRenewalAutoExpand())) {
                    String[] ranges = protocolProductPrem.getRenewalAutoExpand().split("\\|");
                    for (String range : ranges) {
                        String[] parts = range.split("_");
                        String[] nums = parts[0].split("-");
                        int start = Integer.parseInt(nums[0]);
                        int end = Integer.parseInt(nums[1]);
                        double percentage = Double.parseDouble(parts[1].substring(0, parts[1].length() - 1)) / 100;
                        if (renewalYear >= start && renewalYear <= end) {
                            yearRate = String.valueOf(percentage);
                            break;
                        }
                    }
                }
                break;
            }
        }
        if (StrUtil.isBlank(yearRate)) {
            return null;
        }
        PolicyProductPremResult result = new PolicyProductPremResult();
        try {
            // 如果解析失败了,那么就提示去查一单一议的数据
            result.setYearRate(new BigDecimal(yearRate));
            result.setIsCustomYearRate(StatusEnum.INVALID.getCode());
        } catch (Exception e) {
            if (!"一单一议".equals(yearRate)) {
                log.warn("匹配到的年费率就是数字也不是一单一议请检查:{}", yearRate);
            }
            result.setYearRate(BigDecimal.ZERO);
            result.setIsCustomYearRate(StatusEnum.NORMAL.getCode());
        }

        String taxRate = protocolProductPrem.getTaxRate();
        if (StrUtil.isEmpty(taxRate)) {
            result.setIsCustomTaxRate(StatusEnum.INVALID.getCode());
            result.setTaxRate(new BigDecimal("1"));
        } else {
            try {
                // 如果解析失败了,那么就提示去查一单一议的数据
                result.setTaxRate(new BigDecimal(taxRate));
                result.setIsCustomTaxRate(StatusEnum.INVALID.getCode());
            } catch (Exception e) {
                if (!"一单一议".equals(taxRate)) {
                    log.warn("[协议]匹配到的税率就是数字也不是一单一议请检查:{}", taxRate);
                }
                result.setTaxRate(new BigDecimal("1"));
                result.setIsCustomTaxRate(StatusEnum.NORMAL.getCode());
            }
        }


        return result;
    }

    /**
     * 校验代理的机构信息和险种编码是否可以正常匹配
     *
     * @param orgCode       代理人机构编码
     * @param effectiveDate 协议生效时间 yyyy-MM-dd
     * @param productCode   小鲸险种编码
     * @return
     */
    public static void checkOrgCodeAndProductCodeIsMate(String orgCode, String effectiveDate, String productCode) {
        //校验其他必填项是否给了
        if (!StrUtil.isAllNotBlank(orgCode, effectiveDate, productCode)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("请求参数存在必填项为空,请检查."));
        }
        //处理一下承保时间变成当天0点
        effectiveDate = DateUtil.parseDate(effectiveDate).toString();
        //根据险种编码获取协议产品编码
        ProtocolInsuranceProductInfoOut protocolInsuranceProduct =
                protocolBaseHelper.protocolInsuranceProductProductService.findProtocolInsuranceProductInfo(productCode,
                        ReconcileTypeEnum.PROTOCOL.getCode());
        if (protocolInsuranceProduct == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("险种没有配置协议产品"));
        }
        //兼容处理传进来的代理人机构编码不是机构,需要获取到他最近的一级机构编码
        List<OrgInfoEntity> orgInfoList = protocolBaseHelper.orgInfoService.lambdaQuery().list();
        OrgInfoEntity orgInfo = PremUtil.getOrgCode(orgCode, orgInfoList);
        orgCode = orgInfo.getOrgCode();
        //过滤保司协议险种编码
        List<String> orgCodeList = protocolBaseHelper.protocolProductPremService.lambdaQuery()
                .eq(ProtocolProductPremEntity::getInsuranceProductCode, protocolInsuranceProduct.getInsuranceProductCode())
                .le(ProtocolProductPremEntity::getEffectiveStartDate, effectiveDate)
                .ge(ProtocolProductPremEntity::getEffectiveEndDate, effectiveDate).list().stream()
                .map(ProtocolProductPremEntity::getOrgCode).distinct().collect(Collectors.toList());

        if (CollUtil.isEmpty(orgCodeList)) {
            throw new GlobalException(
                    BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("险种{}无法匹配到协议", productCode)));
        }
        if (CollUtil.newArrayList(Constant.NATIONWIDE_ORG_CODE, orgCode).stream().noneMatch(orgCodeList::contains)) {
            throw new GlobalException(
                    BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("主代理人无权销售险种{}", productCode)));
        }
    }

    /**
     * 校验机构信息是否正确
     *
     * @param inputList
     * @return
     */
    public static List<CheckOrgListIsMateOut> checkOrgListIsMate(List<CheckOrgListIsMateInput> inputList) {
        // 获取所有险种信息
        List<String> productList =
                inputList.stream().map(CheckOrgListIsMateInput::getProductCode).distinct().collect(Collectors.toList());
        // 获取险种与协议产品的关系.
        Map<String, ProtocolInsuranceProductInfoOut> productInfoMap =
                protocolBaseHelper.protocolInsuranceProductProductService.findProtocolInsuranceProductList(productList, 0)
                        .stream()
                        .collect(Collectors.toMap(ProtocolInsuranceProductInfoOut::getProductCode, Function.identity()));
        if (productInfoMap.isEmpty()) {
            // 直接全量返回险种未管关联协议产品
            return inputList.stream().map(m -> {
                CheckOrgListIsMateOut result = BeanUtil.copyProperties(m, CheckOrgListIsMateOut.class);
                result.setIsMate(1);
                result.setErrorMateMsg("险种未管关联协议产品");
                return result;
            }).collect(Collectors.toList());
        }
        // 获取协议产品对应的费率表数据 应该不会很多
        List<String> insuranceProductCodeList =
                productInfoMap.values().stream().map(ProtocolInsuranceProductInfoOut::getInsuranceProductCode).distinct()
                        .collect(Collectors.toList());
        Map<String, List<ProtocolProductPremEntity>> insuranceProductPremMap =
                protocolBaseHelper.protocolProductPremService.lambdaQuery()
                        .select(ProtocolProductPremEntity::getInsuranceProductCode,
                                ProtocolProductPremEntity::getEffectiveStartDate, ProtocolProductPremEntity::getEffectiveEndDate,
                                ProtocolProductPremEntity::getOrgCode)
                        .in(ProtocolProductPremEntity::getInsuranceProductCode, insuranceProductCodeList)
                        .groupBy(ProtocolProductPremEntity::getInsuranceProductCode,
                                ProtocolProductPremEntity::getEffectiveStartDate, ProtocolProductPremEntity::getEffectiveEndDate,
                                ProtocolProductPremEntity::getOrgCode).list().stream()
                        .collect(Collectors.groupingBy(ProtocolProductPremEntity::getInsuranceProductCode));
        //兼容处理传进来的代理人机构编码不是机构,需要获取到他最近的一级机构编码
        List<OrgInfoEntity> orgInfoList = protocolBaseHelper.orgInfoService.lambdaQuery().list();
        // 处理返回数据
        return inputList.stream().map(m -> {
            CheckOrgListIsMateOut result = BeanUtil.copyProperties(m, CheckOrgListIsMateOut.class);
            //处理一下承保时间变成当天0点
            DateTime effectiveDate = DateUtil.parseDate(result.getEffectiveDate());
            OrgInfoEntity orgInfo = PremUtil.getOrgCode(m.getOrgCode(), orgInfoList);
            // 校验险种
            if (productInfoMap.containsKey(result.getProductCode())) {
                ProtocolInsuranceProductInfoOut protocolInsuranceProductInfo =
                        productInfoMap.get(result.getProductCode());
                if (insuranceProductPremMap.containsKey(protocolInsuranceProductInfo.getInsuranceProductCode())) {
                    List<String> orgCodeList =
                            insuranceProductPremMap.get(protocolInsuranceProductInfo.getInsuranceProductCode()).stream()
                                    .filter(p -> {
                                        // 获取时间满足的数据
                                        return DateUtil.isIn(effectiveDate, DateUtil.parseDate(p.getEffectiveStartDate()),
                                                DateUtil.parseDate(p.getEffectiveEndDate()));
                                    }).map(ProtocolProductPremEntity::getOrgCode).collect(Collectors.toList());
                    if (CollUtil.isEmpty(orgCodeList)) {
                        result.setIsMate(1);
                        result.setErrorMateMsg(
                                StrUtil.format("险种匹配到协议产品编码[{}],协议产品[{}],根据时间[{}]没有命中到费率表",
                                        protocolInsuranceProductInfo.getInsuranceProductCode(),
                                        protocolInsuranceProductInfo.getInsuranceProductName(), effectiveDate.toString()));
                    } else {
                        if (CollUtil.newArrayList(Constant.NATIONWIDE_ORG_CODE, orgInfo.getOrgCode()).stream()
                                .noneMatch(orgCodeList::contains)) {
                            result.setIsMate(1);
                            result.setErrorMateMsg(StrUtil.format(
                                    "险种匹配到协议产品编码[{}],协议产品[{}],根据时间[{}]命中费率表适用分公司编码{},根据组织编码[]没有命中费率表",
                                    protocolInsuranceProductInfo.getInsuranceProductCode(),
                                    protocolInsuranceProductInfo.getInsuranceProductName(), effectiveDate.toString(),
                                    JSONUtil.toJsonStr(orgCodeList), orgInfo.getOrgCode()));
                        } else {
                            result.setIsMate(0);
                            result.setErrorMateMsg("成功命中费率表");
                        }
                    }
                } else {
                    result.setIsMate(1);
                    result.setErrorMateMsg(
                            StrUtil.format("险种匹配到协议产品编码[{}],协议产品[{}],但是没有未上传费率表",
                                    protocolInsuranceProductInfo.getInsuranceProductCode(),
                                    protocolInsuranceProductInfo.getInsuranceProductName()));
                }
            } else {
                result.setIsMate(1);
                result.setErrorMateMsg("险种未关联协议产品");
            }
            return result;
        }).collect(Collectors.toList());
    }

    /**
     * 根据险种编码 + 时间 获取适用保司信息
     *
     * @param params
     * @return
     */
    public static List<ProductOrgListOut> findProductOrgList(ProductOrgListVo params) {
        log.info("获取险种={} 时间={} 的适用保司信息", params.getProductCode(), params.getEffectiveDate());
        List<ProtocolInsuranceProductProductEntity> insuranceProductProductList =
                protocolBaseHelper.protocolInsuranceProductProductService.lambdaQuery()
                        .eq(ProtocolInsuranceProductProductEntity::getProductCode, params.getProductCode()).list();
        if (insuranceProductProductList.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> insuranceProductCodeList =
                insuranceProductProductList.stream().map(ProtocolInsuranceProductProductEntity::getInsuranceProductCode)
                        .collect(Collectors.toList());
        ProtocolInsuranceProductEntity protocolInsuranceProduct =
                protocolBaseHelper.protocolInsuranceProductService.lambdaQuery()
                        .in(ProtocolInsuranceProductEntity::getInsuranceProductCode, insuranceProductCodeList)
                        .eq(ProtocolInsuranceProductEntity::getReconcileType, params.getReconcileType()).one();
        if (protocolInsuranceProduct == null) {
            return Collections.emptyList();
        }
        log.info("获取险种={}获取到产品关系信息={}", params.getProductCode(),
                JSONUtil.toJsonStr(protocolInsuranceProduct));
        String effectiveDate = DateUtil.parseDate(params.getEffectiveDate()).toString();
        //获取费率表数据
        Map<String, List<ProtocolProductPremEntity>> protocolProductPremMap =
                protocolBaseHelper.protocolProductPremService.lambdaQuery()
                        .eq(ProtocolProductPremEntity::getInsuranceProductCode,
                                protocolInsuranceProduct.getInsuranceProductCode())
                        .le(ProtocolProductPremEntity::getEffectiveStartDate, effectiveDate)
                        .ge(ProtocolProductPremEntity::getEffectiveEndDate, effectiveDate).list().stream()
                        .collect(Collectors.groupingBy(ProtocolProductPremEntity::getProtocolCode));
        if (protocolProductPremMap.isEmpty()) {
            return Collections.emptyList();
        }
        log.info("获取险种={}获取费率信息={}", params.getProductCode(),
                JSONUtil.toJsonStr(protocolProductPremMap.values()));
        // 获取协议编码集合
        Map<String, ProtocolBasicInfoEntity> protocolBasicInfoMap =
                protocolBaseHelper.protocolBasicInfoService.lambdaQuery()
                        .in(ProtocolBasicInfoEntity::getProtocolCode, new ArrayList<>(protocolProductPremMap.keySet())).list()
                        .stream().collect(Collectors.toMap(ProtocolBasicInfoEntity::getProtocolCode, v -> v));

        log.info("获取险种={}获取协议信息={}", params.getProductCode(),
                JSONUtil.toJsonStr(protocolBasicInfoMap.values()));
        // 获取协议信息
        List<ProductOrgListOut> resultList = new ArrayList<>();
        protocolProductPremMap.forEach((protocolCode, protocolProductPrem) -> {
            try {
                ProtocolBasicInfoEntity protocolBasicInfo = protocolBasicInfoMap.get(protocolCode);
                if (protocolBasicInfo != null) {
                    ProductOrgListOut result = BeanUtil.copyProperties(protocolBasicInfo, ProductOrgListOut.class);
                    List<String> orgCodeList =
                            protocolProductPrem.stream().map(ProtocolProductPremEntity::getOrgCode).distinct()
                                    .collect(Collectors.toList());
                    result.setOrgCodeList(orgCodeList);
                    resultList.add(result);
                }
            } catch (Exception e) {
                log.warn("协议编码={}费率信息={},处理异常", protocolCode, JSONUtil.toJsonStr(protocolProductPrem), e);
            }
        });
        return resultList;
    }

    public static void main(String[] args) {
        DateTime insuredBirthday = DateUtil.parse("2023-07-07 00:00:00");
        DateTime approvedTime = DateUtil.parse("2023-07-08 00:00:00");
        if (DateUtil.compare(insuredBirthday, approvedTime) < 1) {
            System.out.println(DateUtil.age(insuredBirthday, approvedTime));
        } else {
            System.out.println("xxx");
        }
    }

    /**
     * 将保单入参转换成用于匹配费率的参数
     *
     * @param input             查询条件
     * @param reconcileTypeEnum 结算类型0:协议 1:合约 2:基础佣金
     * @return
     */
    public static PolicyProductPrem policyPremToPremInfo(PolicyProductPremInput input,
                                                         ReconcileTypeEnum reconcileTypeEnum) {
        try {
            log.info("[{}]第一步:外部请求获取费率入参={}", reconcileTypeEnum.getDesc(), JSONUtil.toJsonStr(input));
            if (StrUtil.isBlank(input.getProductCode())) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("获取费率缺失险种编码"));
            } else if (input.getSalesType() == null) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("获取费率缺失销售类型"));
            } else if (input.getApprovedTime() == null) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("获取费率缺失承保时间"));
            }

            PolicyProductPrem premInfoVo = new PolicyProductPrem();
            premInfoVo.setChannelCode(input.getChannelCode());
            premInfoVo.setPolicyNo(input.getPolicyNo());
            premInfoVo.setProductCode(input.getProductCode());
            premInfoVo.setMainProductCode(input.getMainProductCode());
            if (reconcileTypeEnum == ReconcileTypeEnum.PROTOCOL || reconcileTypeEnum == ReconcileTypeEnum.CONTRACT) {
                ProtocolInsuranceProductInfoOut protocolInsuranceProduct =
                        protocolBaseHelper.protocolInsuranceProductProductService.findProtocolInsuranceProductInfo(
                                input.getProductCode(), reconcileTypeEnum.getCode());
                if (protocolInsuranceProduct == null) {
                    log.warn("[{}]小鲸险种编码{}没有匹配到产品信息", reconcileTypeEnum.getDesc(),
                            input.getProductCode());
                    throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                            StrUtil.format("小鲸险种编码{}没有匹配到[{}]信息", input.getProductCode(),
                                    reconcileTypeEnum.getDesc())));
                }
                premInfoVo.setInsuranceProductCode(protocolInsuranceProduct.getInsuranceProductCode());
                premInfoVo.setProductPlan(protocolInsuranceProduct.getProductPlan());

                if (StrUtil.isNotBlank(input.getMainProductCode())) {
                    ProtocolInsuranceProductInfoOut mainProtocolInsuranceProduct =
                            protocolBaseHelper.protocolInsuranceProductProductService.findProtocolInsuranceProductInfo(
                                    input.getMainProductCode(), reconcileTypeEnum.getCode());
                    if (mainProtocolInsuranceProduct == null) {
                        log.warn("[{}]小鲸险种编码{}没有匹配到产品信息", reconcileTypeEnum.getDesc(),
                                input.getProductCode());
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                                StrUtil.format("小鲸险种编码{}没有匹配到产品信息", input.getProductCode())));
                    }
                    premInfoVo.setMainInsuranceProductCode(mainProtocolInsuranceProduct.getInsuranceProductCode());
                }
                //投保计划
                if (StrUtil.isNotBlank(input.getPlantCode())) {
                    log.info("[{}]匹配产品协议id={}计划编码={}", reconcileTypeEnum.getDesc(),
                            protocolInsuranceProduct.getProductId(), input.getPlantCode());
                    ProtocolInsuranceProductProductPlanEntity protocolInsuranceProductProductPlan =
                            protocolBaseHelper.protocolInsuranceProductProductPlanService.lambdaQuery()
                                    .eq(ProtocolInsuranceProductProductPlanEntity::getProductId,
                                            protocolInsuranceProduct.getProductId())
                                    .eq(ProtocolInsuranceProductProductPlanEntity::getPlanCode, input.getPlantCode()).one();
                    if (protocolInsuranceProductProductPlan != null) {
                        premInfoVo.setProductPlan(protocolInsuranceProductProductPlan.getProductPlan());
                    }
                }
            } else if (ReconcileTypeEnum.COMMISSION.getCode().equals(reconcileTypeEnum.getCode())) {
                premInfoVo.setProductPlan(input.getPlantCode());
            }

            //是否为自保件
            premInfoVo.setSelfPreservation(
                    StatusEnum.NORMAL.getCode().equals(input.getSelfPreservation()) ? "是" : "否");
            //保障期间
            PolicyInsuredPeriodTypeEnum insuredTypeEnum =
                    PolicyInsuredPeriodTypeEnum.getInsuredTypeEnumByCode(input.getInsuredPeriodType());
            if (insuredTypeEnum == null) {
                log.warn("[{}]校验保障期间参数出现异常:{}", reconcileTypeEnum.getDesc(), JSONUtil.toJsonStr(input));
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保障期间参数错误"));
            }
            String coveragePeriod = insuredTypeEnum.getInsuredDesc();
            if (insuredTypeEnum != PolicyInsuredPeriodTypeEnum.LIFE && insuredTypeEnum != PolicyInsuredPeriodTypeEnum.OTHER) {
                coveragePeriod += ("_" + input.getInsuredPeriod());
            }
            premInfoVo.setCoveragePeriod(coveragePeriod);
            //TODO 满期年龄
            // 投保人年龄-被保人年龄
            if (input.getApplicantGender() != null) {
                if (StatusEnum.NORMAL.getCode().equals(input.getApplicantGender())) {
                    premInfoVo.setApplicantGender("男");
                } else if (StatusEnum.INVALID.getCode().equals(input.getApplicantGender())) {
                    premInfoVo.setApplicantGender("女");
                }
            }
            if (input.getInsuredGender() != null) {
                if (StatusEnum.NORMAL.getCode().equals(input.getInsuredGender())) {
                    premInfoVo.setInsuredGender("男");
                } else if (StatusEnum.INVALID.getCode().equals(input.getInsuredGender())) {
                    premInfoVo.setInsuredGender("女");
                }
            }
            // 投保人性别-被保人性别
            if (input.getApplicantBirthday() != null) {
                premInfoVo.setApplicantAge(DateUtil.age(input.getApplicantBirthday(), input.getApprovedTime()));
            }
            premInfoVo.setInsuredAge(input.getInsuredPolicyAge());
            try {
                if (input.getInsuredBirthday() != null) {
                    //如果出生日期晚于承保时间 那么被保人年龄就按0岁计算
                    if (DateUtil.compare(input.getInsuredBirthday(), input.getApprovedTime()) < 1) {
                        premInfoVo.setInsuredAge(DateUtil.age(input.getInsuredBirthday(), input.getApprovedTime()));
                    }
                }
            } catch (Exception e) {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("计算被保人年龄异常,请检查出生日期"));
            }

            // 新单/续投
            premInfoVo.setInsuranceType(input.getInsuranceType());
            // 缴费期
            PolicyPaymentPeriodTypeEnum paymentPeriodTypeEnum =
                    PolicyPaymentPeriodTypeEnum.getPaymentPeriodTypeEnumByCode(input.getPaymentPeriodType());
            if (paymentPeriodTypeEnum == null) {
                log.warn("[{}]校验缴费期参数出现异常:{}", reconcileTypeEnum.getDesc(), JSONUtil.toJsonStr(input));
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("缴费期参数错误"));
            }
            if (paymentPeriodTypeEnum == PolicyPaymentPeriodTypeEnum.YEAR) {
                premInfoVo.setPaymentPeriod(input.getPaymentPeriod().toString());
            } else if (paymentPeriodTypeEnum == PolicyPaymentPeriodTypeEnum.AGE) {
                int paymentPeriod =
                        input.getPaymentPeriod() - (premInfoVo.getInsuredAge() == null ? 0 : premInfoVo.getInsuredAge());
                premInfoVo.setPaymentPeriod(paymentPeriod + "");
            } else {
                premInfoVo.setPaymentPeriod("1");
            }
            premInfoVo.setPaymentPeriodType(paymentPeriodTypeEnum.getPeriodDesc());
            // 渠道信息
            premInfoVo.setChannelCode(input.getChannelCode());
            premInfoVo.setReportOrgCode(Constant.ZB_ORG_CODE);
            premInfoVo.setReportOrgName(Constant.ZB_ORG_NAME);
            //适用分公司组织ID 没有的就是全国
            if (StrUtil.isBlank(input.getOrgCode())) {
                premInfoVo.setOrgCode(Constant.NATIONWIDE_ORG_CODE);
            } else if (!Constant.NATIONWIDE_ORG_CODE.equals(input.getOrgCode())) {
                //兼容一下 获取最近的一级机构
                List<OrgInfoEntity> orgInfoList = protocolBaseHelper.orgInfoService.lambdaQuery().list();
                OrgInfoEntity orgInfo = PremUtil.getOrgCode(input.getOrgCode(), orgInfoList);
                premInfoVo.setOrgCode(orgInfo.getOrgCode());
                if (StrUtil.isNotBlank(orgInfo.getOrgCode())
                        && input.getSalesType() == 1
                        && !Constant.NATIONWIDE_ORG_CODE.equals(orgInfo.getOrgCode())) {
                    premInfoVo.setReportOrgCode(orgInfo.getOrgCode());
                    premInfoVo.setReportOrgName(orgInfo.getOrgName());
                }
            }
            //承保时间
            premInfoVo.setEffectiveDate(DateUtil.formatDateTime(input.getApprovedTime()));
            //缴费方式
            PolicyPaymentTypeEnum paymentTypeEnum =
                    PolicyPaymentTypeEnum.getPaymentTypeEnumByCode(input.getPeriodType());
            if (paymentTypeEnum == null) {
                log.warn("[{}]校验缴费方式参数出现异常:{}", reconcileTypeEnum.getDesc(), JSONUtil.toJsonStr(input));
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("缴费方式参数错误"));
            }
            if (paymentTypeEnum == PolicyPaymentTypeEnum.YCJQ || paymentTypeEnum == PolicyPaymentTypeEnum.DJ || paymentTypeEnum == PolicyPaymentTypeEnum.BDQJ || paymentTypeEnum == PolicyPaymentTypeEnum.DXYCX) {
                premInfoVo.setPaymentType(PolicyPaymentTypeEnum.NJ.getPaymentDesc());
            } else {
                premInfoVo.setPaymentType(paymentTypeEnum.getPaymentDesc());
            }
            // 续期年期
            //todo 新单第一年,续投判断这个险种是否需要强制校验续投期数-蓝医保待确定..是否可以考虑单独在代码里面加这个险种编码然后进行处理???
            premInfoVo.setRenewalYear(input.getRenewalYear() == null ? 1 : input.getRenewalYear());
            // todo 续投的单子先把他的年期由1变成2,根缴费期有关系
            if (premInfoVo.getInsuranceType().equals(1) && premInfoVo.getRenewalYear() < 2) {
                premInfoVo.setRenewalYear(2);
            }
            // 续期期数(暂时未使用)
            premInfoVo.setRenewalPeriod(input.getRenewalPeriod() == null ? 1 : input.getRenewalPeriod());
            log.info("[{}]第二步:将保单入参转换成用于匹配费率的参数={}", reconcileTypeEnum.getDesc(),
                    JSONUtil.toJsonStr(premInfoVo));
            // 报送机构如果是江苏那么将他设置为总公司
            if (TRANSFER_HEADQUARTERS.contains(premInfoVo.getReportOrgCode())) {
                premInfoVo.setReportOrgCode(Constant.ZB_ORG_CODE);
                premInfoVo.setReportOrgName(Constant.ZB_ORG_NAME);
            }
            return premInfoVo;
        } catch (GlobalException e) {
            log.warn("[{}]保单数据转费率基础数据转换异常", reconcileTypeEnum.getDesc(), e);
            throw e;
        } catch (Exception e) {
            log.warn("[{}]保单数据转费率基础数据转换异常", reconcileTypeEnum.getDesc(), e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("解析保单数据异常={}", e.getMessage())));
        }
    }

    public static PolicyTaxPremResult queryPolicyTaxPrem(PolicyPremInput input, int reconcileType) {
        log.info("协议查询税率一单一议入参={}", JSONUtil.toJsonStr(input));
        List<SettlementPolicyProductFeePremEntity> premList =
                protocolBaseHelper.policyProductFeePremService.lambdaQuery()
                        .eq(SettlementPolicyProductFeePremEntity::getPolicyNo, input.getPolicyNo())
                        .eq(SettlementPolicyProductFeePremEntity::getReconcileType, reconcileType).list();
        if (premList.isEmpty()) {
            log.info("协议查询税率一单一议入参={}没有匹配到数据", JSONUtil.toJsonStr(input));
            return null;
        }
        // 判断有没有满足险种的数据,没有的话取 险种编码为空的数据
        if (StrUtil.isBlank(input.getProductCode())) {
            premList = premList.stream().filter(f -> StrUtil.isBlank(f.getProductCode())).collect(Collectors.toList());
        } else {
            List<SettlementPolicyProductFeePremEntity> resultList = premList.stream().filter(
                            f -> StrUtil.isNotBlank(f.getProductCode()) && input.getProductCode().equals(f.getProductCode()))
                    .collect(Collectors.toList());
            if (resultList.isEmpty()) {
                resultList =
                        premList.stream().filter(f -> StrUtil.isBlank(f.getProductCode())).collect(Collectors.toList());
            }
            premList = resultList;
        }
        if (premList.isEmpty()) {
            return null;
        }
        // 判断有没有满足产品的数据,没有的话取 险种编码为空的数据
        if (StrUtil.isBlank(input.getInsuranceProductCode())) {
            premList = premList.stream().filter(f -> StrUtil.isBlank(f.getInsuranceProductCode())).collect(Collectors.toList());
        } else {
            List<SettlementPolicyProductFeePremEntity> resultList = premList.stream().filter(
                            f -> StrUtil.isNotBlank(f.getInsuranceProductCode()) && input.getInsuranceProductCode().equals(f.getInsuranceProductCode()))
                    .collect(Collectors.toList());
            if (resultList.isEmpty()) {
                resultList =
                        premList.stream().filter(f -> StrUtil.isBlank(f.getInsuranceProductCode())).collect(Collectors.toList());
            }
            premList = resultList;
        }
        if (premList.isEmpty()) {
            return null;
        }

        // 批单号同险种编码
        if (StrUtil.isBlank(input.getBatchCode())) {
            premList = premList.stream().filter(f -> StrUtil.isBlank(f.getBatchCode())).collect(Collectors.toList());
        } else {
            List<SettlementPolicyProductFeePremEntity> resultList = premList.stream()
                    .filter(f -> StrUtil.isNotBlank(f.getBatchCode()) && input.getBatchCode().equals(f.getBatchCode()))
                    .collect(Collectors.toList());
            if (resultList.isEmpty()) {
                resultList =
                        premList.stream().filter(f -> StrUtil.isBlank(f.getBatchCode())).collect(Collectors.toList());
            }
            premList = resultList;
        }
        if (premList.isEmpty()) {
            return null;
        }

        if (premList.size() > 1) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("获取一单一议请求参数={},匹配到了多个费率信息", JSONUtil.toJsonStr(input))));
        }
        PolicyTaxPremResult taxPremResult = premList.stream().map(m -> {
            PolicyTaxPremResult result = new PolicyTaxPremResult();
            result.setPremCode(m.getPremCode());
            result.setPremium(m.getPremium());
            result.setTaxAfterPremium(m.getTaxAfterPremium());
            result.setProductCode(m.getProductCode());
            result.setProductName(m.getProductName());
            result.setTaxRate(m.getTaxRate());
            result.setSettlementMethod(m.getSettlementMethod());
            return result;
        }).findFirst().get();
        log.info("[协议]保单号={}一单一议成功匹配费率信息={}", input.getPolicyNo(),
                 JSONUtil.toJsonStr(taxPremResult));
        return taxPremResult;
    }

}
