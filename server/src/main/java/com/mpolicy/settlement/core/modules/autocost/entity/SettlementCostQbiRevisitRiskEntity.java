package com.mpolicy.settlement.core.modules.autocost.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 支出结算保险风险预警名单
 * 
 * <AUTHOR>
 * @date 2024-02-29 21:03:20
 */
@TableName("settlement_cost_qbi_revisit_risk")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostQbiRevisitRiskEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 区域编号
	 */
	private String regionCode;
	/**
	 * 区域名称
	 */
	private String regionName;
	/**
	 * 分支机构编码
	 */
	private String orgCode;
	/**
	 * 分会机构名称
	 */
	private String orgName;
	/**
	 * 员工编号
	 */
	private String employeeCode;
	/**
	 * 员工名称
	 */
	private String employeeName;
	/**
	 * 被保人客户姓名
	 */
	private String insuredCustName;
	/**
	 * 被保人客户信贷编号
	 */
	private String insuredLoanCustId;
	/**
	 * 投保人客户姓名
	 */
	private String insureCustName;
	/**
	 * 投保人客户信贷编号
	 */
	private String insureLoanCustId;
	/**
	 * 保险订单id
	 */
	private String insuranceOrderId;
	/**
	 * 保单号
	 */
	private String policyNo;
	/**
	 * 类型;1是保单支付，-1是退保
	 */
	private String insuranceType;
	/**
	 * 保险产品名称
	 */
	private String insuranceProductName;
	/**
	 * 保险金额
	 */
	private BigDecimal insuranceAmt;
	/**
	 * 订单时间
	 */
	private String orderTime;
	/**
	 * 被保人最近放款时间
	 */
	private String insuredBankTime;
	/**
	 * 投保人最近放款时间
	 */
	private String insureBankTime;
	/**
	 * 前后3月预警标识
	 */
	@TableField(value = "is_insured_bank_3m")
	private String isInsuredBank3m;
	/**
	 * 前后1月预警标识
	 */
	@TableField(value = "is_insured_bank_1m")
	private String isInsuredBank1m;
	/**
	 * 前后7天预警标识
	 */
	@TableField(value = "is_insured_bank_7d")
	private String isInsuredBank7d;
	/**
	 * 前后3天预警标识
	 */
	@TableField(value = "is_insured_bank_3d")
	private String isInsuredBank3d;
	/**
	 * 前后1天预警标识
	 */
	@TableField(value = "is_insured_bank_1d")
	private String isInsuredBank1d;
	/**
	 * 是否昨日新增
	 */
	private String isAddLastday;
	/**
	 * 保险期数
	 */
	private Integer insuranceTermNum;
	/**
	 * 快照分区
	 */
	private Integer pt;
	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
}
