package com.mpolicy.settlement.core.modules.autocost.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.mpolicy.settlement.core.modules.autocost.enums.ManualTargetGrantStatueEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.Objects;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = true)
@ApiModel("人工导入指标信息")
public class ManualImportTargetDto extends BaseRowModel implements Serializable {
    /**
     * 区域名称
     */
    @ExcelProperty(value="所属区域", index = 6)
    private String regionName;

    /**
     * 机构名称
     */
    @ExcelProperty(value="所属机构", index = 7)
    private String orgName;



    /**
     * 员工名称
     */
    @ExcelProperty(value="推荐人/管护经理姓名", index = 8)
    private String employeeName;
    /**
     * 员工工号
     */
    @ExcelProperty(value="推荐人/管护经理编号", index = 9)
    private String employeeCode;
    /**
     * 投保人姓名
     */
    @ExcelProperty(value="投保人姓名", index = 10)
    private String applicationName;

    /**
     * 被保人姓名
     */
    @ExcelProperty(value="被保人姓名", index = 11)
    private String insuredName;
    /**
     * 产品名称
     */
    @ExcelProperty(value="投保产品名称", index = 12)
    private String productName;

    /**
     * 保单号
     */
    @ExcelProperty(value="保单号", index = 13)
    private String policyNo;

    @ExcelProperty(value="保单状态", index = 15)
    private String policyStatusStr;

    @ApiModelProperty(value = "保单状态编码" ,hidden = true)
    private Integer policyStatus;
    public Integer getPolicyStatus(){
        if(Objects.equals("承保成功",policyStatusStr)){
            return 1;
        }else{
            return -1;
        }
    }
    /**
     * 创建时间
     */
    @ExcelProperty(value="投保期数", index = 2)
    private Integer renewalPeriod;

    /**
     * 记账时间
     */
    @ExcelProperty(value="记账时间", index = 3)
    private String accountTime;

    /**
     * 订单时间
     */
    @ExcelProperty(value="订单创建日期", index = 4)
    private String orderTime;



    /**
     * 发放状态
     */
    @ExcelProperty(value="发放情况", index = 28)
    private String grantStatusStr;
    @ApiModelProperty(value = "发放状态编码" ,hidden = true)
    private Integer grantStatus;

    public Integer getGrantStatus(){
        if(Objects.equals(grantStatusStr,"未发")){
            return ManualTargetGrantStatueEnum.NOT_SEND.getCode();
        }else if(Objects.equals(grantStatusStr,"已发")){
            return ManualTargetGrantStatueEnum.SENT.getCode();
        }else if(Objects.equals(grantStatusStr,"退保未发")){
            return ManualTargetGrantStatueEnum.SURRENDER_NOT_SEND.getCode();
        }else {
            return ManualTargetGrantStatueEnum.NOT_SEND.getCode();
        }
    }

    /**
     * 前后3月预警标识
     */
    private String isInsuredBank3m;

    public String getIsInsuredBank3m(){
        if(renewalPeriod == 1){
            return "1";
        }else{
            return "0";
        }
    }
}
