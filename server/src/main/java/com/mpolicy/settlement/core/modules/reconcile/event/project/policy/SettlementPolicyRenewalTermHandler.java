package com.mpolicy.settlement.core.modules.reconcile.event.project.policy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.policy.common.policy.renewal.response.PolicyRenewalTermBaseInfo;
import com.mpolicy.policy.common.policy.renewal.response.PolicyRenewalTermInsuredInfo;
import com.mpolicy.policy.common.policy.renewal.response.PolicyRenewalTermOutput;
import com.mpolicy.policy.common.policy.renewal.response.PolicyRenewalTermProductInfo;
import com.mpolicy.product.common.base.ProductBase;
import com.mpolicy.settlement.core.enums.SettlementPolicyHandlerEnum;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.SettlementPolicyFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementPolicyHandler;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.modules.reconcile.vo.BasisSettlementPolicyInfoDomain;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 保单续期转明细
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SettlementPolicyRenewalTermHandler extends SettlementPolicyHandler {

    @Override
    public void afterPropertiesSet() {
        SettlementPolicyFactory.register(SettlementPolicyHandlerEnum.RENEWAL_TERM, this);
    }
    /**
     * 构建基础结算明细(不包含费率匹配)
     *
     * @param domain 生成计算信息条件
     * @return
     */
    @Override
    public List<SettlementPolicyInfoEntity> buildBasisSettlementPolicyInfo(BasisSettlementPolicyInfoDomain domain) {
        //校验一下数据
        this.checkBasisSettlementPolicyInfoDomain(domain);
        // 获取续期详情...
        // 保全编码
        Integer period = domain.getPeriod();
        if (period == null) {
            throw new GlobalException(
                BasicCodeMsg.SERVER_ERROR.setMsg("续期事件,续期期数不能为空,保单号=" + domain.getPolicyNo()));
        }
        // 获取续期详情
        PolicyRenewalTermOutput policyRenewalTermDetail =
            policyCenterBaseClient.getPolicyRenewalTermDetail(domain.getPolicyNo(), domain.getPeriod());
        if (policyRenewalTermDetail == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                "续期详情不存在,保单号=" + domain.getPolicyNo() + "期次=" + domain.getPeriod()));
        }
        PolicyRenewalTermBaseInfo baseInfo = policyRenewalTermDetail.getBaseInfo();
        if (baseInfo == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                "续期基本信息不存在,保单号=" + domain.getPolicyNo() + "期次=" + domain.getPeriod()));
        }
        Date duePaymentTime = baseInfo.getDuePaymentTime();
        Date paymentTime = baseInfo.getPaymentTime();
        if (domain.getSettlementEventTypeEnum() == SettlementEventTypeEnum.RENEWAL_TERM_POLICY
                && (duePaymentTime == null || paymentTime ==null)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                StrUtil.format("续期事件 保单号={}, 缺少应收或实收时间信息", domain.getPolicyNo())));
        }
        // 只有个财才会有续期,其他类型暂时没有续期事件,所以判断一下被保人信息是否存在,如果不存在,那么直接报错
        List<PolicyRenewalTermInsuredInfo> insuredList = policyRenewalTermDetail.getInsuredList();
        if (CollUtil.isEmpty(insuredList)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                "续期详情没有被保人信息,保单号=" + domain.getPolicyNo() + "期次=" + domain.getPeriod()));
        }
        // 获取险种信息
        List<String> productCodes = insuredList.stream()
            .flatMap(f -> f.getProductInfoList().stream().map(PolicyRenewalTermProductInfo::getProductCode)).distinct()
            .collect(Collectors.toList());
        if (CollUtil.isEmpty(productCodes)) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                StrUtil.format("续期事件 保单号={}, 缺少险种信息", domain.getPolicyNo())));
        }
        // 合同信息
        EpContractInfoVo contractInfo = domain.getContractInfo();
        // 获取险种编码和详情map
        Map<String, ProductBase> productMap = this.getProductInfoMapByProductCodes(productCodes);
        // 构建明细基本信息
        SettlementPolicyInfoEntity sourceBean = buildSettlementPolicyInfoBasis(domain);
        // 将他的续期信息替换基础信息
        sourceBean.setPayableTime(duePaymentTime);
        sourceBean.setRealityTime(paymentTime);
        sourceBean.setPaymentPeriodType(baseInfo.getPaymentType());
        sourceBean.setRenewalYear(baseInfo.getPolicyPeriodYear());
        sourceBean.setRenewalPeriod(baseInfo.getPeriod());

        List<SettlementPolicyInfoEntity> settlementPolicyInfoList = new ArrayList<>();
        switch (domain.getPolicyProductTypeEnum()) {
            case PERSONAL:
            case PROPERTY: {
                // 处理个财保单信息
                insuredList.forEach(insured -> insured.getProductInfoList().forEach(x -> {
                    BigDecimal premium = domain.getSettlementEventTypeEnum() == SettlementEventTypeEnum.CHANGE_RENEWAL_FALLBACK ?
                            x.getDuePremium().negate() : x.getPremium();
                    SettlementPolicyInfoEntity bean =
                        BeanUtil.copyProperties(sourceBean, SettlementPolicyInfoEntity.class);
                    bean.setSettlementCode(PolicySettlementUtils.createCodeLastUuid("SC"));
                    // 被保人信息
                    bean.setInsuredName(insured.getInsuredName());
                    bean.setInsuredMobile(insured.getInsuredMobile());
                    bean.setInsuredIdCard(insured.getInsuredIdCard());
                    bean.setInsuredGender(insured.getInsuredGender());
                    bean.setInsuredBirthday(insured.getInsuredBirthday());

                    if (insured.getInsuredBirthday() == null && StrUtil.isNotBlank(
                            insured.getInsuredIdCard()) && IdcardUtil.isValidCard(insured.getInsuredIdCard())) {
                        bean.setInsuredBirthday(IdcardUtil.getBirthDate(insured.getInsuredIdCard()));
                    }

                    bean.setInsuredPolicyAge(insured.getInsuredPolicyAge());
                    try {
                        if (bean.getInsuredBirthday() != null) {
                            //如果出生日期晚于承保时间 那么被保人年龄就按0岁计算
                            log.info(JSONObject.toJSONString(contractInfo));
                            if (DateUtil.compare(bean.getInsuredBirthday(), contractInfo.getContractExtendInfo().getApprovedTime()) < 1) {
                                bean.setInsuredPolicyAge(DateUtil.age(bean.getInsuredBirthday(), domain.getContractInfo().getContractExtendInfo().getApprovedTime()));
                            }
                        }
                    } catch (Exception e) {
                        log.warn("计算被保人年龄异常,请检查出生日期", e);
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("计算被保人年龄异常,请检查出生日期"));
                    }
//                    bean.setInsuredPolicyAge(insured.getInsuredPolicyAge());
                    // 险种信息
                    bean.setProductCode(x.getProductCode());
                    bean.setProductName(x.getProductName());
                    bean.setPlanCode(x.getPlanCode());
                    bean.setPlanName(x.getPlanName());
                    bean.setMainInsurance(x.getMainInsurance());
                    bean.setAdditionalRisksType(x.getAdditionalRisksType());
                    bean.setEffectiveDate(contractInfo.getContractExtendInfo().getEnforceTime());
                    bean.setEndDate(
                        PolicySettlementUtils.getInsuredEndDate(bean.getEffectiveDate(), x.getInsuredPeriod(),
                            x.getInsuredPeriodType()));
                    bean.setCoverage(x.getCoverage());
                    bean.setCoverageUnit(x.getCoverageUnit());
                    bean.setCoverageUnitName(x.getCoverageUnitName());
                    bean.setInsuredPeriodType(x.getInsuredPeriodType());
                    bean.setInsuredPeriod(x.getInsuredPeriod());
                    bean.setPeriodType(x.getPeriodType());
                    bean.setPaymentPeriodType(x.getPaymentPeriodType());
                    bean.setPaymentPeriod(x.getPaymentPeriod());
                    bean.setDrawAge(x.getAnnDrawAge());
                    bean.setPremium(premium);// todo 这里的保费.....
                    bean.setProductPremiumTotal(bean.getPremium());
                    bean.setCopies(x.getCopies());
                    // 如果保费为0 不生成明细
                    if (bean.getPremium().compareTo(BigDecimal.ZERO)==0) {
                        return;
                    }
                    // 其他信息
                    if (productMap.containsKey(x.getProductCode())) {
                        ProductBase productBase = productMap.get(x.getProductCode());
                        if (StrUtil.isBlank(bean.getProductName())){
                            bean.setProductName(productBase.getProductName());
                        }
                        bean.setProductGroup(productBase.getProductGroup());
                        bean.setProductType(productBase.getProductType());
                        bean.setLevel2Code(productBase.getLevel2Code());
                        bean.setLevel3Code(productBase.getLevel3Code());
                        bean.setLongShortFlag(productBase.getLongShortFlag());
                        if (StrUtil.isBlank(productBase.getLevel2Code())){
                            throw new GlobalException(
                                BasicCodeMsg.SERVER_ERROR.setMsg("险种编码=" + x.getProductCode()+"不存在二级分类"));
                        }
                    } else {
                        // 这个情况是不存在的 但是为了严谨一点 加上了...
                        throw new GlobalException(
                            BasicCodeMsg.SERVER_ERROR.setMsg("产品信息不存在，产品编码=" + x.getProductCode()));
                    }
                    settlementPolicyInfoList.add(bean);
                }));
                break;
            }
            case GROUP:
            case VEHICLE: {
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(
                    StrUtil.format("保单类型={},不支持续期事件,保单号={}," + "期数={}",
                        domain.getPolicyProductTypeEnum().getPolicyProductType(), domain.getPolicyNo(),
                        domain.getPeriod())));
            }
        }
        return settlementPolicyInfoList;
    }
}
