package com.mpolicy.settlement.core.modules.autocost.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.common.autocost.SettlementDynamicSubjectDefinition;
import com.mpolicy.settlement.core.modules.autocost.dao.SettlementDynamicSubjectDefinitionDao;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementDynamicSubjectDefinitionEntity;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementDynamicSubjectDefinitionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class SettlementDynamicSubjectDefinitionServiceImpl extends ServiceImpl<SettlementDynamicSubjectDefinitionDao, SettlementDynamicSubjectDefinitionEntity> implements SettlementDynamicSubjectDefinitionService {

    public List<SettlementDynamicSubjectDefinitionEntity> listLikeRightBySubjectName(String subjectName){

        return this.lambdaQuery().likeRight(SettlementDynamicSubjectDefinitionEntity::getSubjectName,subjectName)
                .eq(SettlementDynamicSubjectDefinitionEntity::getStatus,1)
                .orderByDesc(SettlementDynamicSubjectDefinitionEntity::getUpdateTime)
                .list();
    }

    public SettlementDynamicSubjectDefinitionEntity getBySubjectName(String subjectName){

        return this.lambdaQuery().eq(SettlementDynamicSubjectDefinitionEntity::getSubjectName,subjectName)
                .eq(SettlementDynamicSubjectDefinitionEntity::getStatus,1)
                .one();
    }

    public void saveSubjectDefinition(SettlementDynamicSubjectDefinitionEntity entity){
        if(Objects.isNull(entity)){
            return ;
        }
        if(entity.getId()!=null){
            this.lambdaUpdate().set(SettlementDynamicSubjectDefinitionEntity::getSubjectName,entity.getSubjectName())
                    .set(SettlementDynamicSubjectDefinitionEntity::getRemark,entity.getRemark())
                    .set(SettlementDynamicSubjectDefinitionEntity::getUpdateUser,entity.getUpdateUser())
                    .eq(SettlementDynamicSubjectDefinitionEntity::getId,entity.getId())
                    .update();
        }else{
            entity.setCreateUser(entity.getCreateUser());
            this.baseMapper.insert(entity);
        }
    }


    public List<SettlementDynamicSubjectDefinitionEntity> listBySubjectCodes(List<String> subjectCodes){
        if(CollectionUtils.isEmpty(subjectCodes)){
            return Collections.emptyList();
        }
        return this.lambdaQuery().in(SettlementDynamicSubjectDefinitionEntity::getSubjectCode,subjectCodes)
                .list();
    }


}
