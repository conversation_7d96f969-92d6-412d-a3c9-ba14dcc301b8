package com.mpolicy.settlement.core.modules.reconcile.job;

import cn.hutool.core.util.StrUtil;
import com.mpolicy.settlement.core.common.reconcile.company.ReconcileRuleFileTemplate;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileTemplateEnum;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementReconcileFileEntity;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcileFileService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementReconcilePolicyFileService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SettlementReconcilePolicyFileJob {

    @Autowired
    private SettlementReconcileFileService settlementReconcileFileService;

    @Autowired
    private SettlementReconcilePolicyFileService settlementReconcilePolicyFileService;

    @XxlJob("updateReconcilePolicyFileJob")
    public void updateReconcilePolicyFileJob() {
        String reconcileCode = XxlJobHelper.getJobParam();
        settlementReconcileFileService.lambdaQuery()
                .eq(StrUtil.isNotBlank(reconcileCode), SettlementReconcileFileEntity::getReconcileCode, reconcileCode)
                .list().forEach(action -> {
                    try {
                        XxlJobHelper.log("开始解析文件Id={}", action.getId());
                        List<ReconcileRuleFileTemplate> readFile = ReconcileBaseHelper.loadReconcileRuleFileData(Objects.requireNonNull(ReconcileTemplateEnum.matchSearchCode(action.getReconcileFileType())), action.getReconcileFileCode(), action.getReconcileCode());
                        settlementReconcilePolicyFileService.saveReconcileRuleFileTemplate(action.getReconcileCode(), action.getReconcileFileCode(), readFile);
                        XxlJobHelper.log("开始解析文件Id={}完成", action.getId());
                    } catch (Exception e) {
                        XxlJobHelper.log("开始解析文件Id={}失败", action.getId());
                        XxlJobHelper.log(e);
                    }
                });

    }

}
