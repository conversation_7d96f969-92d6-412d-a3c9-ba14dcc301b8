package com.mpolicy.settlement.core.modules.autocost.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.autocost.dao.SettlementCostLogDao;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostLogEntity;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 支出结算日志记录表
 *
 * <AUTHOR>
 * @since 2023-10-31 20:03:47
 */
@Slf4j
@Service("settlementCostLogService")
public class SettlementCostLogServiceImpl extends ServiceImpl<SettlementCostLogDao, SettlementCostLogEntity> implements SettlementCostLogService {

}
