package com.mpolicy.settlement.core.modules.autocost.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.autocost.dao.SettlementCostUploadPcoInfoDao;
import com.mpolicy.settlement.core.modules.autocost.dto.SettlementCostInfoDto;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostUploadPcoInfoEntity;
import com.mpolicy.settlement.core.modules.autocost.service.SettlementCostUploadPcoInfoService;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.MAX_BATCH_INSERT_NUM;

/**
 * 结算导入的pco信息
 *
 * <AUTHOR>
 * @since 2024-06-07 3:34:32
 */
@Slf4j
@Service("settlementCostUploadPcoInfoService")
public class SettlementCostUploadPcoInfoServiceImpl extends ServiceImpl<SettlementCostUploadPcoInfoDao, SettlementCostUploadPcoInfoEntity> implements SettlementCostUploadPcoInfoService {
    @Override
    public int saveList(List<SettlementCostUploadPcoInfoEntity> list) {
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }


        return batchSave(list);
    }

    public int batchSave(List<SettlementCostUploadPcoInfoEntity> list){
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }
        int result = 0;
        // 批量写入
        if (list.size() > MAX_BATCH_INSERT_NUM) {
            List<List<SettlementCostUploadPcoInfoEntity>> partition = ListUtils.partition(list, MAX_BATCH_INSERT_NUM);
            for (List<SettlementCostUploadPcoInfoEntity> x : partition) {
                result = result + baseMapper.insertBatchSomeColumn(x);
            }
        } else {
            result = baseMapper.insertBatchSomeColumn(list);
        }
        return result;
    }
}
