package com.mpolicy.settlement.core.modules.autocost.dto.hr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 结算Hr发放人员信息DTO
 * 
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@ApiModel(value = "Hr发放人员信息", description = "结算Hr发放人员信息")
public class HrGrantPersonInfoDto {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "工号")
    private String sendObjectCode;

    @ApiModelProperty(value = "姓名")
    private String sendObjectName;

    @ApiModelProperty(value = "结算周期")
    private String costSettlementCycle;

    @ApiModelProperty(value = "hr发放标志位 0 hr不发放，1 hr发放")
    private Integer hrGrantFlag;

    @ApiModelProperty(value = "hr发放标志位描述")
    private String hrGrantFlagDesc;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "乐观锁")
    private Integer revision;

    /**
     * 获取hr发放标志位描述
     */
    public String getHrGrantFlagDesc() {
        if (hrGrantFlag == null) {
            return "";
        }
        return hrGrantFlag == 1 ? "hr发放" : "hr不发放";
    }
}
