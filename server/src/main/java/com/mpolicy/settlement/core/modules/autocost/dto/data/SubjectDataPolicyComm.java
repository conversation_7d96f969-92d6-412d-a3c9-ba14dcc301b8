package com.mpolicy.settlement.core.modules.autocost.dto.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 科目数据信息-科目(基础佣金)数据
 *
 * <AUTHOR>
 * @since 2023-11-02 20:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "科目数据范围数据保单相关信息", description = "科目数据范围数据保单相关信息")
public class SubjectDataPolicyComm extends SubjectDataBase implements Serializable {

    private static final long serialVersionUID = 1;

    private Integer id;

    /**
     * 初始事件编码，做冲正时直接从源记录上获取
     */
    @ApiModelProperty(name = "事件编码", example = "settlement.global.policy.hesitate_surrender")
    private String initialEventCode;

    /**
     * 员工ID
     */
    @ApiModelProperty(name = "员工ID")
    private Integer employeeId;

    /**
     * 员工编号
     */
    @ApiModelProperty(value = "员工编号", example = "ZHNX202020")
    private String employeeCode;

    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工编号", example = "张三")
    private String employeeName;


    /**
     * 岗位ID
     */
    @ApiModelProperty(name = "岗位ID")
    private String jobId;


    /**
     * 岗位名称
     */
    @ApiModelProperty(name = "岗位")
    private String jobName;

    /**
     * 责任督导工号
     */
    @ApiModelProperty(name = "责任督导工号")
    private String supervisorCode;

    /**
     * 组织性质
     */
    @ApiModelProperty(name = "组织性质", example = "1")
    private Integer orgNature;

    /**
     * 职务类型：0—主职/1—兼职
     */
    @ApiModelProperty(name = "职务类型：0—主职/1—兼职", example = "1")
    private Integer serviceType;

    /**
     * 任职状态：0-任职中/1-任职结束/2-草稿/3待生效/4作废/5审批中
     */
    @ApiModelProperty(name = "任职状态：0-任职中/1-任职结束/2-草稿/3待生效/4作废/5审批中")
    private Integer postingStatus;

    /**
     * pco等级
     */
    private String pcoLevel;
    /**
     * 主任等级
     */
    private String directorLevel;

    /**
     * 继续率月份[yyyy-MM]
     */
    @ApiModelProperty(value = "继续率月份", example = "2023-05")
    private String bizMonth;

    /**
     * 继续率月份对应-续期率
     */
    @ApiModelProperty(value = "继续率月份对应续期率", example = "0.8")
    private BigDecimal monthRenewalRate;

    /**
     * 继续率
     */
    private BigDecimal renewalRate;
    /**
     * 短险推广费
     */
    private BigDecimal shortPromotion;
    /**
     * 长险推广费
     */
    private BigDecimal longPromotion;
    /**
     * 短险保费
     */
    private BigDecimal shortProductPremium;
    /**
     * 长险保费
     */
    private BigDecimal longProductPremium;
    /**
     * 短期险折标保费
     */
    private BigDecimal shortAssessConvertInsurancePremium;
    /**
     * 长期险折标保费
     */
    private BigDecimal longAssessConvertInsurancePremium;
    /**
     * 注册客户数
     */
    private Integer registerUserCount;

    /**
     * 分支编号
     */
    @ApiModelProperty(value = "分支编号", example = "ZHNX202020")
    private String orgCode;

    /**
     * 分支名称
     */
    @ApiModelProperty(value = "分支名称", example = "ZHNX202020")
    private String orgName;

    /**
     * 单据编号
     */
    @ApiModelProperty(value = "单据编号", example = "12345678")
    private String documentCode;

    /**
     * 保单合同号
     */
    @ApiModelProperty(value = "保单合同号", example = "C12345678")
    private String contractCode;

    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号", example = "PC12345678")
    private String policyNo;

    /**
     * 险种保费
     */
    @ApiModelProperty(value = "保单号", example = "12.12")
    private BigDecimal premium;

    /**
     * 源单据编号
     */
    @ApiModelProperty(value = "源单据编号", example = "PC12345678")
    private String sourceDocumentCode;

    /**
     * 业务保费（承保时与product_premium一直，退保时与surrender_amount一直，冲正时在原值上取反）
     */
    @ApiModelProperty(value = "业务保费", example = "123")
    private BigDecimal businessPremium;

    /**
     * 车险评分等级
     */
    @ApiModelProperty(value = "车险评分等级", example = "S")
    private String vehicleBusinessScore;

    /**
     * 农机险标识
     */
    @ApiModelProperty(value = "农机险标识", example = "1")
    private Integer agriculturalMachineryFlag;

    /**
     * 业务记账时间 新契约/续保，退保_退保时间
     */
    @ApiModelProperty(value = "业务记账时间")
    private Date businessAccountTime;

    /**
     * 推广费
     */
    @ApiModelProperty(value = "推广费", example = "12.12")
    private BigDecimal grantAmount;

    /**
     * 长短险标记 0短险1长险
     */
    @ApiModelProperty(value = "长短险标记", example = "1")
    private Integer longShortFlag;

    /**
     * 结算机构编码
     */
    @ApiModelProperty(value = "结算机构编码", example = "XJXH")
    private String settlementInstitution;

    /**
     * 结算机构名称
     */
    @ApiModelProperty(value = "结算机构名称", example = "小鲸向海")
    private String settlementInstitutionName;
    /**
     * 主险编码
     */
    @ApiModelProperty(value = "主险编码", example = "小鲸向海")
    private String mainProductCode;

    /**
     * 续期期数
     */
    private Integer renewalPeriod;

    /**
     * 冲正数据标志，0未冲正 ,1 已冲正（包括冲正数据和被冲正数据）
     */
    private Integer correctionFlag;

    private String periodType;

    private Date orderTime;
}