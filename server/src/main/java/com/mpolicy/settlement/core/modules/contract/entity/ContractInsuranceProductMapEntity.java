package com.mpolicy.settlement.core.modules.contract.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("ep_contract_insurance_product_map")
public class ContractInsuranceProductMapEntity implements Serializable {
    private static final long serialVersionUID = -4990911865227311126L;

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 保司编码
     */
    private String contractCode;
    /**
     * 协议保司产品编码
     */
    private String insuranceProductCode;
    /**
     * 协议保司产品名称
     */
    private String insuranceProductName;

}
