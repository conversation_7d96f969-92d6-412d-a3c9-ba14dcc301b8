package com.mpolicy.settlement.core.modules.add.dto;

import com.mpolicy.settlement.core.modules.add.entity.SettlementAddCostInfoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class SyncAddCommissionDetail implements Serializable {
    /**
     * 交单时间
     */
    @ApiModelProperty(value = "交单时间")
    private Date orderTime;
    /**
     * 承保时间
     */
    @ApiModelProperty(value = "承保时间")
    private Date approvedTime;
    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号")
    private String policyNo;
    /**
     * 保全创建时间
     */
    @ApiModelProperty(value = "保全创建时间")
    private Date applyTime;
    /**
     * 实收时间
     */
    @ApiModelProperty(value = "实收时间")
    private Date paymentTime;
    /**
     * 批单号
     */
    @ApiModelProperty(value = "批单号")
    private String endorsementNo;
    /**
     * 记账时间
     */
    @ApiModelProperty(value = "记账时间：新契约取交单时间，保全取保全创建时间，续期取实收时间")
    private Date accountTime;
    /**
     * 所属区域名称
     */
    @ApiModelProperty(value = "所属区域名称")
    private String regionName;
    /**
     * 所属区域编码
     */
    @ApiModelProperty(value = "所属区域编码")
    private String regionCode;
    /**
     * 所属机构编码
     */
    @ApiModelProperty(value = "所属机构编码")
    private String orgCode;
    /**
     * 所属机构名称
     */
    @ApiModelProperty(value = "所属机构名称")
    private String orgName;
    /**
     * 推荐人工号
     */
    @ApiModelProperty(value = "推荐人工号")
    private String recommendId;
    /**
     * 推荐人姓名
     */
    @ApiModelProperty(value = "推荐人姓名")
    private String recommendName;
    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private Integer sellProductId;
    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String sellProductCode;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String sellProductName;
    /**
     * 险种id
     */
    @ApiModelProperty(value = "险种id")
    private Integer riskId;
    /**
     * 险种编码
     */
    @ApiModelProperty(value = "险种编码")
    private String riskCode;
    /**
     * 险种名称
     */
    @ApiModelProperty(value = "险种名称")
    private String riskName;
    /**
     * 长短险标记
     */
    @ApiModelProperty(value="长短险标记 0短险1长险")
    private Integer longShortFlag;
    /**
     * 投保人姓名
     */
    @ApiModelProperty(value="投保人姓名")
    private String applicantName;
    /**
     * 投保人证件号
     */
    @ApiModelProperty(value="投保人证件号")
    private String applicantIdCard;
    /**
     * 被保人姓名
     */
    @ApiModelProperty(value="被保人姓名")
    private String insuredName;
    /**
     * 被保人证件号
     */
    @ApiModelProperty(value="被保人证件号")
    private String insuredIdCard;
    /**
     * 缴费期数
     */
    @ApiModelProperty(value="缴费期数")
    private Integer termNum;
    /**
     * 险种保费
     */
    @ApiModelProperty(value="险种保费")
    private BigDecimal premium;
    /**
     * 退保金额
     */
    @ApiModelProperty(value="退保金额")
    private BigDecimal surrenderPremium;
    /**
     * 险种状态
     */
    @ApiModelProperty(value="险种状态")
    private String productStatus;
    /**
     * 是否整村推进 0否1是
     */
    @ApiModelProperty(value="是否整村推进 0否1是")
    private Integer isActivityOrder;
    /**
     * 是否分销单 0否1是
     */
    @ApiModelProperty(value="是否分销单 0否1是")
    private Integer isDistOrder;

    @ApiModelProperty("保全类型")
    private String preservationType;
    @ApiModelProperty("保全项目")
    private String preservationProject;

    @ApiModelProperty(value = "结算批次")
    private String batchNo;

    /**
     * 加佣比例
     */
    @ApiModelProperty(value = "加佣比例")
    private BigDecimal addCommissionProportion;

    /**
     * 加佣金额
     */
    @ApiModelProperty(value = "加佣金额")
    private BigDecimal addCommissionAmount;

    /**
     * 被保人编码
     */
    @ApiModelProperty(value = "被保人编码")
    private String insuredCode;
    /**
     * 事件类型编码
     */
    @ApiModelProperty(value = "事件类型编码")
    private String eventTypeCode;

    /**
     * 结算状态：0核算中，1待结算，2已结算，3作废
     */
    @ApiModelProperty(value = "结算状态：0核算中，1待结算，2已结算，3作废")
    private Integer settlementState;

    /**
     * 活动编码
     */
    @ApiModelProperty(value = "活动编码")
    private Integer activityId;
    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "唯一标识，唯一索引字段值拼接而成 拼接字符|")
    private String uuid;

    @ApiModelProperty(value = "加佣生成时间")
    private Date addCommissionTime;


}
