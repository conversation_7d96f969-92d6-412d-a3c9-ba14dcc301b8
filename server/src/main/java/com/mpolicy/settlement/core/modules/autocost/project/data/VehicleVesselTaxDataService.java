package com.mpolicy.settlement.core.modules.autocost.project.data;

import com.alibaba.fastjson.JSON;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.base.SettlementCostQuery;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataPolicyComm;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataRequest;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataRuleInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.employee.EmployeeInfo;
import com.mpolicy.settlement.core.modules.autocost.helper.SubjectDataHelper;
import com.mpolicy.settlement.core.modules.autocost.project.data.common.PolicyCommDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 【科目12】车险车船税推广费数据生成服务
 *
 * <AUTHOR>
 * @since 2023-11-05 17:58
 */
@Service
@Slf4j
public class VehicleVesselTaxDataService extends PolicyCommDataService<SubjectDataRequest<String>, List<SubjectDataPolicyComm>> {

    @Override
    public CostSubjectEnum getSubjectDataEnum() {
        return CostSubjectEnum.VEHICLE_VESSEL_TAX;
    }

    /**
     * 车险车船税推广费生成数据明细
     * 1、解析规则
     * 1-1 【固定规则】此科目为对象暂时写死为：中和农信
     * 1-2 【动态规则】 读取规则配置表获取配置信息
     * 2、获取源数据
     * 3、解析构建科目目标数据
     * 4、写入科目范围数据
     */
    @Override
    public int builderCostSubjectData(String costSettlementCycle, CostSubjectInfo subjectInfo, String batchCode, List<SubjectDataRuleInfo> subjectDataRuleList) {
        log.info("【车险车船税推广费】车险车船税推广费科目数据计算 构建开始......");
        // 1 解析规则
        SettlementCostQuery query = new SettlementCostQuery();
        // 1-1 【动态规则】 读取规则配置表获取配置信息
        SubjectDataHelper.builderDataRuleInfo(costSettlementCycle, subjectDataRuleList, query);
        log.info("【车险车船税推广费】开始获取源数据,进行构建，获取条件={}", JSON.toJSONString(query));
        // 1-2 【固定规则】此科目为对象中和农信所有成员
        List<EmployeeInfo> employeeList = employeeService.queryEmployeeAllSnapshot(costSettlementCycle);
        if (employeeList.isEmpty()) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("科目员工信息缺失，无法进行科目数据计算"));
        }
        // 2 获取源数据
        int saveSize = 0;
        if (employeeList.size() > SUBJECT_HANDLE_DATA_SIZE) {
            List<List<EmployeeInfo>> partition = ListUtils.partition(employeeList, SUBJECT_HANDLE_DATA_SIZE);
            for (List<EmployeeInfo> partitionEmployeeList : partition) {
                // 2-1 获取源数据 + 进行构建科目数据
                List<SubjectDataPolicyComm> data = super.builderSettlementCostInfo(costSettlementCycle, batchCode, partitionEmployeeList, query);
                if (!data.isEmpty()) {
                    // 2-2 写入科目范围数据
                    saveSubjectData(data);
                    saveSize += data.size();
                }
            }
        } else {
            // 2-1 获取源数据 + 进行构建科目数据
            List<SubjectDataPolicyComm> data = super.builderSettlementCostInfo(costSettlementCycle, batchCode, employeeList, query);
            if (!data.isEmpty()) {
                // 2-2 写入科目范围数据
                saveSubjectData(data);
                saveSize += data.size();
            }
        }
        log.info("【车险车船税推广费】科目数据计算 构建完成, 构建数据总数={}", saveSize);
        return saveSize;
    }

    @Override
    public int resetSubjectData(String costSettlementCycle) {
        int result = clearSubjectData(costSettlementCycle, getSubjectDataEnum().getCode());
        log.info("【车险车船税推广费】数据生成服务 重置完成了......");
        return result;
    }

    @Override
    public List<SubjectDataPolicyComm> subjectData(SubjectDataRequest<String> request) {
        List<SubjectDataPolicyComm> result = querySubjectData(request.getCostSettlementCycle(), getSubjectDataEnum().getCode());
        log.info("【车险车船税推广费】科目数据获取完成, 数据数量={}", result.size());
        return result;
    }
}