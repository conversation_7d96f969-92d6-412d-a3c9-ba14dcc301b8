package com.mpolicy.settlement.core.modules.invoice.enums;


import lombok.Getter;

@Getter
public enum ReconcileInvoiceApplyStatusEnum {

    // 申请状态0:修改申请信息 1、待财务审核2、开票处理中 3、已开票 4、开票失败 5、驳回 6、系统回退
    WAIT_FINANCE_AUDIT(1, "待财务审核"),
    INVOICE_PROCESSING(2, "开票处理中"),
    INVOICE_COMPLETED(3, "已开票"),
    INVOICE_FAILED(4, "开票失败"),
    REJECT(5, "驳回"),
    SYSTEM_ROLLBACK(6, "开票失败"),
    NULL(-1, "未知"),
    ;

    private Integer code;

    private String desc;

    ReconcileInvoiceApplyStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 匹配操作码
     *
     * @param code
     * @return
     */
    public static ReconcileInvoiceApplyStatusEnum matchSearchCode(Integer code) {
        for (ReconcileInvoiceApplyStatusEnum searchEnum : ReconcileInvoiceApplyStatusEnum.values()) {
            if (searchEnum.code.equals(code)) {
                return searchEnum;
            }
        }
        return ReconcileInvoiceApplyStatusEnum.NULL;
    }
}
