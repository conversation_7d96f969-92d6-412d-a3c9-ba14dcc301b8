package com.mpolicy.settlement.core.common.keys;

import com.mpolicy.common.redis.key.BasePrefix;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/16 11:13
 */
public class AdminCommonKeys extends BasePrefix {

    private AdminCommonKeys(String prefix) {
        super(prefix);
    }

    private AdminCommonKeys(int expireSeconds, String prefix) {
        super(expireSeconds, prefix);
    }

    /**
     * 渠道应用缓存key
     */
    public static AdminCommonKeys CHANNEL_APPLICATION = new AdminCommonKeys("CHANNEL_APPLICATION");

    /**
     * 渠道缓存key
     */
    public static AdminCommonKeys CHANNEL = new AdminCommonKeys("CHANNEL");

    /**
     * 账号登录错误次数(半小时3次)
     */
    public static AdminCommonKeys LOGIN_FAIL_NUM = new AdminCommonKeys(60 * 30, "LOGIN_FAIL_NUM");
    /**
     * 修改密码错误次数
     */
    public static AdminCommonKeys UPDATE_PASSWORD = new AdminCommonKeys(60 * 30, "UPDATE_PASSWORD");
    /**
     * 登录唯一标识(1小时)
     */
    public static AdminCommonKeys LOGIN_CODE = new AdminCommonKeys(60 * 60, "LOGIN_CODE");
    /**
     * 数据处理状态24小时
     */
    public static AdminCommonKeys HANDLE_STATUS = new AdminCommonKeys(60 * 60 * 24, "HANDLE_STATUS");
    /**
     * 超时未操作时间(5小时分钟)
     */
    public static AdminCommonKeys NOT_OPERATED_TIME = new AdminCommonKeys(60 * 60 * 5, "NOT_OPERATED_TIME");
}
