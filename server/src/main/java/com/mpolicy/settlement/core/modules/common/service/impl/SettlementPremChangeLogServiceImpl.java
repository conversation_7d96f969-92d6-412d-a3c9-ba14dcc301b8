package com.mpolicy.settlement.core.modules.common.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.enums.EventSendStatusEnum;
import com.mpolicy.settlement.core.modules.common.dao.SettlementPremChangeLogDao;
import com.mpolicy.settlement.core.modules.common.entity.SettlementPremChangeLogEntity;
import com.mpolicy.settlement.core.modules.common.service.SettlementPremChangeLogService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @since 2024/08/05
 */
@Service("settlementPremChangeLogService")
public class SettlementPremChangeLogServiceImpl extends ServiceImpl<SettlementPremChangeLogDao, SettlementPremChangeLogEntity> implements SettlementPremChangeLogService {


    @Override
    public void saveSettlementPremChangeLog(String pushEventCode, Integer changeType, String businessCode, String dataJson) {
        SettlementPremChangeLogEntity settlementPremChangeLog = new SettlementPremChangeLogEntity();
        settlementPremChangeLog.setPushEventCode(pushEventCode);
        settlementPremChangeLog.setChangeType(changeType);
        settlementPremChangeLog.setBusinessCode(businessCode);
        settlementPremChangeLog.setDataJson(dataJson);
        settlementPremChangeLog.setSendStatus(EventSendStatusEnum.INVALID.getCode());
        save(settlementPremChangeLog);
    }

    /**
     * 保存费率变更记录
     *
     * @param pushEventCode 推送唯一标识
     * @param changeType    类型
     * @param businessCode  事件编码
     * @param dataJson      数据体
     */
    @Override
    public void saveNeedSendSettlementPremChangeLog(String pushEventCode, Integer changeType, String businessCode, String dataJson,Integer sendStatus) {
        SettlementPremChangeLogEntity settlementPremChangeLog = new SettlementPremChangeLogEntity();
        settlementPremChangeLog.setPushEventCode(pushEventCode);
        settlementPremChangeLog.setChangeType(changeType);
        settlementPremChangeLog.setBusinessCode(businessCode);
        settlementPremChangeLog.setDataJson(dataJson);
        settlementPremChangeLog.setSendStatus(sendStatus);
        if(Objects.equals(sendStatus,1)){
            settlementPremChangeLog.setSendTime(new Date());
            settlementPremChangeLog.setSendCount(0);
        }
        save(settlementPremChangeLog);
    }

    public boolean updateSendStatus(String pushEventCode,Integer fromStatus,Integer toStatus){
        return this.lambdaUpdate().set(SettlementPremChangeLogEntity::getSendStatus,toStatus)
                .eq(SettlementPremChangeLogEntity::getPushEventCode,pushEventCode)
                .eq(SettlementPremChangeLogEntity::getSendStatus,fromStatus)
                .update();
    }
}
