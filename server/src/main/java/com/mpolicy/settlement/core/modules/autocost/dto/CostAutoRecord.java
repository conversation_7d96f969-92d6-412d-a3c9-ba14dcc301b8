package com.mpolicy.settlement.core.modules.autocost.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 农保自动结算科目佣金记录表
 * @date 2023/11/5 5:14 下午
 * @Version 1.0
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "农保自动结算科目佣金记录表", description = "农保自动结算科目佣金记录表")
public class CostAutoRecord<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;
    /**
     * 自动结算支出编码
     */
    private String autoCostCode;
    /**
     * 计算周期
     */
    private String costSettlementCycle;
    /**
     * 计算时间
     */
    private Date costSettlementTime;
    /**
     * 方案编码
     */
    private String programmeCode;
    /**
     * 方案名称
     */
    private String programmeName;
    /**
     * 科目编码
     */
    private String subjectCode;
    /**
     * 科目名称
     */
    private String subjectName;
    /**
     * 发放对象类型
     */
    private String sendObjectType;
    /**
     * 发放对象编码
     */
    private String sendObjectCode;
    /**
     * 发放对象名称
     */
    private String sendObjectName;
    /**
     * 发放对象机构编码
     */
    private String objectOrgCode;
    /**
     * 发放对象机构名称
     */
    private String objectOrgName;
    /**
     * 单据编号
     */
    private String documentCode;
    /**
     * 结算机构编码
     */
    private String settlementInstitution;
    /**
     * 结算机构名称
     */
    private String settlementInstitutionName;
    /**
     * 记账日期
     */
    private Date settlementDate;
    /**
     * 明细类型;policy 保单维度，product 险种维度，policy_product保单险种维度
     */
    private String costDataType;
    /**
     * 保费
     */
    private BigDecimal premium;
    /**
     * 费用类型：推广费，津贴、激励，整村推荐推广费，代发区域营销费
     */
    private String amountType;
    /**
     * 费用
     */
    private BigDecimal amount;
    /**
     * 提成比例
     */
    private BigDecimal commissionRate;
    /**
     * 自动结算佣金
     */
    private BigDecimal commissionAmount;
    /**
     * 发放比例 0-100
     */
    private BigDecimal grantRate;
    /**
     * 发放金额
     */
    private BigDecimal grantAmount;
    /**
     * 继续率
     */
    private BigDecimal renewalRate;
    /**
     * 继续率月份[yyyy-MM]
     */
    private String bizMonth;
    /**
     * 增长系数
     */
    private BigDecimal growthFactor;
    /**
     * pco等级
     */
    private String pcoLevel;

    /**
     * 人均单量
     */
    private BigDecimal perCapitaCount;

    /**
     * 注册用户数
     */
    private Integer registeredUsers;
    /**
     * 人均注册用户数
     */
    private BigDecimal avgRegisterUserCount;
    /**
     * 业务数据类型
     */
    private String businessDataType;
    /**
     * 源单据编号
     */
    private String sourceDocumentCode;

    /**
     * 源自动结算支出编码
     */
    private String sourceAutoCostCode;

    /**
     * 源周期
     */
    private String sourceCostSettlementCycle;
    /**
     * 新增明细list
     */
    private List<T> addItemList;
    /**
     * 更新明细list (被保人险种维度才会存在即settlement_cost_info)
     */
    private List<T> updateItemList;

    /**
     * 计算补发记录时适用，用于记录暂发金额
     */
    private BigDecimal oldGrantRate;
    /**
     * 计算补发记录时适用，用于记录暂发金额
     */
    private BigDecimal oldGrantAmount;

    /**
     * 发放科目编码
     */
    private String sendSubjectCode;
    /**
     * 发放科目名称
     */
    private String sendSubjectName;
    /**
     * 是否动态科目
     */
    private Integer dynamicFlag;
    /**
     * 是否有明细数据
     */
    private Boolean hasItemFlag;

    /**
     * 是否有生成暂发记录 0无，1有
     */
    private Integer hasTemporary;

    /**
     * 确认状态;0未对账1对账中2已完成对账
     */
    private Integer confirmStatus;
    /**
     * 确认操作员
     */
    private String confirmUser;
    /**
     * 确认完成时间
     */
    private Date confirmTime;
    /**
     * 是否直接发放，默认为1
     * 老科目直接发放 1，
     * 25版督导，生服对接人、主任绩效不直接发放 0
     */
    private Integer directGrantFlag = 1;

    /**
     * 生服业务相关知识（是否合格）
     */
    private String businessKnowledgeQualifyFlag;
    /**
     * pco 上月审核通过培训次数
     */
    private Integer smPcoTraining;
    /**
     * 酒水/家清破损理赔提交及时性（是否合格）
     */
    private String claimTimelinessQualifyFlag;
    /**
     * 上月理赔多次驳回率
     */
    private BigDecimal smRejectManyRate;
    /**
     * 生服库存盘点率
     */
    private BigDecimal smCheckSignTaskRadio;
    /**
     * 酒水个人仓建仓率
     */
    private BigDecimal personalWarehouseCreateRadio;
    /**
     * 当月酒水个人仓使用率
     */
    private BigDecimal smPersonalWarehouseUseRadio;
    /**
     * 生服业务相关知识
     */
    private BigDecimal businessKnowledgeQualifyAmt;
    /**
     * 生服业务培训组织
     */
    private BigDecimal smPcoTrainingAmt;
    /**
     * 售后理赔及投诉
     */
    private BigDecimal claimAndComplaintAmt;
    /**
     * 实物库存盘点
     */
    private BigDecimal stocktakingAmt;

    /**
     * 当月酒水营收
     */
    private BigDecimal smWineRevenueAmt;

    /**
     * 统计月
     */
    private String statisticsMonth;

    /**
     * 入账标志
     */
    private Integer accountFlag;

}

