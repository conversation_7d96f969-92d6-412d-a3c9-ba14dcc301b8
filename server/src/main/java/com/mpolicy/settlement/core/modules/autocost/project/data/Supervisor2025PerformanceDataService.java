package com.mpolicy.settlement.core.modules.autocost.project.data;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.service.common.woodpecker.dto.MonitorWoodpeckerData;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.helper.SettlementBaseHelper;
import com.mpolicy.settlement.core.modules.autocost.dto.CostSubjectInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.base.SettlementCostQuery;
import com.mpolicy.settlement.core.modules.autocost.dto.base.SettlementInstitutionInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataPolicyGroup;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataRequest;
import com.mpolicy.settlement.core.modules.autocost.dto.data.SubjectDataRuleInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.employee.EmployeeInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.QbiRenewalRate;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.SupervisorQbiInfo;
import com.mpolicy.settlement.core.modules.autocost.dto.qbi.SupervisorQbiRenewalRate;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostSubjectDataPolicyDetailEntity;
import com.mpolicy.settlement.core.modules.autocost.enums.EmployeeRoleEnum;
import com.mpolicy.settlement.core.modules.autocost.helper.SettlementInstitutionHelper;
import com.mpolicy.settlement.core.modules.autocost.helper.SubjectDataHelper;
import com.mpolicy.settlement.core.modules.autocost.project.data.common.PolicyGroupDataService;
import com.mpolicy.settlement.core.modules.autocost.project.data.rule.PolicyCommDataRuleEnum;
import com.mpolicy.settlement.core.modules.autocost.utils.AutoCostUtils;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 【科目4】督导2025绩效暂发数据生成服务
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Service
@Slf4j
public class Supervisor2025PerformanceDataService extends PolicyGroupDataService<SubjectDataRequest<String>, List<SubjectDataPolicyGroup>> {

    @Override
    public CostSubjectEnum getSubjectDataEnum() {
        return CostSubjectEnum.SUPERVISOR_2025_PERFORMANCE;
    }


    private Map<String, QbiRenewalRate> builderRenewalRateMap(List<SupervisorQbiRenewalRate> renewalRates) {
        Map<String, QbiRenewalRate> result = renewalRates.stream().map(item -> {
            QbiRenewalRate renewalRate = new QbiRenewalRate();
            renewalRate.setBizMonth(item.getBizMonth());
            renewalRate.setObjectCode(item.getEmployeeCode());
            renewalRate.setObjectName(item.getEmployeeName());
            //renewalRate.setRenewalRate(item.getRenewalRate());
            if(item.getPayAmt() == null || BigDecimal.ZERO.compareTo(item.getPayAmt()) == 0){
                renewalRate.setRenewalRate(null);
            }else {
                renewalRate.setRenewalRate(item.getRenewalRate());
            }
            return renewalRate;
        }).collect(Collectors.toMap(QbiRenewalRate::getObjectCode, Function.identity(), (k1, k2) -> k1));
        return result;
    }

    /**
     * 督导2025绩效生成数据明细
     * 1、解析规则
     * 2、读取员工服务接口岗位为【督导】的成员集合
     * 3、遍历督导获取督导下面的管辖员工集合
     * 4、置员工集合为【基础佣金】的查询条件
     * 5、根据【基础佣金】返回的数据，构建科目数据： 基础佣金维度结构（基础佣金 + 员工 + 员工qbi）
     * 注意事项：
     * 1、返回给到结佣服务，怎么取督导的指标？
     * 2、督导是否属于督导本身的员工关系？
     */
    @Override
    public int builderCostSubjectData(String costSettlementCycle, CostSubjectInfo subjectInfo, String batchCode, List<SubjectDataRuleInfo> subjectDataRuleList) {
        log.info("【督导2025绩效】科目数据计算 构建开始......");
        // 1 初始化【基础佣金】查询条件
        SettlementCostQuery query = new SettlementCostQuery();
        // 1-1 【动态规则】 读取规则配置表获取配置信息
        SubjectDataHelper.builderDataRuleInfo(costSettlementCycle, subjectDataRuleList, query);
        // 1-2 【动态规则】 非数据库查询条件规则解析
        SubjectDataHelper.builderNotDatabaseQueryCntRule(costSettlementCycle,subjectDataRuleList,query);
        // 2 获取所有督导(主职)员工信息，
        List<EmployeeInfo> supervisorEmployeeList = employeeService.listEmployeeByEmployeeRoleSnapshot(EmployeeRoleEnum.SUPERVISOR, true, costSettlementCycle);

        if (supervisorEmployeeList.isEmpty()) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("科目督导(主职)员工信息缺失，无法进行科目数据计算"));
        }
        // 2-1 督导绩效暂发 继续率读取信息  25版督导绩效与续期率没有关系 2025-01-13 25版屏蔽 begin
//        int renewalRateMonth = SubjectDataHelper.getReissueCostSettlementCycleMonth(subjectInfo, subjectDataRuleList, PolicyCommDataRuleEnum.SUPERVISOR_PERFORMANCE_RENEWAL_RATE_MONTH);
//        log.info("【督导2025绩效】科目继续率数据需要前置月份为={}", renewalRateMonth);
//        String renewalRateCycle = AutoCostUtils.getReissueCostSettlementCycle(costSettlementCycle, renewalRateMonth);
//        // 2-2 获取督导继续率信息
//        List<SupervisorQbiRenewalRate> renewalRates = settlementCostQbiService.listSupervisorQbiRenewalRate(supervisorEmployeeList.stream().map(EmployeeInfo::getEmployeeCode).collect(Collectors.toList()), renewalRateCycle, costSettlementCycle);
//        final Map<String, QbiRenewalRate> renewalRateMap = builderRenewalRateMap(renewalRates);
        // 屏蔽 end;

        // 声明督导绩发生成数据
        List<SubjectDataPolicyGroup> data = new ArrayList<>();
        // 获取结算机构集合
        List<SettlementInstitutionInfo> settlementInstitutionList = SettlementInstitutionHelper.listSettlementInstitution();
        // 3 处理督导
        for (EmployeeInfo supervisor : supervisorEmployeeList) {
            // 3-1 获取督导管辖员工集合
            /* modify by zhangjian 2025-03-11 督导绩效需要关联机构编码和客户经理
            List<EmployeeInfo> employeeInfos = employeeService.listEmployeeBySupervisorSnapshot(supervisor.getEmployeeCode(), costSettlementCycle);
            */
            List<EmployeeInfo> employeeInfos = employeeService.listEmployeeByOrgCodeAndSuperCodeSnapshot(supervisor.getOrgCode(),supervisor.getEmployeeCode(), costSettlementCycle);
            //add by zhangjian 督导绩效需要包括自己。2025-02-24
            if(CollectionUtils.isEmpty(employeeInfos)){
                employeeInfos = Lists.newArrayList();
            }
            log.info("督导{}绩效统计添加督导的推广费",supervisor.getEmployeeCode());
            employeeInfos.add(supervisor);
            String groupCode = PolicySettlementUtils.createCodeLastNumber("GC");



            if (employeeInfos.isEmpty()) {
                log.warn("【督导2025绩效】科目数据计算构建, 当前督导[{}]没有管辖员工", supervisor.getEmployeeCode());
                data.add(builderNotCalcDataGroup(costSettlementCycle,subjectInfo.getSubjectCode(),batchCode, groupCode, supervisor, "当前督导没有管辖员工"));
                continue;
            }

            //【督导】指标数据 2025-03-11指标数据没有关联机构编码，如果后续要用指标需要注意
//            SupervisorQbiInfo supervisorQbiInfo;
//            try {
//                supervisorQbiInfo = settlementCostQbiService.querySupervisorQbiInfo(supervisor.getEmployeeCode(), costSettlementCycle);
//            } catch (GlobalException e) {
//                log.warn("【督导2025绩效】获取督导指标信息异常，督导[{}]，错误信息={}", supervisor.getEmployeeCode(), e.getMessage());
//                // 如果存在没有指标 添加业务健康数据表
//                SettlementBaseHelper.addMonitorWoodpeckerDataAsync(MonitorWoodpeckerData.builder()
//                        .businessType("结算科目数据构建")
//                        .businessName(getSubjectDataEnum().getName())
//                        .eventData(e.getMessage())
//                        .businessDesc(StrUtil.format("{} 督导指标信息不存在", supervisor.getEmployeeCode()))
//                        .build());
//                continue;
//            }
            // 3-2 构建管辖员工明细
            //add by zhangjian 2025-03-11 督导绩效数据范围增加机构
            query.setOrgCode(supervisor.getOrgCode());
            List<SettlementCostSubjectDataPolicyDetailEntity> policyGroupDetail = builderSettlementPolicyGroupDetail(costSettlementCycle, subjectInfo, batchCode, groupCode, employeeInfos, query);
            if (policyGroupDetail.isEmpty()) {
                log.warn("【督导2025绩效】科目数据计算构建, 当前督导[{}]的管辖员工没有基础佣金明细", supervisor.getEmployeeCode());
                SubjectDataPolicyGroup group = builderNotCalcDataGroup(costSettlementCycle,subjectInfo.getSubjectCode(),batchCode, groupCode, supervisor, "当前督导下所管辖员工没有基础佣金明细");
//                group.setPcoLevel(supervisorQbiInfo.getPcoLevel());
//                group.setRegisterUserCount(supervisorQbiInfo.getRegisterUserCount());
//                group.setRenewalRate(supervisorQbiInfo.getRenewalRate());
                data.add(group);
                continue;
            }
            List<SettlementCostSubjectDataPolicyDetailEntity> canCalcPolicyGroupDetail = policyGroupDetail.stream().filter(e -> e.getCalcFlag() == 1).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(canCalcPolicyGroupDetail)) {
                log.warn("【督导2025绩效】科目数据计算构建, 当前督导[{}]的管辖员工没有可用于计算的基础佣金明细", supervisor.getEmployeeCode());
                SubjectDataPolicyGroup group = builderNotCalcDataGroup(costSettlementCycle,subjectInfo.getSubjectCode(),batchCode, groupCode, supervisor, "当前督导下所管辖员工没有可用于计算的基础佣金明细");
//                group.setPcoLevel(supervisorQbiInfo.getPcoLevel());
//                group.setRegisterUserCount(supervisorQbiInfo.getRegisterUserCount());
//                group.setRenewalRate(supervisorQbiInfo.getRenewalRate());
                data.add(group);
            } else {
                log.info("【督导2025绩效】员工信息={} ，明细记录信息数量={},可参与计算的记录数={}", JSON.toJSONString(supervisor), policyGroupDetail.size(), canCalcPolicyGroupDetail.size());

                // 需要对明细进行分组
                Map<String, List<SettlementCostSubjectDataPolicyDetailEntity>> settlementInstitutionMap = canCalcPolicyGroupDetail.stream()
                        .collect(Collectors.groupingBy(SettlementCostSubjectDataPolicyDetailEntity::getSettlementInstitution));
                for (Map.Entry<String, List<SettlementCostSubjectDataPolicyDetailEntity>> entry : settlementInstitutionMap.entrySet()) {
                    // 3-3 根据明细获取构建分组数据
                    SubjectDataPolicyGroup subjectDataPolicyGroup = new SubjectDataPolicyGroup();
                    subjectDataPolicyGroup.setBatchCode(batchCode);

                    // 3-4 【督导】汇总信息
                    builderSettlementPolicyGroup(subjectDataPolicyGroup, entry.getValue());
                    // 3-5 【督导】基本信息
                    BeanUtils.copyProperties(supervisor, subjectDataPolicyGroup);

                    subjectDataPolicyGroup.setGroupCode(groupCode);
                    // 3-6 【督导】指标信息
//                    subjectDataPolicyGroup.setPcoLevel(supervisorQbiInfo.getPcoLevel());
//                    subjectDataPolicyGroup.setRegisterUserCount(supervisorQbiInfo.getRegisterUserCount());
//                    subjectDataPolicyGroup.setRenewalRate(supervisorQbiInfo.getRenewalRate());
                    // 3-7 添加到科目数据集合
                    subjectDataPolicyGroup.setCostSettlementCycle(costSettlementCycle);
                    subjectDataPolicyGroup.setSubjectCode(subjectInfo.getSubjectCode());
                    subjectDataPolicyGroup.setSettlementInstitution(entry.getKey());
                    SettlementInstitutionInfo settlementInstitutionInfo = SettlementInstitutionHelper.machineSettlementInstitutionByCode(settlementInstitutionList, subjectDataPolicyGroup.getSettlementInstitution());
                    if (settlementInstitutionInfo == null) {
                        throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("结算机构信息匹配未命中，结算机构编码:{}", subjectDataPolicyGroup.getSettlementInstitution())));
                    }
                    subjectDataPolicyGroup.setSettlementInstitutionName(settlementInstitutionInfo.getSettlementInstitutionName());
                    // 3-8 继续率信息，25版督导绩效与续期率没有关系 2025-01-13 25版屏蔽 begin
//                    subjectDataPolicyGroup.setBizMonth(renewalRateCycle);
//                    if (renewalRateMap.containsKey(supervisor.getEmployeeCode())) {
//                        subjectDataPolicyGroup.setMonthRenewalRate(renewalRateMap.get(supervisor.getEmployeeCode()).getRenewalRate());
//                    } else {
//                        log.info(StrUtil.format("科目={} 员工信息={} 结算周期={} 继续率周期={} 缺少继续率信息，将设置默认继续率信息", getSubjectDataEnum().getName(), supervisor.getEmployeeCode(), costSettlementCycle, renewalRateCycle));
//                    }
                    // 屏蔽 end;
                    subjectDataPolicyGroup.setCalcFlag(1);
                    data.add(subjectDataPolicyGroup);
                }
                // 3-8 写入明细记录
                saveSubjectDataDetail(policyGroupDetail);
            }
        }
        // 3 写入科目范围数据
        if (data.isEmpty()) {
            log.warn("【督导2025绩效】科目数据计算构建完成, 科目数据记录为空...");
            return 0;
        }
        int saveSize = saveSubjectData(data);
        log.info("【督导2025绩效】科目数据计算 构建完成, 构建数据总数={}", saveSize);
        return saveSize;
    }

    @Override
    public int resetSubjectData(String costSettlementCycle) {
        int result = clearSubjectData(costSettlementCycle, getSubjectDataEnum().getCode());
        log.info("【督导2025绩效】数据生成服务 重置完成了......");
        return result;
    }

    @Override
    public List<SubjectDataPolicyGroup> subjectData(SubjectDataRequest<String> request) {
        List<SubjectDataPolicyGroup> result = querySubjectData(request.getCostSettlementCycle(), getSubjectDataEnum().getCode());
        log.info("【督导2025绩效】科目数据获取完成, 数据数量={}", result.size());
        return result;
    }
}