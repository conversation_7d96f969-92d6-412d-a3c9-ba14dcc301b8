package com.mpolicy.settlement.core.modules.reconcile.event.project.policyplatform;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mpolicy.common.enums.StatusEnum;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.common.reconcile.enums.ReconcileStatusEnum;
import com.mpolicy.settlement.core.enums.ReconcileTypeEnum;
import com.mpolicy.settlement.core.enums.SettlementPolicyHandlerEnum;
import com.mpolicy.settlement.core.modules.autocost.enums.ConfirmStatusEnum;
import com.mpolicy.settlement.core.modules.govern.dto.proejct.ChangePolicyPremiumData;
import com.mpolicy.settlement.core.modules.govern.dto.proejct.ChangePremiumInsured;
import com.mpolicy.settlement.core.modules.govern.dto.proejct.ChangePremiumInsuredProduct;
import com.mpolicy.settlement.core.modules.reconcile.dto.settlement.CostCorrectionDto;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementEventJobEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.factory.SettlementPolicyFactory;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementPolicyHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.project.common.AbsPolicyCommonEvent;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventCheckResult;
import com.mpolicy.settlement.core.modules.reconcile.event.vo.HandleEventData;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.service.impl.SettlementCostProcessServiceImpl;
import com.mpolicy.settlement.core.modules.reconcile.utils.PolicySettlementUtils;
import com.mpolicy.settlement.core.modules.reconcile.vo.BasisSettlementPolicyInfoDomain;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.common.Constant.SYSTEM_CORRECTION_USER;
import static com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum.*;

/**
 * <AUTHOR>
 * @description 保单保费变更
 * @date 2024/2/26 12:13 上午
 * @Version 1.0
 */
@Service
@Slf4j
public class PolicyPremiumChangeEvent extends AbsPolicyCommonEvent {
    @Override
    public HandleEventCheckResult handleIncomeEventCheck(SettlementEventJobEntity eventJob, Integer reconcileType) {
        ChangePolicyPremiumData changePolicyCodeData = JSONObject.parseObject(eventJob.getEventRequest(),
                ChangePolicyPremiumData.class);
        String policyNo = changePolicyCodeData.getPolicyCode();
        // 2.参数校验
        if (StrUtil.isEmpty(policyNo)) {
            return HandleEventCheckResult.builder().checkStatus(false).checkMsg("事件报文没有获取到保单号").build();
        }
        // 判断事件是否已经生成了明细数据,如果生成了就不在运行了
        boolean checkSettlementPolicyInfo = checkSettlementPolicyInfo(eventJob.getPushEventCode(), reconcileType);
        // 如果存在纪录
        if (checkSettlementPolicyInfo) {
            return HandleEventCheckResult.builder().checkStatus(false)
                .checkMsg(StrUtil.format("保单保费变更事件已经写入明细")).build();
        }
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleIncomeEvent(SettlementEventJobEntity eventJob, HandleEventData handleEventData) {
        ChangePolicyPremiumData changePolicyCodeData = JSONObject.parseObject(eventJob.getEventRequest(),
                ChangePolicyPremiumData.class);
        String policyNo = changePolicyCodeData.getPolicyCode();
        // 判断在这之前有没有生成明细,如果没有生成明细 先执行之前的数据
        List<SettlementPolicyInfoEntity> rectificationList =
            settlementPolicyInfoService.lambdaQuery().eq(SettlementPolicyInfoEntity::getPolicyNo, policyNo)
                .eq(SettlementPolicyInfoEntity::getPolicyNo, policyNo)
                .eq(SettlementPolicyInfoEntity::getReconcileType, handleEventData.getReconcileType())
                .eq(SettlementPolicyInfoEntity::getRectificationMark, StatusEnum.INVALID.getCode()).list();
        if (!rectificationList.isEmpty()) {
            // 判断一下 是否存在已经结算的数据,如果存在,那么提示失败
            boolean anyMatch = rectificationList.stream()
                .anyMatch(a -> ReconcileStatusEnum.RECONCILE_FINISH.getStatusCode().equals(a.getReconcileStatus()));
            if (anyMatch) {
                return "明细存在已经结算的数据,暂不处理";
            }
            // 冲正错误数据
            settlementPolicyInfoService.rectification(rectificationList);
        }

        // 处理保单保费变更事件
        try {
            PolicyProductTypeEnum prodTypeEnum =
                PolicyProductTypeEnum.getProdTypeEnum(handleEventData.getPolicyInfo().getPolicyProductType());
            ReconcileTypeEnum reconcileTypeEnum = ReconcileTypeEnum.matchSearchCode(handleEventData.getReconcileType());
            BasisSettlementPolicyInfoDomain domain = new BasisSettlementPolicyInfoDomain();
            domain.setContractInfo(handleEventData.getPolicyInfo());
            domain.setSettlementEventTypeEnum(
                prodTypeEnum == PolicyProductTypeEnum.GROUP ? SettlementEventTypeEnum.GROUP_NEW_POLICY
                    : SettlementEventTypeEnum.PERSONAL_NEW_POLICY);
            domain.setReconcileTypeEnum(reconcileTypeEnum);
            domain.setEventSourceCode(eventJob.getPushEventCode());
            // 获取处理事件
            SettlementPolicyHandler handler =
                SettlementPolicyFactory.getInvoke(SettlementPolicyHandlerEnum.NEW_INSURANCE);
            // 构建结算明细基础信息
            List<SettlementPolicyInfoEntity> policyInfoList = handler.buildBasisSettlementPolicyInfo(domain);
            if (CollUtil.isNotEmpty(policyInfoList)) {
                //匹配产品信息
                handler.matchInsuranceProduct(reconcileTypeEnum, policyInfoList);
                // 构建手续费和折标保费金额
                List<SettlementPolicyInfoEntity> resultList = handler.buildSettlementSubjectAmount(policyInfoList);
                if (!resultList.isEmpty()) {
                    settlementPolicyInfoService.saveBatch(resultList);
                }
            } else {
                return StrUtil.format("没有生成结算明细，保单号={}, 事件编码={}", eventJob.getEventBusinessCode(),
                    eventJob.getPushEventCode());
            }
        } catch (GlobalException e) {
            throw e;
        } catch (Exception e) {
            String msg = StrUtil.format("生成结算明细异常，保单号={}, 事件编码={}", eventJob.getEventBusinessCode(),
                    eventJob.getPushEventCode());
            log.warn(msg, e);
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(msg));
        }
        return "success";
    }

    @Override
    public HandleEventCheckResult handleCostEventCheck(SettlementEventJobEntity eventJob) {
        return HandleEventCheckResult.builder().checkStatus(true).checkMsg("success").build();
    }

    @Override
    public String handleCostEvent(SettlementEventJobEntity eventJob, HandleEventData eventData) {
        ChangePolicyPremiumData changePolicyCodeData = JSONObject.parseObject(eventJob.getEventRequest(),
                ChangePolicyPremiumData.class);

        String policyNo = changePolicyCodeData.getPolicyCode();
        if (StrUtil.isEmpty(policyNo)) {
            return StrUtil.format("保单号-{}不存在-success", JSONObject.toJSONString(eventData));
        }

        SettlementCostPolicyInfoEntity costPolicy =
                settlementCostPolicyInfoService.getCostPolicyInfoEntityByContractCode(eventJob.getContractCode());

        if (Objects.isNull(costPolicy)) {
            return "结算保单信息缺失-success";
        }

        List<SettlementCostInfoEntity> costInfoEntityList =
                settlementCostInfoService.lambdaQuery().eq(SettlementCostInfoEntity::getCostPolicyId,
                        costPolicy.getId()).eq(SettlementCostInfoEntity::getCorrectionFlag, 0).in(SettlementCostInfoEntity::getInitialEventCode, Lists.newArrayList(PERSONAL_NEW_POLICY.getEventCode(), GROUP_NEW_POLICY.getEventCode())).list();
        if (CollectionUtils.isEmpty(costInfoEntityList)) {
            return StrUtil.format("批改单结算信息-{}不存在-success", JSONObject.toJSONString(eventData));
        }

        //2、参数验证，验证不通过抛出异常
        settlementCostProcessService.validParam(eventJob, null);

        //校验
        List<ChangePremiumInsured> premiumInsuredList = changePolicyCodeData.getInsuredList();
        if (CollectionUtils.isEmpty(premiumInsuredList)) {
            return StrUtil.format("更改被保人-{}不存在-success", JSONObject.toJSONString(eventData));
        }

        Map<String, SettlementCostInfoEntity> insuredSettlementMap =
                costInfoEntityList.stream().collect(Collectors.toMap(x -> StrUtil.format("{}-{}", x.getInsuredCode(),
                        x.getProductCode()), Function.identity()));

        List<SettlementCostInfoEntity> newCostList = new ArrayList<>();
        for (ChangePremiumInsured changePremiumInsured : premiumInsuredList) {
            for (ChangePremiumInsuredProduct changePremiumInsuredProduct : changePremiumInsured.getInsuredProduct()) {
                SettlementCostInfoEntity oldSettlementCostInfo = insuredSettlementMap.get(StrUtil.format("{}-{}",
                        changePremiumInsured.getInsuredCode(), changePremiumInsuredProduct.getProductCode()));

                if (Objects.isNull(oldSettlementCostInfo)) {
                    return StrUtil.format("更改被保人-{}-{}不存在-success", changePremiumInsured.getInsuredCode(),
                            changePremiumInsuredProduct.getProductCode());
                }
                //冲正
                newCostList.add(settlementCostProcessService.builderOffsetCostInfo(eventJob, handlerEventType(),
                        oldSettlementCostInfo,eventJob.getCreateTime(), SYSTEM_CORRECTION_USER, "保费变更冲正", Boolean.TRUE));
                //生成一条新结算信息
                SettlementCostInfoEntity newSettlementCostInfo = new SettlementCostInfoEntity();
                BeanUtil.copyProperties(oldSettlementCostInfo, newSettlementCostInfo);
                newSettlementCostInfo.setId(null);
                newSettlementCostInfo.setCostCode(PolicySettlementUtils.createCodeLastNumber("CC"));
                //记账时间处理
                newSettlementCostInfo.setSettlementTime(new Date());
                newSettlementCostInfo.setSettlementDate(newSettlementCostInfo.getSettlementTime());

                //事件编号
                newSettlementCostInfo.setEventSourceCode(eventJob.getPushEventCode());

                //事件信息
                newSettlementCostInfo.setSettlementEventCode(handlerEventType().getEventCode());
                newSettlementCostInfo.setSettlementEventDesc(handlerEventType().getEventDesc());
                newSettlementCostInfo.setCommissionType(oldSettlementCostInfo.getCommissionType());
                newSettlementCostInfo.setSettlementGenerateType(2);
                //是否冲正
                //业务记账时间处理
                newSettlementCostInfo.setBusinessAccountTime(SettlementCostProcessServiceImpl.getCorrectionBusinessAccountTime(oldSettlementCostInfo.getBusinessAccountTime(),eventJob.getCreateTime()));
                newSettlementCostInfo.setSourceCostCode(oldSettlementCostInfo.getCostCode());
                newSettlementCostInfo.setCorrectionTime(new Date());
                newSettlementCostInfo.setCorrectionFlag(0);

                newSettlementCostInfo.setCorrectionOpType(1);
                if (StringUtil.isNotBlank(changePolicyCodeData.getBusinessDesc())) {
                    newSettlementCostInfo.setCorrectionRemark(changePolicyCodeData.getBusinessDesc());
                }
                //清除确认信息
                newSettlementCostInfo.setConfirmStatus(ConfirmStatusEnum.UNCONFIRMED.getCode());
                newSettlementCostInfo.setConfirmUser(null);
                newSettlementCostInfo.setConfirmTime(null);
                newSettlementCostInfo.setConfirmGrantTime(null);
                newSettlementCostInfo.setDocumentCode(null);
                newSettlementCostInfo.setAutoCostCode(null);
                newSettlementCostInfo.setCostSettlementCycle(null);
                //冲正金额字段
                newSettlementCostInfo.setPremium(changePremiumInsuredProduct.getPremium());
                newSettlementCostInfo.setBusinessPremium(changePremiumInsuredProduct.getPremium());
                newSettlementCostInfo.setPremium(changePremiumInsuredProduct.getPremium());
                newSettlementCostInfo.setDiscountPremium(ReconcileBaseHelper.calcDiscountPremium(policyNo, changePremiumInsuredProduct.getProductCode(), oldSettlementCostInfo.getInsuredPolicyAge(), PolicyProductTypeEnum.getProdTypeEnum(costPolicy.getPolicyProductType()), oldSettlementCostInfo.getLongShortFlag(), changePremiumInsuredProduct.getPremium(), oldSettlementCostInfo.getPeriodType(), oldSettlementCostInfo.getPaymentPeriodType(), oldSettlementCostInfo.getPaymentPeriod()));
                calcNewCommission(newSettlementCostInfo);

                newCostList.add(newSettlementCostInfo);
            }
        }


        //冲正
        CostCorrectionDto costCorrectionDto = new CostCorrectionDto();

        costCorrectionDto.setNewCostList(newCostList);
        costCorrectionDto.setCorrectionOldIds(costInfoEntityList.stream().map(SettlementCostInfoEntity::getId).collect(Collectors.toList()));
        costCorrectionDto.setPolicy(costPolicy);

        settlementCostCorrectionService.saveCostCommissionRecord(eventJob, costCorrectionDto);
        policyCenterBaseClient.governApplyConfirm(eventJob.getEventBusinessCode(), "settlement_server", "支出端");
        return "success";
    }

    @Override
    public SettlementEventTypeEnum handlerEventType() {
        return CHANGE_POLICY_PREMIUM;
    }
}
