package com.mpolicy.settlement.core.modules.autocost.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2024/05/31 9:18 下午
 * @Version 1.0
 */
@TableName("settlement_cost_account_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostAccountInfoEntity {
    /**
     * id
     */
    @TableId
    private Integer id;
    /**
     * 分支编码。
     */
    private String objectOrgCode;

    /**
     * 分支名称。
     */
    private String objectOrgName;

    /**
     * 工号。
     */
    private String sendObjectCode;

    /**
     * 姓名。
     */
    private String sendObjectName;

    /**
     * 上月负值，当此值大于0时，实际记录为0。
     */
    private BigDecimal lastMonthNegativeAmount;

    /**
     * 账户累计金额。
     */
    private BigDecimal amount;

    /**
     * 账户状态：0不可用，1可用。
     */
    private String accountStatus;

    /**
     * 是否删除标志：0有效，1删除。
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建人。
     */
    private String createUser;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @Version
    @TableField(fill = FieldFill.INSERT)
    private Integer revision;
}
