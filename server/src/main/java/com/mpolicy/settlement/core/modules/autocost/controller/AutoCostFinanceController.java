package com.mpolicy.settlement.core.modules.autocost.controller;


import com.mpolicy.common.result.Result;
import com.mpolicy.settlement.core.common.Constant;
import com.mpolicy.settlement.core.modules.autocost.service.*;
import com.mpolicy.web.common.annotation.PassToken;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 结算季度数据
 *
 * <AUTHOR>
 * @since 2023/12/16
 */
@RestController
@RequestMapping("/settlement/auto_cost/finance_quarter")
@Api(tags = "结算帮助服务")
@Slf4j
public class AutoCostFinanceController {

    @Autowired
    @Qualifier("simpleTaskExecutor")
    private AsyncTaskExecutor taskExecutor;

    @Autowired
    private SettlementCostFinanceService settlementCostFinanceService;


    /**
     * 结算季度数据汇总加载
     */
    @ApiOperation(value = "结算季度数据汇总加载", notes = "结算季度数据汇总加载")
    @PostMapping("/load_finance_quarter")
    @PassToken
    public Result<String> loadFinanceQuarter(@RequestParam @ApiParam(name = "fileCode", value = "文件编码") String fileCode,
                                           @RequestParam @ApiParam(name = "userName", value = "指定结算周期") String userName) {
        settlementCostFinanceService.loadFinanceQuarter(fileCode, userName);
        return Result.success(Constant.DEFAULT_SUCCESS);
    }
}
