package com.mpolicy.settlement.core.modules.autocost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mpolicy.settlement.core.common.autocost.SettlementDynamicSubjectDefinition;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementCostDynamicSubjectErrorDataEntity;
import com.mpolicy.settlement.core.modules.autocost.entity.SettlementDynamicSubjectDefinitionEntity;

import java.util.List;

public interface SettlementDynamicSubjectDefinitionService extends IService<SettlementDynamicSubjectDefinitionEntity> {
    /**
     * 根据科目名称模糊查询科目定义
     * @param subjectName
     * @return
     */
    List<SettlementDynamicSubjectDefinitionEntity> listLikeRightBySubjectName(String subjectName);

    /**
     * 根据科目名称精确查询科目定义
     * @param subjectName
     * @return
     */
    SettlementDynamicSubjectDefinitionEntity getBySubjectName(String subjectName);

    /**
     * 保存科目定义
     * @param entity
     */
    void saveSubjectDefinition(SettlementDynamicSubjectDefinitionEntity entity);

    List<SettlementDynamicSubjectDefinitionEntity> listBySubjectCodes(List<String> subjectCodes);
}
