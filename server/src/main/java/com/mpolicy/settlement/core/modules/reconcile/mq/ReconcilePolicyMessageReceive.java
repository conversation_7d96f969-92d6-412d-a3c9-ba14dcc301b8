package com.mpolicy.settlement.core.modules.reconcile.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.mq.MQMessage;
import com.mpolicy.settlement.core.config.RabbitConfig;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.mq.service.ReconcilePolicyMessageService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

/**
 * 保险业务中台消息订阅-保单中心结算事件
 *
 * <AUTHOR>
 * @since 2023/05/20
 */
@Component
@Slf4j(topic = "reconcileMq")
public class ReconcilePolicyMessageReceive {

    @Autowired
    private ReconcilePolicyMessageService reconcilePolicyMessageService;

    /**
     * <p>
     * exchange = settlement_topic
     * routeKey = settlement.global.policy.#
     * </p>
     *
     * @param message 消息内容
     * @param headers headers信息
     * @param channel channel信息
     * <AUTHOR>
     * @since 2022/05/25
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitConfig.SETTLEMENT_POLICY_GLOBAL_QUEUE)
    public void customerMessage(@Payload MQMessage msg, @Headers Map<String, Object> headers, Channel channel, Message message) throws IOException {
        // 消费者消息
        log.info("接受保单中心结算事件的消息，内容 = {},headers = {}", JSON.toJSONString(msg), headers.toString());
        Long deliveryTag = (Long) headers.get(AmqpHeaders.DELIVERY_TAG);
        // 操作编号 + 操作编号 + 消息体
        String opeType = msg.getOpeType();
        JSONObject data = msg.getData();
        log.info("opeType={}", opeType);
        try {
            // 获取事件枚举
            SettlementEventTypeEnum eventTypeEnum = SettlementEventTypeEnum.deCode(opeType);
            log.info("opeTypeEnum={}", eventTypeEnum);
            if (eventTypeEnum != null) {
                switch (eventTypeEnum) {
                    case PERSONAL_NEW_POLICY:
                    case GROUP_NEW_POLICY:
                    case RENEWAL_POLICY:
                    case RENEWAL_TERM_POLICY:
                    case STANDARD_SURRENDER:
                    case HESITATE_SURRENDER:
                    case PROTOCOL_TERMINATION:
                    case TERMINATION_PRODUCT:
                    case GROUP_ADD_OR_SUBTRACT:
                    case VEHICLE_PREMIUM_INFO_CHANGE:
                    case CUSTOMER_MANAGER_CHANGE:
                    case CHANNEL_REFERRER_CHANGE:
                    case POLICY_INTERRUPT:
                    case POLICY_INSURED_REFERRER_EVENT:
                    case POLICY_REFERRER_EVENT:
                    case POLICY_RENEWAL_TERM_REFERRER_EVENT:
                    case POLICY_ACTIVITY_CHANGE:
                    case POLICY_REFUND:
                    case POLICY_REPAY:
                    case POLICY_PRODUCT_CHANGE: // 险种变更
                    case POLICY_CHANNEL_DISTRIBUTION_CHANGE: // 图例变更
                    case POLICY_RECEIPT_REVISIT_EVENT: // 回执回访事件
                    case POLICY_SUPPLEMENT_PREMIUM:
                    case POLICY_RURAL_PROXY_CHANGE:
                    case POLICY_CHANNEL_CHANGE:{
                        log.info("接受保单中心结算事件={}", eventTypeEnum.getEventName());
                        reconcilePolicyMessageService.saveEventInfo(msg.getCode(), eventTypeEnum, data, false);
                        break;
                    }
                    default: {
                        log.warn("接受保单中心结算事件无需处理.");
                    }
                }
            } else {
                log.warn("无法受保单中心结算事件的消息，操作类型={}", opeType);
            }
            // 手工确认签收消息 ACK
            channel.basicAck(deliveryTag, false);
        } catch (GlobalException g) {
            log.warn("保单中心结算事件自定义异常警告,消息为={}", g.getMsg());
            // 手工确认签收消息 ACK
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            if (message.getMessageProperties().getRedelivered()) {
                log.warn("消息已重复处理失败,拒绝再次接收...", e);
                channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
            } else {
                log.warn("消息即将再次返回队列处理...", e);
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
            }
        }
    }
}