package com.mpolicy.settlement.core.modules.reconcile.event.factory;

import com.google.common.collect.Maps;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementStandardPremiumEventTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementEventHandler;
import com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementStandardPremiumEventHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * 结算事件处理工厂
 *
 * <AUTHOR>
 * @date 2024-11-26 22:57
 */
@Component
@Slf4j
public class SettlementStandardPremiumEventFactory {

    public static SettlementStandardPremiumEventFactory factory;


    private Map<String, SettlementStandardPremiumEventHandler> settlementStandardEventServices = Maps.newHashMap();

    @Autowired
    Map<String, SettlementStandardPremiumEventHandler> settlementEventMap;

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        factory = this;
        for (SettlementStandardPremiumEventHandler solver : settlementEventMap.values()) {
            settlementStandardEventServices.put(solver.handlerEventType().getEventCode(), solver);
        }
        factory.settlementStandardEventServices = settlementStandardEventServices;
    }

    /**
     * 根据事件枚举获取事件处理服务
     *
     * @param eventCode 事件类型编码
     * @return com.mpolicy.settlement.core.modules.reconcile.event.handler.SettlementStandardPremiumEventHandler
     */
    public static SettlementStandardPremiumEventHandler getSettlementEventHandler(String eventCode) {
        SettlementStandardPremiumEventTypeEnum eventType = SettlementStandardPremiumEventTypeEnum.deCode(eventCode);
        if (eventType == null) {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("标保事件枚举类型不能为空"));
        }
        return factory.settlementStandardEventServices.get(eventType.getEventCode());
    }
}