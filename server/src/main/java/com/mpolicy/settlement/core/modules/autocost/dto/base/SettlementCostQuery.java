package com.mpolicy.settlement.core.modules.autocost.dto.base;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 基础佣金基本信息查询对象
 *
 * <AUTHOR>
 * @since 2023-11-03 14:05
 */
@Data
@ApiModel(value = "基础佣金基本信息查询对象", description = "基础佣金基本信息查询对象")
public class SettlementCostQuery implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 数据归属成员
     * 温馨提示：数据归属成员 、数据归属成员集合未2选一
     */
    @ApiModelProperty(value = "数据归属成员", example = "ZHNX202020")
    private String employeeCode;

    /**
     * 数据归属成员集合
     * 温馨提示：数据归属成员 、数据归属成员集合未2选一
     */
    @ApiModelProperty(value = "数据归属成员集合")
    private List<String> employeeCodeList;


    /**
     * 包含的产品类型(个团车财)集合
     */
    @ApiModelProperty(value = "产品类型(个团车财)")
    private List<String>  policyProductType = new ArrayList<>();

    /**
     * 忽略产品类型(个团车财)集合
     */
    @ApiModelProperty(value = "产品类型(个团车财)")
    private List<String> ignorePolicyProductType = new ArrayList<>();

    /**
     * 是否启用新契约 0否 1是
     */
    @ApiModelProperty(value = "新契约启用标识 0否 1是")
    private Integer newPolicyActivation;

    /**
     * 续投启动标识  0否 1是
     */
    @ApiModelProperty(value = "续投启动标识  0否 1是")
    private Integer maintainActivation;

    /**
     * 续期启动标识  0否 1是
     */
    @ApiModelProperty(value = "续期启动标识  0否 1是")
    private Integer renewalActivation;

    /**
     * 保全启动标识  0否 1是
     */
    @ApiModelProperty(value = "保全启动标识  0否 1是")
    private Integer preservationActivation;

    /**
     * 长短险标记 0短险1长险
     */
    @ApiModelProperty(value = "长短险标记 0短险1长险")
    private Integer longShortFlag;

    /**
     * 整村推进，村代模式
     */
    @ApiModelProperty(value = "整村推进 0否 1是")
    private Integer ruralProxyFlag;

    /**
     * 是否分销单
     */
    @ApiModelProperty(value = "是否分销单 0否 1是")
    private Integer distributionFlag;

    /**
     * 一单一议标识
     */
    @ApiModelProperty(value = "一单一议 0否 1是")
    private Integer singleProposeFlag;

    /**
     * 农机险表示
     */
    @ApiModelProperty(value = "农机险表示 0否 1是")
    private Integer agriculturalFlag;

    /**
     * 续期-回访结果 0未回访 1回访成功
     */
    @ApiModelProperty(value = "续期回访结果 0未回访 1回访成功")
    private Integer revisitFlag;

    /**
     * 续期-回访截止时间
     */
    @ApiModelProperty(value = "续期-回访截止时间")
    private Date revisitEndTime;

    /**
     * 续期-回执截止时间
     */
    @ApiModelProperty(value = "续期-回执截止时间")
    private Date receiptEndTime;

    /**
     * 续期-实收开始时间
     */
    @ApiModelProperty(value = "续期-实收开始时间")
    private Date realityStartTime;

    /**
     * 续期-结束开始时间
     */
    @ApiModelProperty(value = "续期-实收开始时间")
    private Date realityEndTime;

    /**
     * 续期-业务记账开始时间
     */
    @ApiModelProperty(value = "续期-业务记账开始时间")
    private Date realityBusinessStartTime;

    /**
     * 续期-业务记账截止时间
     */
    @ApiModelProperty(value = "续期-业务记账截止时间")
    private Date realityBusinessEndTime;

    /**
     * 新契约-承保开始时间
     */
    @ApiModelProperty(value = "新契约-承保开始时间")
    private Date newPolicyUnderwriteStartTime;

    /**
     * 新契约-承保截止时间
     */
    @ApiModelProperty(value = "新契约-承保截止时间")
    private Date newPolicyUnderwriteEndTime;

    /**
     * 新契约-业务记账开始时间
     */
    @ApiModelProperty(value = "新契约-业务记账开始时间")
    private Date newPolicyBusinessStartTime;

    /**
     * 新契约-业务记账截止时间
     */
    @ApiModelProperty(value = "新契约-业务记账截止时间")
    private Date newPolicyBusinessEndTime;

    /**
     * 续投-承保开始时间
     */
    @ApiModelProperty(value = "续投-承保开始时间")
    private Date renewalUnderwriteStartTime;

    /**
     * 续投-承保截止时间
     */
    @ApiModelProperty(value = "续投-承保截止时间")
    private Date renewalUnderwriteEndTime;

    /**
     * 续投-业务记账开始时间
     */
    @ApiModelProperty(value = "续投-业务记账开始时间")
    private Date renewalBusinessStartTime;

    /**
     * 续投-业务记账截止时间
     */
    @ApiModelProperty(value = "续投-业务记账截止时间")
    private Date renewalBusinessEndTime;

    /**
     * 保全生效-开始时间
     */
    @ApiModelProperty(value = "保全生效-开始时间")
    private Date preservationEffectiveStartTime;

    /**
     * 保全生效-截止时间
     */
    @ApiModelProperty(value = "保全生效-截止时间")
    private Date preservationEffectiveEndTime;

    /**
     * 保全批改-开始时间
     */
    @ApiModelProperty(value = "保全批改-开始时间")
    private Date preservationStartTime;

    /**
     * 保全批改-截止时间
     */
    @ApiModelProperty(value = "保全批改-截止时间")
    private Date preservationEndTime;

    /**
     * 保全批改-业务记账开始时间
     */
    @ApiModelProperty(value = "保全批改-业务记账开始时间")
    private Date preservationBusinessStartTime;

    /**
     * 保全批改-业务记账截止时间
     */
    @ApiModelProperty(value = "保全批改-业务记账截止时间")
    private Date preservationBusinessEndTime;

    /**
     * 业务记账-开始时间
     */
    @ApiModelProperty(value = "业务记账-开始时间")
    private Date businessAccountStartTime;

    /**
     * 业务记账-结束时间
     */
    @ApiModelProperty(value = "业务记账-结束时间")
    private Date businessAccountEndTime;

    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码集合")
    private List<String> regionCodeList = new ArrayList<>();

    /**
     * 机构分支编码集合
     */
    @ApiModelProperty(value = "机构分支编码集合")
    private List<String> orgCodeList = new ArrayList<>();
    /**
     * 单个机构编码
     */
    @ApiModelProperty(value = "单个机构编码")
    private String orgCode;

    /**
     * 指定产品编码集合
     */
    @ApiModelProperty(value = "指定产品编码集合")
    private List<String> productCodeList = new ArrayList<>();

    /**
     * 忽略的产品编码集合
     */
    @ApiModelProperty(value = "忽略的产品编码集合")
    private List<String> ignoreProductCodeList = new ArrayList<>();
    /**
     * 事件编码
     */
    private List<String> eventCodeList;

    /**
     * 基础佣金编码costCode
     */
    private List<String> costCodeList;

    /**
     * 动态规则数据条件
     */
    private JSONObject dynamicRule;

    /**
     * 是否根据绩效分配标志位计算  0 否，1 是
     */
    private Integer performanceAllocationFlag;
    /**
     * 参与绩效分配的险种编码
     */
    private List<String> performanceProductCods;
    /**
     * 第三方离职状态
     */
    private Integer ownerThirdStatus;

}