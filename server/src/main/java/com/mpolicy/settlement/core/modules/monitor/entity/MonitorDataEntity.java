package com.mpolicy.settlement.core.modules.monitor.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 监控数据实体
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Data
public class MonitorDataEntity {

    /**
     * 监控ID
     */
    private Long monitorId;

    /**
     * 监控类型
     */
    private String monitorType;

    /**
     * 监控指标名称
     */
    private String metricName;

    /**
     * 监控指标值
     */
    private String metricValue;

    /**
     * 监控单位
     */
    private String unit;

    /**
     * 监控时间
     */
    private LocalDateTime monitorTime;

    /**
     * 服务器IP
     */
    private String serverIp;

    /**
     * 服务器名称
     */
    private String serverName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
