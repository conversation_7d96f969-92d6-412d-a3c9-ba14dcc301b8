package com.mpolicy.settlement.core.modules.autocost.dto.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 自动结算科目数据基础请求
 *
 * <AUTHOR>
 * @since  2023-11-01 19:50
 */
@Data
@ApiModel(value = "自动结算科目数据基础请求", description = "自动结算科目数据基础请求")
public class SubjectDataRequest<T> implements Serializable {

    private static final long serialVersionUID = 1;

    @ApiModelProperty(value = "结算周期", required = true, example = "A000001")
    @NotBlank(message = "结算周期不能为空")
    private String costSettlementCycle;

    @Valid
    private T data;
}
