package com.mpolicy.settlement.core.modules.reconcile.job;

import cn.hutool.core.date.DateUtil;
import com.mpolicy.policy.common.ep.policy.EpContractInfoVo;
import com.mpolicy.settlement.core.common.reconcile.enums.CostSubjectEnum;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementCostPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.entity.SettlementPolicyInfoEntity;
import com.mpolicy.settlement.core.modules.reconcile.enums.PolicyProductTypeEnum;
import com.mpolicy.settlement.core.modules.reconcile.helper.ReconcileBaseHelper;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementCostInfoService;
import com.mpolicy.settlement.core.modules.reconcile.service.SettlementCostPolicyInfoService;
import com.mpolicy.settlement.core.service.common.PolicyCenterBaseClient;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/18 2:37 下午
 * @Version 1.0
 */
@Component
@Slf4j(topic = "reconcileCore")
public class RefreshCostPolicyJob {
    @Autowired
    private PolicyCenterBaseClient policyCenterBaseClient;
    @Autowired
    private SettlementCostPolicyInfoService settlementCostPolicyInfoService;
    @Autowired
    private SettlementCostInfoService settlementCostInfoService;

    /**
     * 刷新支出端保单明细中，需要回执回访的明细纪录
     */
    @XxlJob("refreshCostPolicyReceiptAndRevisitJob")
    public void refreshCostPolicyReceiptAndRevisitJob() {
        XxlJobHelper.log("刷新支出端保单明细中，需要回执/回访的明细纪录 start date= {}", DateUtil.now());

        XxlJobHelper.log("支出端【回执】同步处理 start");

        List<SettlementCostPolicyInfoEntity> receiptList = settlementCostPolicyInfoService.lambdaQuery()
                .eq(SettlementCostPolicyInfoEntity::getReceiptStatus, 1)
                .isNull(SettlementCostPolicyInfoEntity::getReceiptTime)
                .last(" limit 6000")
                .list();
        log.info("支出端【回执】需要同步纪录条数={}", receiptList.size());
        if (!receiptList.isEmpty()) {
            receiptList.forEach(p -> {
                EpContractInfoVo policyInfo = policyCenterBaseClient.getPolicyInfoByPolicyCode(p.getPolicyNo());
                // 回执签署日期不为空
                if (policyInfo.getContractExtendInfo().getReceiptSignTime() != null) {
                    settlementCostPolicyInfoService.lambdaUpdate()
                            .set(SettlementCostPolicyInfoEntity::getReceiptTime, policyInfo.getContractExtendInfo().getReceiptSignTime())
                            .eq(SettlementCostPolicyInfoEntity::getId, p.getId())
                            .update();
                }
            });
        }

        XxlJobHelper.log("支出端【回访】保单明细信息补全 start");
        List<SettlementCostPolicyInfoEntity> revisitList = settlementCostPolicyInfoService.lambdaQuery()
                .eq(SettlementCostPolicyInfoEntity::getRevisitStatus, 1)
                .ne(SettlementCostPolicyInfoEntity::getRevisitResult,1)
                .last(" limit 6000")
                .list()
                ;
        log.info("【回访】需要同步纪录条数={}", revisitList.size());
        if (!revisitList.isEmpty()) {
            revisitList.forEach(p -> {
                EpContractInfoVo policyInfo = policyCenterBaseClient.getPolicyInfoByPolicyCode(p.getPolicyNo());
                // 回访日期不为空,且回访结果未成功的状态下处理
                if (policyInfo.getContractExtendInfo().getRevisitTime() != null && Objects.equals(policyInfo.getContractExtendInfo().getRevisitResult(), 1)) {
                    settlementCostPolicyInfoService.lambdaUpdate()
                            .set(SettlementCostPolicyInfoEntity::getRevisitTime, policyInfo.getContractExtendInfo().getRevisitTime())
                            .set(SettlementCostPolicyInfoEntity::getRevisitResult,policyInfo.getContractExtendInfo().getRevisitResult())
                            .set(SettlementCostPolicyInfoEntity::getRevisitSuccessTime,new Date())
                            .eq(SettlementCostPolicyInfoEntity::getId, p.getId())
                            .update();
                }
            });
        }

        XxlJobHelper.log("支出端【回访】保单明细信息补全 end");
        XxlJobHelper.handleSuccess("刷新支出端保单明细中，需要回执/回访的明细纪录 end 任务完成");
    }


    @XxlJob("compensateCostInfoDiscountPremiumJob")
    public void compensateCostInfoDiscountPremiumJob() {
        XxlJobHelper.log("补充计算支出端基础佣金折算保费  start");

        List<SettlementCostInfoEntity> receiptList = settlementCostInfoService.lambdaQuery()
                .eq(SettlementCostInfoEntity::getSettlementSubjectCode, CostSubjectEnum.FIRST_BASIC_COMM.getCode())
                .isNull(SettlementCostInfoEntity::getDiscountPremium)
                .last(" limit 1000")
                .list();
        log.info("补充计算支出端基础佣金折算保费记录数={}", receiptList.size());
        if (!receiptList.isEmpty()) {
            receiptList.forEach(p -> {
                try {
                    SettlementCostPolicyInfoEntity costPolicyInfo = settlementCostPolicyInfoService.lambdaQuery()
                            .eq(SettlementCostPolicyInfoEntity::getContractCode, p.getContractCode())
                            .one();
                    BigDecimal discountPremium = ReconcileBaseHelper.calcDiscountPremium(p.getPolicyNo(), p.getProductCode(), p.getInsuredPolicyAge(), PolicyProductTypeEnum.getProdTypeEnum(costPolicyInfo.getPolicyProductType()), p.getLongShortFlag(), p.getBusinessPremium(), p.getPeriodType(), p.getPaymentPeriodType(), p.getPaymentPeriod());


                    settlementCostInfoService.lambdaUpdate()
                            .set(SettlementCostInfoEntity::getDiscountPremium, discountPremium)
                            .eq(SettlementCostInfoEntity::getId, p.getId())
                            .update();
                }catch (Exception e){
                    log.warn("补充计算合同号{},保单号{}支出端基础佣金折算保费异常,记录id={}",p.getContractCode(),p.getPolicyNo(),p.getId(),e);
                }
            });
        }


        XxlJobHelper.log("补充计算支出端基础佣金折算保费 end");
        XxlJobHelper.handleSuccess("补充计算支出端基础佣金折算保费 end 任务完成");
    }
}



