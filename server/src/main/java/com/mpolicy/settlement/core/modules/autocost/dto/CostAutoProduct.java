package com.mpolicy.settlement.core.modules.autocost.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 农保自动结算科目险种维度明细表
 * @date 2023/11/5 5:28 下午
 * @Version 1.0
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "农保自动结算科目险种维度明细表", description = "农保自动结算科目险种维度明细表")
public class CostAutoProduct implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;
    /**
     * 结算支出险种凭证编码
     */
    private String costProductCode;
    /**
     * 结算支出编码
     */
    private String autoCostCode;
    /**
     * 计算周期
     */
    private String costSettlementCycle;
    /**
     * 计算时间
     */
    private Date costSettlementTime;
    /**
     * 方案编码
     */
    private String programmeCode;
    /**
     * 方案名称
     */
    private String programmeName;
    /**
     * 科目编码
     */
    private String subjectCode;
    /**
     * 科目名称
     */
    private String subjectName;
    /**
     * 发放对象类型
     */
    private String sendObjectType;
    /**
     * 发放对象编码
     */
    private String sendObjectCode;
    /**
     * 发放对象名称
     */
    private String sendObjectName;
    /**
     * 发放对象机构编码
     */
    private String objectOrgCode;
    /**
     * 发放对象机构名称
     */
    private String objectOrgName;
    /**
     * 单据编号
     */
    private String documentCode;
    /**
     * 结算机构编码
     */
    private String settlementInstitution;
    /**
     * 结算机构名称
     */
    private String settlementInstitutionName;
    /**
     * 记账日期
     */
    private Date settlementDate;
    /**
     * 险种编码
     */
    private String productCode;
    /**
     * 险种名称
     */
    private String productName;
    /**
     * 协议产品编码
     */
    private String protocolProductCode;
    /**
     * 协议产品名称
     */
    private String protocolProductName;
    /**
     * 长短险标记 0短险1长险
     */
    private Integer longShortFlag;
    /**
     * 险种大类
     */
    private String productGroup;
    /**
     * 险种类型
     */
    private String productType;
    /**
     * 二级分类编码
     */
    private String level2Code;
    /**
     * 二级分类名称
     */
    private String level2Name;
    /**
     * 三级分类编码
     */
    private String level3Code;
    /**
     * 三级分类名称
     */
    private String level3Name;
    /**
     * 明细类型;policy 保单维度，product 险种维度，policy_product保单险种维度
     */
    private String costDataType;
    /**
     * 保费
     */
    private BigDecimal premium;
    /**
     * 费用类型：推广费，津贴、激励，整村推荐推广费，代发区域营销费
     */
    private String amountType;
    /**
     * 费用
     */
    private BigDecimal amount;
    /**
     * 提成比例
     */
    private BigDecimal commissionRate;
    /**
     * 自动结算佣金
     */
    private BigDecimal commissionAmount;
    /**
     * 发放比例 0-100
     */
    private BigDecimal grantRate;
    /**
     * 发放金额
     */
    private BigDecimal grantAmount;
    /**
     * 续期率
     */
    private BigDecimal renewalRate;
    /**
     * 继续率月份[yyyy-MM]
     */
    private String bizMonth;
    /**
     * 增长系数
     */
    private BigDecimal growthFactor;
    /**
     * pco等级
     */
    private String pcoLevel;

    /**
     * 人均单量
     */
    private BigDecimal perCapitaCount;
    /**
     * 注册用户数
     */
    private Integer registeredUsers;
    /**
     * 人均注册用户数
     */
    private BigDecimal avgRegisterUserCount;
    /**
     * 业务数据类型
     */
    private String businessDataType;
    /**
     * 源单据编号
     */
    private String sourceDocumentCode;

    /**
     * 源自动结算支出编码
     */
    private String sourceCostProductCode;
    /**
     * 分摊率
     */
    private BigDecimal shareRate;

    /**
     * 发放科目编码
     */
    private String sendSubjectCode;
    /**
     * 发放科目名称
     */
    private String sendSubjectName;
    /**
     * 是否动态科目
     */
    private Integer dynamicFlag;
}
