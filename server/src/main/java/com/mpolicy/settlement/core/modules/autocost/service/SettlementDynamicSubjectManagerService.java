package com.mpolicy.settlement.core.modules.autocost.service;

import com.mpolicy.settlement.core.common.autocost.SettlementDynamicSubjectDefinition;

import java.util.List;

public interface SettlementDynamicSubjectManagerService {
    /**
     *
     * @param definition
     */
    void saveDynamicSubjectDefinition(SettlementDynamicSubjectDefinition definition);

    /**
     *
     * @param subjectName
     * @return
     */
    List<SettlementDynamicSubjectDefinition> listLikeRightBySubjectName(String subjectName);

    /**
     *
     * @param subjectCodes
     * @return
     */
    List<SettlementDynamicSubjectDefinition> listBySubjectCodes(List<String> subjectCodes);

    List<SettlementDynamicSubjectDefinition> listAllDynamicSubjectDefinition();

    /**
     * 清空动态科目数据
     * @param applyCode
     * @param errorMessage
     */
    void clearDynamicData(String applyCode, String errorMessage);
}
