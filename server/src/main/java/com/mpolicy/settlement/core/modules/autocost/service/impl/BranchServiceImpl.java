package com.mpolicy.settlement.core.modules.autocost.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.open.common.cfpamf.hr.EmployeeTotalRequest;
import com.mpolicy.open.common.cfpamf.hr.OrganizationRequest;
import com.mpolicy.open.common.hr.vo.EmployeeLdomVO;
import com.mpolicy.open.common.hr.vo.OrganizationLdomVO;
import com.mpolicy.settlement.core.modules.autocost.dto.employee.EmployeeInfo;
import com.mpolicy.settlement.core.modules.autocost.service.BranchService;
import com.mpolicy.settlement.core.service.common.OpenApiBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.mpolicy.settlement.core.modules.autocost.service.impl.EmployeeServiceImpl.PAGE_SIZE;

@Service
@Slf4j
public class BranchServiceImpl implements BranchService {
    @Autowired
    private OpenApiBaseService openApiBaseService;


    @Override
    public List<OrganizationLdomVO> queryBranchAllSnapshot(String monthSnapshot) {
        List<OrganizationLdomVO> result = new ArrayList<>();
        OrganizationRequest request = new OrganizationRequest();
        request.setYear(getSnapshotYear(monthSnapshot));
        request.setMonth(getSnapshotMonth(monthSnapshot));
        Integer page = 1;
        log.info("开始查询所有分支信息！{}", JSON.toJSONString(request));
        // 2 业务处理
        while (true) {
            log.info("查询所有分支信息！当前页码{}", page);
            request.setPage(page);
            request.setSize(200);
            // 2-1 分页获取分支信息
            List<OrganizationLdomVO> employeeList = openApiBaseService.queryBranchInfo(request, true);
            if (employeeList.isEmpty()) {
                break;
            }
            log.info("查询所有分支信息,当前页{}获取的机构记录数：{}",page,employeeList.size());
            log.info("查询所有分支信息,当前页{}获取机构编码：{}",page,JSON.toJSONString(employeeList.stream().map(OrganizationLdomVO::getBranchCode).collect(Collectors.toList())));
            result.addAll(employeeList);
            page++;
        }
        return result;
    }

    /**
     * 获取快照周期年份
     *
     * @param monthSnapshot 快照周期
     * @return 快照年份
     * <AUTHOR>
     * @since 2023/11/18 11:09
     */
    private Integer getSnapshotYear(String monthSnapshot) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date date = null;
        try {
            date = sdf.parse(monthSnapshot);
        } catch (ParseException e) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("格式无法进行转换日期", monthSnapshot)));
        }
        return Integer.parseInt(DateUtil.format(DateUtil.offsetMonth(date, -1), "yyyy"));
    }

    /**
     * 获取快照周期月份
     *
     * @param monthSnapshot 快照周期
     * @return 快照月份
     * <AUTHOR>
     * @since 2023/11/18 11:09
     */
    private Integer getSnapshotMonth(String monthSnapshot) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date date = null;
        try {
            date = sdf.parse(monthSnapshot);
        } catch (ParseException e) {
            throw new GlobalException(BasicCodeMsg.PARAMETER_ERROR.setMsg(StrUtil.format("格式无法进行转换日期", monthSnapshot)));
        }
        return Integer.parseInt(DateUtil.format(DateUtil.offsetMonth(date, -1), "MM"));
    }
}
