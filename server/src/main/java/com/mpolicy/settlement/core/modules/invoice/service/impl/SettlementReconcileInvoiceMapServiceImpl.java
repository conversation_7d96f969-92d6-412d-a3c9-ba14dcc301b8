package com.mpolicy.settlement.core.modules.invoice.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mpolicy.settlement.core.modules.invoice.dao.SettlementReconcileInvoiceMapDao;
import com.mpolicy.settlement.core.modules.invoice.entity.SettlementReconcileInvoiceMapEntity;
import com.mpolicy.settlement.core.modules.invoice.service.SettlementReconcileInvoiceMapService;
import org.springframework.stereotype.Service;

@Service("settlementReconcileInvoiceMapService")
public class SettlementReconcileInvoiceMapServiceImpl extends ServiceImpl<SettlementReconcileInvoiceMapDao, SettlementReconcileInvoiceMapEntity> implements SettlementReconcileInvoiceMapService {


}