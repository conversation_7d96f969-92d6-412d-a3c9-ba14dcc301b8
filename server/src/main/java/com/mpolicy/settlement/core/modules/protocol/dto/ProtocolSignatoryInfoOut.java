package com.mpolicy.settlement.core.modules.protocol.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 协议签署方信息
 * <AUTHOR>
 */
@Data
public class ProtocolSignatoryInfoOut implements Serializable {

    /**
     * 协议编码
     */
    private String protocolCode;
    /**
     * 小鲸险种编码
     */
    private String productCode;
    /**
     * 小鲸险种名称
     */
    private String productName;
    /**
     * 保司产品编码
     */
    private String companyCode;
    /**
     * 保司产品名称
     */
    private String companyName;
    /**
     * 保司简称
     */
    private String companyShortName;
    /**
     * 协议保司产品编码
     */
    private String insuranceProductCode;
    /**
     * 协议保司产品名称
     */
    private String insuranceProductName;
    /**
     * 外部签署方类型字典 保险公司/经纪公司/代理公司
     */
    private String externalSignatoryType;
    /**
     * 外部签署方编码
     */
    private String externalSignatoryCode;
    /**
     * 外部签署方名称
     */
    private String externalSignatoryName;
    /**
     * 内部签署方名称
     */
    private String innerSignatoryName;
    /**
     * 内部签署方编码
     */
    private String innerSignatoryCode;
}
