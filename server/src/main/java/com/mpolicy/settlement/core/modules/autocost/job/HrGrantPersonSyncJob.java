package com.mpolicy.settlement.core.modules.autocost.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.settlement.core.modules.autocost.service.HrGrantPersonSyncService;
import com.mpolicy.settlement.core.modules.autocost.utils.AutoCostUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;
import static cn.hutool.core.date.DatePattern.SIMPLE_MONTH_PATTERN;

/**
 * Hr发放人员信息同步定时任务
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class HrGrantPersonSyncJob {

    private final HrGrantPersonSyncService hrGrantPersonSyncService;

    private static final String PARAM_FORCE_OVERRIDE = "true";
    private static final String PARAM_SEPARATOR = "|";
    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");

    /**
     * Hr发放人员信息同步定时任务
     *
     * 任务参数说明：
     * - 无参数或"false"：正常同步模式，不覆盖已存在数据
     * - "true"：强制覆盖模式，删除已存在数据后重新同步
     * - 指定日期格式(YYYY-MM-DD)：同步指定结算周期的数据
     * - 指定日期格式(YYYY-MM-DD)+"|true"：强制覆盖指定结算周期的数据
     */
    @XxlJob("hrGrantPersonSyncJob")
    public void hrGrantPersonSyncJob() {
        XxlJobHelper.log("开始执行Hr发放人员信息同步任务");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("hrGrantPersonSyncJob入参：{}", jobParam);

        try {
            // 解析任务参数
            JobParams params = parseJobParams(jobParam);

            log.info("开始同步Hr发放人员信息 - 结算周期：{}，强制覆盖：{}", params.getSettlementCycle(), params.isForceOverride());
            XxlJobHelper.log("开始同步Hr发放人员信息 - 结算周期：{}，强制覆盖：{}", params.getSettlementCycle(), params.isForceOverride());

            // 执行同步
            int syncCount = hrGrantPersonSyncService.syncHrGrantPersonInfo(params.getSettlementCycle(), params.isForceOverride());

            String successMsg = String.format("Hr发放人员信息同步完成 - 结算周期：%s，同步记录数：%d",
                    params.getSettlementCycle(), syncCount);
            log.info(successMsg);
            XxlJobHelper.log(successMsg);
            XxlJobHelper.handleSuccess(successMsg);

        } catch (GlobalException e) {
            handleJobError("Hr发放人员信息同步失败", e);
        } catch (Exception e) {
            handleJobError("Hr发放人员信息同步异常", e);
        }
    }

    /**
     * Hr发放人员信息清理任务
     *
     * 任务参数说明：
     * - 指定日期格式(YYYY-MM-DD)：清理指定结算周期的数据
     */
    @XxlJob("hrGrantPersonCleanJob")
    public void hrGrantPersonCleanJob() {
        XxlJobHelper.log("开始执行Hr发放人员信息清理任务");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("hrGrantPersonCleanJob入参：{}", jobParam);

        try {
            // 参数校验
            if (StrUtil.isBlank(jobParam)) {
                String errorMsg = "清理任务必须指定结算周期参数(YYYY-MM-DD格式)";
                handleJobError(errorMsg, new IllegalArgumentException(errorMsg));
                return;
            }

            String settlementCycle = jobParam.trim();
            validateSettlementCycle(settlementCycle);

            log.info("开始清理Hr发放人员信息 - 结算周期：{}", settlementCycle);
            XxlJobHelper.log("开始清理Hr发放人员信息 - 结算周期：{}", settlementCycle);

            // 执行清理
            int deleteCount = hrGrantPersonSyncService.deleteBySettlementCycle(settlementCycle);

            String successMsg = String.format("Hr发放人员信息清理完成 - 结算周期：%s，清理记录数：%d",
                    settlementCycle, deleteCount);
            log.info(successMsg);
            XxlJobHelper.log(successMsg);
            XxlJobHelper.handleSuccess(successMsg);

        } catch (Exception e) {
            handleJobError("Hr发放人员信息清理异常", e);
        }
    }

    /**
     * 解析任务参数
     */
    private JobParams parseJobParams(String jobParam) {
        String settlementCycle;
        boolean forceOverride = false;

        if (StrUtil.isBlank(jobParam)) {
            // 默认使用当月结算周期
            settlementCycle = AutoCostUtils.costSettlementMonthSnapshot();
        } else if (PARAM_FORCE_OVERRIDE.equals(jobParam)) {
            // 强制覆盖模式，使用上月最后一天
            settlementCycle = AutoCostUtils.costSettlementMonthSnapshot();
            forceOverride = true;
        } else if (jobParam.contains(PARAM_SEPARATOR)) {
            // 指定日期+覆盖标志
            String[] params = jobParam.split("\\" + PARAM_SEPARATOR);
            if (params.length >= 2) {
                settlementCycle = params[0].trim();
                forceOverride = PARAM_FORCE_OVERRIDE.equals(params[1].trim());
            } else {
                settlementCycle = params[0].trim();
            }
        } else {
            // 指定日期
            settlementCycle = jobParam.trim();
        }

        // 校验结算周期格式
        validateSettlementCycle(settlementCycle);

        return new JobParams(settlementCycle, forceOverride);
    }

    /**
     * 校验结算周期格式
     */
    private void validateSettlementCycle(String settlementCycle) {
        try {
            LocalDate.parse(settlementCycle, MONTH_FORMATTER);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("结算周期格式错误，应为：YYYYMM，实际值：" + settlementCycle);
        }
    }

    /**
     * 处理任务错误
     */
    private void handleJobError(String message, Exception e) {
        String errorMsg = String.format("%s：%s", message, e.getMessage());
        log.error(errorMsg, e);
        XxlJobHelper.log(errorMsg);
        XxlJobHelper.handleFail(errorMsg);
    }

    /**
     * 任务参数内部类
     */
    private static class JobParams {
        private final String settlementCycle;
        private final boolean forceOverride;

        public JobParams(String settlementCycle, boolean forceOverride) {
            this.settlementCycle = settlementCycle;
            this.forceOverride = forceOverride;
        }

        public String getSettlementCycle() { return settlementCycle; }
        public boolean isForceOverride() { return forceOverride; }
    }
}
