package com.mpolicy.settlement.core.modules.autocost.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.settlement.core.modules.autocost.service.HrGrantPersonSyncService;
import com.mpolicy.settlement.core.modules.autocost.utils.AutoCostUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * Hr发放人员信息同步定时任务
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Component
@Slf4j
public class HrGrantPersonSyncJob {

    @Autowired
    private HrGrantPersonSyncService hrGrantPersonSyncService;

    /**
     * Hr发放人员信息同步定时任务
     *
     * 任务参数说明：
     * - 无参数或"false"：正常同步模式，不覆盖已存在数据
     * - "true"：强制覆盖模式，删除已存在数据后重新同步
     * - 指定日期格式(YYYY-MM-DD)：同步指定结算周期的数据
     * - 指定日期格式(YYYY-MM-DD)+"|true"：强制覆盖指定结算周期的数据
     */
    @XxlJob("hrGrantPersonSyncJob")
    public void hrGrantPersonSyncJob() {
        XxlJobHelper.log("开始执行Hr发放人员信息同步任务");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("hrGrantPersonSyncJob入参：{}", jobParam);

        try {
            String settlementCycle;
            boolean forceOverride = false;

            // 解析任务参数
            if (StringUtils.isBlank(jobParam)) {
                // 默认使用上月最后一天作为结算周期
                settlementCycle = DateUtil.format(DateUtil.endOfMonth(DateUtil.lastMonth()), NORM_DATE_PATTERN);
            } else if ("true".equals(jobParam)) {
                // 强制覆盖模式，使用上月最后一天
                settlementCycle = DateUtil.format(DateUtil.endOfMonth(DateUtil.lastMonth()), NORM_DATE_PATTERN);
                forceOverride = true;
            } else if (jobParam.contains("|")) {
                // 指定日期+覆盖标志
                String[] params = jobParam.split("\\|");
                settlementCycle = params[0];
                forceOverride = "true".equals(params[1]);
            } else {
                // 指定日期
                settlementCycle = jobParam;
            }

            log.info("开始同步Hr发放人员信息 - 结算周期：{}，强制覆盖：{}", settlementCycle, forceOverride);
            XxlJobHelper.log("开始同步Hr发放人员信息 - 结算周期：{}，强制覆盖：{}", settlementCycle, forceOverride);

            // 执行同步
            int syncCount = hrGrantPersonSyncService.syncHrGrantPersonInfo(settlementCycle, forceOverride);

            String successMsg = String.format("Hr发放人员信息同步完成 - 结算周期：%s，同步记录数：%d", settlementCycle, syncCount);
            log.info(successMsg);
            XxlJobHelper.log(successMsg);
            XxlJobHelper.handleSuccess(successMsg);

        } catch (GlobalException e) {
            String errorMsg = String.format("Hr发放人员信息同步失败：%s", e.getMessage());
            log.error(errorMsg, e);
            XxlJobHelper.log(errorMsg);
            XxlJobHelper.handleFail(errorMsg);
        } catch (Exception e) {
            String errorMsg = String.format("Hr发放人员信息同步异常：%s", e.getMessage());
            log.error(errorMsg, e);
            XxlJobHelper.log(errorMsg);
            XxlJobHelper.handleFail(errorMsg);
        }
    }

    /**
     * Hr发放人员信息清理任务
     *
     * 任务参数说明：
     * - 指定日期格式(YYYY-MM-DD)：清理指定结算周期的数据
     */
    @XxlJob("hrGrantPersonCleanJob")
    public void hrGrantPersonCleanJob() {
        XxlJobHelper.log("开始执行Hr发放人员信息清理任务");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("hrGrantPersonCleanJob入参：{}", jobParam);

        try {
            if (StringUtils.isBlank(jobParam)) {
                String errorMsg = "清理任务必须指定结算周期参数(YYYY-MM-DD格式)";
                log.error(errorMsg);
                XxlJobHelper.log(errorMsg);
                XxlJobHelper.handleFail(errorMsg);
                return;
            }

            String settlementCycle = jobParam.trim();
            log.info("开始清理Hr发放人员信息 - 结算周期：{}", settlementCycle);
            XxlJobHelper.log("开始清理Hr发放人员信息 - 结算周期：{}", settlementCycle);

            // 执行清理
            int deleteCount = hrGrantPersonSyncService.deleteBySettlementCycle(settlementCycle);

            String successMsg = String.format("Hr发放人员信息清理完成 - 结算周期：%s，清理记录数：%d", settlementCycle, deleteCount);
            log.info(successMsg);
            XxlJobHelper.log(successMsg);
            XxlJobHelper.handleSuccess(successMsg);

        } catch (Exception e) {
            String errorMsg = String.format("Hr发放人员信息清理异常：%s", e.getMessage());
            log.error(errorMsg, e);
            XxlJobHelper.log(errorMsg);
            XxlJobHelper.handleFail(errorMsg);
        }
    }
}
