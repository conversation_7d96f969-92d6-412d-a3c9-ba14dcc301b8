package com.mpolicy.settlement.core.modules.autocost.job;

import cn.hutool.core.date.DateException;
import cn.hutool.core.date.DateUtil;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.settlement.core.modules.autocost.service.BranchDirectorService;
import com.mpolicy.settlement.core.modules.autocost.service.BranchPcoService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

import static cn.hutool.core.date.DatePattern.PURE_DATE_PATTERN;

/**
 * 【自动结算】 主任绩效寻人job
 *
 * <AUTHOR>
 * @since 2025/01/21
 */
@Component
@Slf4j
public class BranchDirectorJob {

    @Autowired
    private BranchDirectorService branchDirectorService;
    @Autowired
    private BranchPcoService branchPcoService;

    /**
     * 主任绩效寻人job
     */
    @XxlJob("lookBranchDirectorJob")
    public void lookBranchDirectorJob() {
        XxlJobHelper.log("开始执行主任绩效寻人job.");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("lookBranchDirectorJob入参：{}",jobParam);
        try {
            //结算主任信息的生成周期e
            String generateCycle = DateUtil.format(DateUtil.endOfMonth(DateUtil.lastMonth()),PURE_DATE_PATTERN);
            if(Objects.equals(jobParam,"true")){
                log.info("开始强制覆盖历史同步记录");
                branchDirectorService.generateBranchDirectorInfo(generateCycle,Boolean.TRUE);
                log.info("完成强制覆盖历史同步记录");
            }else{
                branchDirectorService.generateBranchDirectorInfo(generateCycle,Boolean.FALSE);
            }

        }catch (GlobalException e){
            XxlJobHelper.log("主任绩效寻人操作失败，{}",e);
            log.error("主任绩效寻人操作失败，{}",e);
        }catch (Exception e){
            XxlJobHelper.log("主任绩效寻人操作失败，{}",e);
            log.error("主任绩效寻人操作失败，{}",e);
        }


        XxlJobHelper.handleSuccess("主任绩效寻人job任务完成");
    }

    /**
     * 生服对接人绩效寻人job
     */
    @XxlJob("lookBranchPcoJob")
    public void lookBranchPcoJob() {
        XxlJobHelper.log("生服对接人绩效寻人job.");
        String jobParam = XxlJobHelper.getJobParam();
        log.info("lookBranchPcoJob入参：{}",jobParam);
        try {
            //结算主任信息的生成周期e
            String generateCycle = DateUtil.format(DateUtil.endOfMonth(DateUtil.lastMonth()),PURE_DATE_PATTERN);
            if(Objects.equals(jobParam,"true")){
                log.info("开始强制覆盖历史同步记录");
                branchPcoService.generatePcoInfo(generateCycle,Boolean.TRUE);
                log.info("完成强制覆盖历史同步记录");
            }else{
                branchPcoService.generatePcoInfo(generateCycle,Boolean.FALSE);
            }

        }catch (GlobalException e){
            XxlJobHelper.log("生服对接人绩效寻人操作失败，{}",e);
            log.error("生服对接人绩效寻人操作失败，{}",e);
        }catch (Exception e){
            XxlJobHelper.log("生服对接人绩效寻人操作失败，{}",e);
            log.error("生服对接人绩效寻人操作失败，{}",e);
        }


        XxlJobHelper.handleSuccess("生服对接人绩效寻人job任务完成");
    }
}
