package com.mpolicy.settlement.core.modules.autocost.dto;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.modules.reconcile.enums.ProductStatusEnum;
import com.mpolicy.settlement.core.modules.reconcile.enums.SettlementEventTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jodd.util.StringUtil;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = true)
@ApiModel("长险支出佣金excel导入文档")
public class SettlementCostExcelDto extends BaseRowModel implements Serializable {

    @ApiModelProperty("记账时间")
    @ExcelProperty(value="记账时间", index = 0)
    Date businessAccountTime;

    @ApiModelProperty("保单号")
    @ExcelProperty(value="保单号", index = 1)
    String policyNo;

    @ApiModelProperty("保单状态")
    @ExcelProperty(value="保单状态", index = 2)
    String policyStatusCn;

    public String getProductStatus(){
        if(Objects.equals(policyStatusCn,"承保成功")){
            return ProductStatusEnum.ACTIVE.getCode();
        }else if(Objects.equals(policyStatusCn,"退保成功")){
            return ProductStatusEnum.CANCELLATION.getCode();
        }else if(Objects.equals(policyStatusCn,"协议退保")){
            return ProductStatusEnum.AGREEMENT_TERMINATION.getCode();
        }else {
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保单"+policyNo+"保单状态["+policyStatusCn+"]对应的险种状态未映射"));
        }
    }

    @ApiModelProperty("期数")
    @ExcelProperty(value="期数", index = 3)
    Integer renewalPeriod;

    @ApiModelProperty("类型：标准退保、协议解约、新契约、续期")
    @ExcelProperty(value="类型" , index = 4)
    String commissionType;

    public SettlementEventTypeEnum getEventType(){
        if(Objects.equals(commissionType,"标准退保")){
            return SettlementEventTypeEnum.STANDARD_SURRENDER;
        }else if(Objects.equals(commissionType,"协议解约")){
            return SettlementEventTypeEnum.PROTOCOL_TERMINATION;
        }else if(Objects.equals(commissionType,"新契约")){
            return SettlementEventTypeEnum.PERSONAL_NEW_POLICY;
        }else if(Objects.equals(commissionType,"续期")){
            return SettlementEventTypeEnum.RENEWAL_TERM_POLICY;
        }else{
            throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保单"+policyNo+"类型["+commissionType+"]对应的事件类型未映射"));
        }
    }

    @ApiModelProperty("投保产品名称")
    @ExcelProperty(value="投保产品名称", index = 5)
    String productName;

    @ApiModelProperty("险种保费")
    @ExcelProperty(value="险种保费", index = 6)

    BigDecimal premium;

    @ApiModelProperty("折算保费")
    @ExcelProperty(value="折算保费", index = 7)
    BigDecimal discountPremium;

    @ApiModelProperty("被保人")
    @ExcelProperty(value="被保人", index = 8)
    String insuredName;


    @ApiModelProperty("基础佣金")
    @ExcelProperty(value="基础佣金", index = 9)
    BigDecimal basicCostAmount;

    @ApiModelProperty("加佣金额")
    @ExcelProperty(value="加佣金额", index = 10)
    BigDecimal addCostAmount;

    @ApiModelProperty("佣金比例")
    @ExcelProperty(value="佣金比例", index = 11)
    String costRateStr;

    @ApiModelProperty("佣金比例")
    public BigDecimal getCostRate() {
        if(StringUtil.isNotBlank(costRateStr)){
            BigDecimal rate =  new BigDecimal(costRateStr);
            if(new BigDecimal("100").compareTo(rate)<0){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保单"+policyNo+"发放比例大于了100"));
            }
            return rate;
        }
        return BigDecimal.ZERO;
    }



    @ApiModelProperty("佣金合计")
    @ExcelProperty(value="佣金合计", index = 12)
    BigDecimal costAmount;

    @ApiModelProperty("发放比例")
    @ExcelProperty(value="发放比例", index = 13)
    String grantRateStr;
    @ApiModelProperty(value = "发放比例",hidden = true)
    public BigDecimal getGrantRate() {
        if(StringUtil.isNotBlank(grantRateStr)){
            BigDecimal rate =  new BigDecimal(grantRateStr).multiply(BigDecimal.valueOf(100)).setScale(2,BigDecimal.ROUND_HALF_UP);
            if(new BigDecimal("110").compareTo(rate)<0){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保单"+policyNo+"发放比例大于了110"));
            }
            return rate;
        }
        return BigDecimal.ZERO;
    }

    @ApiModelProperty("发放金额")
    @ExcelProperty(value="发放金额", index = 14)
    BigDecimal grantAmount;

    @ApiModelProperty("所属区域")
    @ExcelProperty(value="所属区域", index = 15)
    String regionName;

    @ApiModelProperty("所属机构")
    @ExcelProperty(value="所属机构", index = 16)
    String orgName;

    @ApiModelProperty(value = "所属机构编码",hidden = true)
    String orgCode;



    @ApiModelProperty("推荐人/管护经理姓名")
    @ExcelProperty(value="推荐人/管护经理姓名", index = 17)
    String recommendName;

    @ApiModelProperty("推荐人/管护经理编号")
    @ExcelProperty(value="推荐人/管护经理编号", index = 18)
    String recommendCode;

    @ApiModelProperty("是否前三后三")
    @ExcelProperty(value="是否前三后三", index = 19)
    String threeFlag;


    @ApiModelProperty("基础发放月份")
    @ExcelProperty(value="基础发放月份(yyyyMM)", index = 20)
    String grantMonthStr;

    public String  getGrantMonth() {
        if(StringUtils.isNotBlank(grantMonthStr)){
            return DateUtil.format(DateUtil.parse(grantMonthStr,"yyyyMM"), DatePattern.NORM_DATE_FORMAT);
        }
        return null;
    }

    @ApiModelProperty("次月记录记账日期")
    public Date getNextBusinessAccountTime(){
        if(StringUtils.isNotBlank(grantMonthStr)){
            return DateUtil.offsetDay(DateUtil.parse(grantMonthStr,"yyyyMM"),8);
        }
        return null;
    }

    @ApiModelProperty("次月统计续期率")
    @ExcelProperty(value="次月统计续期率", index = 21)
    String monthRenewalRateStr;

    public BigDecimal getMonthRenewalRate() {
        if(StringUtil.isNotBlank(actualRenewalRateStr) && !Objects.equals(actualRenewalRateStr,"-")){
            BigDecimal rate =  new BigDecimal(actualRenewalRateStr).multiply(BigDecimal.valueOf(100)).setScale(2,BigDecimal.ROUND_HALF_UP);
            if(new BigDecimal("100").compareTo(rate)<0){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保单"+policyNo+"次月统计续期率大于了100"));
            }
            return rate;
        }else{
            return null;
        }

    }

    @ApiModelProperty("次月发放比例")
    @ExcelProperty(value="次月发放比例", index = 22)
    String monthGrantRateStr;

    @ApiModelProperty(value = "次月发放比例",hidden = true)
    public BigDecimal getMonthGrantRate() {
        if(StringUtil.isNotBlank(monthGrantRateStr) && !Objects.equals(monthGrantRateStr,"-")){
            BigDecimal rate =  new BigDecimal(monthGrantRateStr).multiply(BigDecimal.valueOf(100)).setScale(2,BigDecimal.ROUND_HALF_UP);
            if(new BigDecimal("110").compareTo(rate)<0){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保单"+policyNo+"次月发放比例大于了110"));
            }
            return rate;
        }else{
            return null;
        }

    }

    @ApiModelProperty("次月发放金额")
    @ExcelProperty(value="次月发放金额", index = 23)
    BigDecimal monthGrantAmount;

    @ApiModelProperty("实际续期率")
    @ExcelProperty(value="实际续期率", index = 24)
    String actualRenewalRateStr;

    public BigDecimal getActualRenewalRate() {
        if(StringUtil.isNotBlank(actualRenewalRateStr) && !Objects.equals(actualRenewalRateStr,"-")){
            BigDecimal rate =  new BigDecimal(actualRenewalRateStr).multiply(BigDecimal.valueOf(100)).setScale(2,BigDecimal.ROUND_HALF_UP);
            if(new BigDecimal("100").compareTo(rate)<0){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保单"+policyNo+"实际续期率大于了100"));
            }
            return rate;
        }else{
            return null;
        }

    }

    @ApiModelProperty("续保率补发差额")
    @ExcelProperty(value="续保率补发差额", index = 25)
    BigDecimal reissueAmount;

    @ApiModelProperty("补发发放月")
    @ExcelProperty(value="补发发放月(yyyyMM)", index = 26)
    String reissueGrantMonthStr;
    @ApiModelProperty("补发发放月")
    public String getReissueGrantMonth(){
        if(StringUtils.isNotBlank(reissueGrantMonthStr)){
            return DateUtil.format(DateUtil.parse(reissueGrantMonthStr,"yyyyMM"), DatePattern.NORM_DATE_FORMAT);
        }
        return null;
    }

    @ApiModelProperty("补发记录记账日期")
    public Date getReissueBusinessAccountTime(){
        if(StringUtils.isNotBlank(reissueGrantMonthStr)){
            return DateUtil.offsetDay(DateUtil.parse(reissueGrantMonthStr,"yyyyMM"),8);
        }
        return null;
    }

    @ApiModelProperty("实发比例")
    @ExcelProperty(value="实发比例", index = 27)
    String actualGrantRateStr;

    @ApiModelProperty(value = "实发比例",hidden = true)
    public BigDecimal getActualGrantRate() {
        if(StringUtil.isNotBlank(actualGrantRateStr) && !Objects.equals(actualGrantRateStr,"-")){
            BigDecimal rate =  new BigDecimal(actualGrantRateStr).multiply(BigDecimal.valueOf(100)).setScale(2,BigDecimal.ROUND_HALF_UP);
            if(new BigDecimal("110").compareTo(rate)<0){
                throw new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg("保单"+policyNo+"发放比例大于了110"));
            }
            return rate;
        }else{
            return null;
        }

    }


    @ApiModelProperty("实发金额")
    @ExcelProperty(value="实发金额", index = 28)
    String actualGrantAmount;


}
