package com.mpolicy.settlement.core.modules.autocost.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 实体类表示结算生服对接人信息
 */
@TableName("settlement_branch_pco_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementBranchPcoInfoEntity {

        /**
         * 唯一标识ID。
         */
        private Integer id;

        /**
         * 生成日期。
         */
        private String generateDate;

        /**
         * 区域编码。
         */
        private String regionCode;

        /**
         * 区域名称。
         */
        private String regionName;

        /**
         * 机构编码。
         */
        private String bchCode;

        /**
         * 机构名称。
         */
        private String bchName;

        /**
         * 负责人工号。
         */
        private String employeeCode;

        /**
         * 负责人姓名。
         */
        private String employeeName;

        /**
         * 任职记录id，任职表hr_posting_record_ldom中主键。
         */
        private Integer empPostId;

        /**
         * 任职开始日期，关联左闭右闭。
         */
        private String postStartDate;

        /**
         * 任职结束日期（结束当日有效），关联左闭右闭。
         */
        private String postEndDate;

        /**
         * 任职部门ID。
         */
        private Long postingDept;

        /**
         * 岗位id。
         */
        private Integer jobId;

        /**
         * 岗位名称。
         */
        private String jobName;

        /**
         * 任职类型编码：0主职/1兼职。
         */
        private String postTypeCode;

        /**
         * 任职编码，人和岗位对应唯一编码。
         */
        private String jobCode;

        /**
         * 入职日期。
         */
        private String joinDate;

        /**
         * 离职日期(最后工作日)。
         */
        private String leaveDate;

        /**
         * 是否删除标志：0有效，1删除。
         */
        private Integer deleted;

        /**
         * 创建人。
         */
        private String createUser;

        /**
         * 创建时间。
         */
        private Date createTime;

        /**
         * 更新人。
         */
        private String updateUser;

        /**
         * 更新时间。
         */
        private Date updateTime;

        /**
         * 乐观锁版本控制字段。
         */
        private Integer revision;


}
