package com.mpolicy.settlement.core.modules.reconcile.dto.standardpremium;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class RenewalTermStandardPremiumDto implements Serializable {
    @ApiModelProperty("保单号")
    private String policyNo;
    @ApiModelProperty("合同编号")
    private String contractCode;
    @ApiModelProperty("续期期次")
    private Integer period;
    @ApiModelProperty("续期实收保费")
    private BigDecimal premium;
    @ApiModelProperty("续期实收标准保费")
    private BigDecimal standardPremium;
    @ApiModelProperty("续期险种明细")
    private List<PolicyProductInsuredStandardPremiumDto> productInsuredMapList;
}
