package com.mpolicy.settlement.core.modules.autocost.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Hr发放人员同步配置
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "settlement.hr-grant-person.sync")
public class HrGrantPersonSyncConfig {

    /**
     * 批量处理大小
     */
    private int batchSize = 1000;

    /**
     * 查询超时时间（秒）
     */
    private int queryTimeoutSeconds = 300;

    /**
     * 是否启用性能监控
     */
    private boolean enablePerformanceMonitor = true;

    /**
     * 最大重试次数
     */
    private int maxRetryCount = 3;

    /**
     * 重试间隔（毫秒）
     */
    private long retryIntervalMs = 1000;

    /**
     * 是否启用数据校验
     */
    private boolean enableDataValidation = true;

    /**
     * 默认Hr发放标志位
     */
    private int defaultHrGrantFlag = 1;

    /**
     * 同步备注信息
     */
    private String syncRemark = "系统自动同步";
}
