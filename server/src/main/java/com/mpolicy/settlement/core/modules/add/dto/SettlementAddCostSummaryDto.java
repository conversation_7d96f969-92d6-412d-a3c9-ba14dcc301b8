package com.mpolicy.settlement.core.modules.add.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SettlementAddCostSummaryDto implements Serializable {
    /**
     * 总条数
     */
    @ApiModelProperty("总条数")
    private int totalQty;

    /**
     * 单数
     */
    @ApiModelProperty("单数")
    private int policyQty;
    /**
     * 总保费
     */
    @ApiModelProperty("保费")
    private BigDecimal totalPremium;

    /**
     * 折算总保费
     */
    @ApiModelProperty("折算保费")
    private BigDecimal discountTotalPremium;

    /**
     * 总佣金
     */
    @ApiModelProperty("佣金")
    private BigDecimal settlementAmount;
    /**
     * 总佣金（排除整村推荐和分销单）
     */
    @ApiModelProperty("佣金（排除整村推荐和分销单）")
    private BigDecimal cmsAmount;
}
