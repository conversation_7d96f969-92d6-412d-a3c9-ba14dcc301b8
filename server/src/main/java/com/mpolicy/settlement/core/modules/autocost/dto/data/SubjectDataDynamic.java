package com.mpolicy.settlement.core.modules.autocost.dto.data;

import com.mpolicy.settlement.core.modules.autocost.enums.DynamicSubjectDataRuleEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 科目数据范围数据动态科目
 *
 * <AUTHOR>
 * @since 2023-11-28 21:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "科目数据范围数据动态科目", description = "科目数据范围数据动态科目")
public class SubjectDataDynamic extends SubjectDataBase implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 动态科目编号
     */
    @ApiModelProperty(value = "动态科目编号", example = "KMH")
    private String dynamicSubjectCode;

    /**
     * 动态科目名称
     */
    @ApiModelProperty(value = "动态科目名称", example = "开门红")
    private String dynamicSubjectName;

    /**
     * 动态科目分摊规则
     */
    @ApiModelProperty(value = "动态科目分摊规则", example = "AUTO_PERSONAL_PRODUCT")
    private DynamicSubjectDataRuleEnum dynamicSubjectDataRuleEnum;

    /**
     * 动态科目发放对象集合
     */
    @ApiModelProperty(value = "动态科目发放对象集合")
    private List<SubjectDataDynamicInfo> dynamicInfoList;
}