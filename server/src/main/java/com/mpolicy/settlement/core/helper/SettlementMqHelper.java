package com.mpolicy.settlement.core.helper;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.mpolicy.common.mq.MQMessage;
import com.mpolicy.common.mq.RabbitMQService;
import com.mpolicy.settlement.core.enums.QueueEnum;
import com.mpolicy.settlement.core.enums.SettlementProtocolEventEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <p>
 * 消息发送Helper
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/11
 */
@Component
@Slf4j(topic = "settlementCoreMq")
public class SettlementMqHelper {

    public static SettlementMqHelper settlementMqHelper;

    @Autowired
    RabbitMQService rabbitMQService;

    @PostConstruct
    public void init() {
        settlementMqHelper = this;
        // 消息服务
        settlementMqHelper.rabbitMQService = this.rabbitMQService;
    }

    /**
     * 消息发送
     *
     * @param policyCode: 保单号
     * @param queueEnum:  队列枚举
     * @param data:       消息内容
     * <AUTHOR>
     * @since 2022/5/21
     */
    public static void sendPolicyCoreMq(String policyCode, QueueEnum queueEnum, JSONObject data) {
        MQMessage message = new MQMessage();
        message.setCode(policyCode);
        message.setOpeType(queueEnum.getRouteKey());
        message.setData(data);
        settlementMqHelper.rabbitMQService.sendTopicMessage(queueEnum.getExchange(), queueEnum.getRouteKey(), message);
        log.info("消息发送MQ完成，单号={},消息类型={},消息内容={}", policyCode, JSON.toJSONString(queueEnum), data);
    }

    /**
     * 消息发送
     *
     * @param businessCode: 业务单号
     * @param queueEnum:  队列枚举
     * @param data:       消息内容
     * <AUTHOR>
     * @since 2022/5/21
     */
    public static void sendInvoiceDingtalkMsg(String businessCode, QueueEnum queueEnum, JSONObject data) {
        MQMessage message = new MQMessage();
        message.setCode(businessCode);
        message.setOpeType(queueEnum.getRouteKey());
        message.setData(data);
        settlementMqHelper.rabbitMQService.sendTopicMessage(queueEnum.getExchange(), queueEnum.getRouteKey(), message);
        log.info("消息发送MQ完成，单号={},消息类型={},消息内容={}", businessCode, JSON.toJSONString(queueEnum), data);
    }


}
