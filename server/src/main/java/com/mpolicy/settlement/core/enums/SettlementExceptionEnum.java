package com.mpolicy.settlement.core.enums;

import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.common.result.ICodeMsg;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 *
 * <p>异常枚举类</p>
 *
 * 通用开头分为：
 * 险种信息：PRODUCT_
 * 佣金配置：CONFIG_
 *
 * 事件计算异常
 * 支出端以字母C_开头
 * 支出端-基础佣金计算:C_B_
 * 支出端-自动结算:C_A_
 * <AUTHOR>
 * @since 2023/10/31
 */
@Getter
public enum SettlementExceptionEnum {

    /**通用，基础佣金从0-99开始*/
    CONFIG_COST_CONFIG_INFO_NOT_EXIST(1,"支出端-获取基础佣金配置, 基础佣金配置信息不存在"),
    CONFIG_SETTLEMENT_COMPANY_NOT_EXIST(2,"支出端-获取基础佣金配置, 佣金配置的结算机构为空"),
    CONFIG_SETTLEMENT_COMPANY_EXIST_REPEAT(3,"支出端-获取基础佣金配置, 结算机构编码存在重复配置"),
    CONFIG_INFO_EXCEPTION(4,"支出端-获取基础佣金配置, 基础佣金配置信息数据异常"),
    CONFIG_A_SINGLE_PROPOSAL_NOT_EXIST(5,"支出端-计算基础佣金,一单一议保单佣金费率信息未找到"),
    CONFIG_A_SINGLE_PROPOSAL_EXCEPTION(6,"支出端-计算基础佣金,一单一议保单佣金费率数据异常(如：多个费率、费率范围不为0-1等)"),
    CONFIG_VEHICLE_VESSEL_EXCEPTION(7,"支出端-计算基础佣金,车船税数据异常"),

    CONFIG_VEHICLE_VESSEL_NOT_HAVE_COMPULSORY(8,"支出端-基础佣金-计算基础佣金,车船税数据异常"),

    CORRECTION_AFTER_CUSTOMER_MANAGER_EXIST(9,"支出端-基础佣金-初始推荐人变更，变更后的推荐人记录已存在"),

    PARAM_POLICY_NOT_CONTAIN_MAIN_PRODUCT(10,"参数验证-保单缺少主险信息"),

    PRODUCT_RETURN_EXCEPTION(11,"产品服务调用异常"),

    CONFIG_PARAM_PRODUCT_NOT_EXIST(12,"获取费率配置-参数验证-入参缺少险种编码"),

    CONFIG_PARAM_SALES_TYPE_NOT_EXIST(13,"获取费率配置-参数验证-入参缺少销售类型"),

    CONFIG_PARAM_APPROVED_TIME_NOT_EXIST(14,"获取费率配置-参数验证-入参缺少承保时间"),

    CONFIG_PARAM_INSURED_TYPE_IS_ERROR(15,"获取费率配置-参数验证-入参保障期间错误"),

    CONFIG_PARAM_PAYMENT_PERIOD_TYPE_IS_ERROR(16,"获取费率配置-参数验证-入参缴费期间类型错误"),

    CONFIG_PARAM_PAYMENT_TYPE_IS_ERROR(17,"获取费率配置-参数验证-入参缴费方式错误"),

    CONFIG_PARAM_PRODUCT_NOT_MATCH(18,"获取费率配置-参数匹配-险种编码未匹配到费率"),

    CONFIG_PARAM_COST_TYPE_NOT_MATCH(19,"获取费率配置-参数匹配-费用类型未匹配到费率"),

    CONFIG_PARAM_ORG_CODE_NOT_MATCH(40,"获取费率配置-参数匹配-分公司编码未匹配到费率"),

    CONFIG_PARAM_TIME_LIST_NOT_MATCH(41,"获取费率配置-参数匹配-时间区间未匹配到费率"),

    /**支出端：基础佣金的code 从20-39*/
    /**
     * 对应异常码为：50020-50039
     */
    C_B_LONG_SURROUND_OLD_NOT_EXIST(20,"支出端-基础佣金-退保事件-历史佣金记录不存在"),
    C_B_BEFORE_EXIST_UN_SUCCESS_COST_EVENT(21,"支出端-基础佣金-该记录前存在未处理成功的记录"),

    C_B_SURRENDER_DETAIL_NOT_EXIST(22,"支出端-基础佣金-退保明细记录不存在"),



    C_B_GROUP_INSURED_DETAIL_NOT_EXIST(23,"支出端-基础佣金-团险-该保单缺少被保人明细"),

    C_B_SURRENDER_DETAIL_NOT_EQUALS_OLD(24,"支出端-基础佣金-根据历史记录计算出的退保记录数与历史记录数不一致"),

    C_B_POLICY_REFUND_NOT_OLD_RECORD(25,"支出端-基础佣金-保单退费保全找不到原新契约记录"),

    C_B_POLICY_REPAY_ONLY_SUPPORT_PERSONAL(26,"支出端-基础佣金-保单退费后续缴只支持个险"),

    C_B_PRODUCT_CHANGE_ONLY_SUPPORT_NEW_EVENT(27,"支出端-基础佣金-险种变更保全当前只支持新契约险种变更"),

    C_B_CHANNEL_CHANGE_OLD_RECORD_NOT_EXIST(28,"支出端-基础佣金-渠道变更保全未找到新老渠道相关的未冲正记录"),

    C_B_CHANNEL_CHANGE_ONLY_SUPPORT_NEW_EVENT(29,"支出端-基础佣金-渠道变更保全当前只支持新契约险种变更"),


    /**支出端：自动结算的code 从50-69*/

    /**收入端， 从70-99开始*/

    ;
    private Integer code;
    private String msg;
    private Integer errorCode;

    SettlementExceptionEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
        this.errorCode = BasicCodeMsg.SERVER_ERROR.setCustom(this.getCode(),this.getMsg()).getCode();
    }

    /**
     * 获取具体的描述
     * @param params
     * @return
     */
    public  ICodeMsg getException(String... params){
        return BasicCodeMsg.SERVER_ERROR.setCustom(this.getCode(),params[0]);
    }

    public static SettlementExceptionEnum decode(String code) {
        return Arrays.stream(SettlementExceptionEnum.values())
                .filter(x -> x.code.equals(code))
                .findFirst().orElse(null);
    }

    public static SettlementExceptionEnum decodeErrorCode(String errorCode) {
        return Arrays.stream(SettlementExceptionEnum.values())
                .filter(x -> x.errorCode.equals(errorCode))
                .findFirst().orElse(null);
    }
}
