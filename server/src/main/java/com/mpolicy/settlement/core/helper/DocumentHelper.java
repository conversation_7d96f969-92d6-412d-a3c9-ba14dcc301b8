package com.mpolicy.settlement.core.helper;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mpolicy.common.exception.GlobalException;
import com.mpolicy.common.result.BasicCodeMsg;
import com.mpolicy.settlement.core.entity.SysDocumentEntity;
import com.mpolicy.settlement.core.service.SysDocumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Optional;

/**
 * 文档附件处理
 *
 * <AUTHOR>
 * @since  2022-10-11 13:12
 */
@Component
public class DocumentHelper {

    public static DocumentHelper documentHelper;

    /**
     * documentService
     */
    @Autowired
    SysDocumentService sysDocumentService;

    @PostConstruct
    public void init() {
        documentHelper = this;
        documentHelper.sysDocumentService = this.sysDocumentService;
    }

    /**
     * <p>
     * 根据fileCode获取文件
     * </p>
     *
     * @param fileCode 文件编码
     * @return com.mpolicy.policy.core.entity.SysDocumentEntity
     * <AUTHOR>
     * @since 2021/12/9
     */
    public static SysDocumentEntity getDocumentInfo(String fileCode){
        // 获取不到直接抛异常
        return Optional.ofNullable(
                documentHelper.sysDocumentService.lambdaQuery()
                        .eq(SysDocumentEntity::getFileCode, fileCode)
                        .one()
        ).orElseThrow(() -> new GlobalException(BasicCodeMsg.SERVER_ERROR.setMsg(StrUtil.format("文档编码=[{}]文档记录信息不存在", fileCode))));
    }

    /**
     * <p>
     * 根据文件编码集合获取文件列表
     * </p>
     *
     * @param fileCodes 文件编码集合
     * @return java.util.List<com.mpolicy.policy.core.entity.SysDocumentEntity>
     * <AUTHOR>
     * @since 2021/12/9
     */
    public static List<SysDocumentEntity> getDocumentList(List<String> fileCodes){
        return documentHelper.sysDocumentService.list(new LambdaQueryWrapper<SysDocumentEntity>().in(SysDocumentEntity::getFileCode, fileCodes));
    }
}
