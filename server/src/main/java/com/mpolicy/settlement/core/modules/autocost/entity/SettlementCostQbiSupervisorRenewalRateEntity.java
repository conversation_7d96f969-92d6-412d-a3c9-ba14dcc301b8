package com.mpolicy.settlement.core.modules.autocost.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

/**
 * 保险结算督导粒度R13表
 * 
 * <AUTHOR>
 * @date 2023-12-16 15:39:35
 */
@TableName("settlement_cost_qbi_supervisor_renewal_rate")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementCostQbiSupervisorRenewalRateEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private Integer id;
	/**
	 * 督导员工编号
	 */
	private String employeeCode;
	/**
	 * 督导员工名称
	 */
	private String employeeName;
	/**
	 * 结算月份
	 */
	private String bizMonth;
	/**
	 * R13继续率
	 */
	private BigDecimal renewalRate;

	/**
	 * 应缴金额
	 */
	private BigDecimal payAmt;
	/**
	 * 实缴金额
	 */
	private BigDecimal actPayAmt;
	/**
	 * 快照分区
	 */
	private Integer pt;
	/**
	 * 是否删除;0有效-1删除
	 */
	@TableLogic
	private Integer deleted;
	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	private String createUser;
	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;
	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private String updateUser;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
	/**
	 * 乐观锁
	 */
	@Version
	@TableField(fill = FieldFill.INSERT)
	private Integer revision;
}
